# 技术栈说明

## 开发语言

**Kotlin** - 作为主要开发语言，用于Android应用开发

## 项目架构类型

这是一个**纯Android客户端应用**，不涉及传统意义上的前后端分离架构。

### 客户端（Android App）

#### 主要目录结构
- **app/** - 主应用模块（入口点、依赖注入、导航）
- **feature/** - 功能模块（各个业务功能的UI和逻辑）
- **core/** - 核心共享模块（通用组件、网络、数据库、UI组件）
- **domain/** - 领域层（业务模型、用例、仓库接口）
- **data/** - 数据层（仓库实现、数据源、API集成）

#### 核心技术栈

##### UI层技术
- **Jetpack Compose** - 现代化声明式UI框架
- **Material Design 3** - Google最新设计规范
- **Navigation Component** - 页面导航管理
- **Compose Animation** - UI动画效果

##### 架构模式
- **MVVM (Model-View-ViewModel)** - 主要架构模式
- **Clean Architecture** - 分层架构原则
- **Repository Pattern** - 数据访问抽象
- **Use Case Pattern** - 业务逻辑封装

##### 依赖注入
- **Hilt** - Google推荐的依赖注入框架
- **Dagger** - Hilt的底层实现

##### 数据持久化
- **Room** - SQLite数据库ORM框架
- **SharedPreferences** - 轻量级配置存储
- **DataStore** - 现代化的配置存储方案

##### 网络通信
- **Retrofit** - REST API客户端
- **OkHttp** - HTTP客户端库
- **Kotlinx Serialization** - JSON序列化/反序列化

##### 异步处理
- **Kotlin Coroutines** - 协程异步编程
- **Flow** - 响应式数据流
- **StateFlow/SharedFlow** - 状态管理

##### 图片处理
- **Coil** - Kotlin优先的图片加载库
- **Compose Integration** - 与Compose的无缝集成

##### 测试框架
- **JUnit 5** - 单元测试框架
- **MockK** - Kotlin模拟框架
- **Compose Testing** - UI测试框架
- **Room Testing** - 数据库测试工具

##### 构建工具
- **Gradle Kotlin DSL** - 构建脚本
- **Version Catalogs** - 依赖版本管理
- **BuildSrc** - 构建逻辑模块化

## 外部服务集成

### AI翻译服务
- **Google Gemini API** - 主要的AI翻译服务
- **自定义提示词生成器** - 地方特色翻译优化

### 语音服务
- **Android Speech API** - 语音识别
- **Text-to-Speech (TTS)** - 语音合成
- **AudioManager** - 音频管理

### 视觉识别
- **ML Kit Text Recognition** - OCR文字识别
- **CameraX** - 相机功能集成

### 离线功能
- **本地ML模型** - 离线翻译支持
- **TensorFlow Lite** - 轻量级机器学习
- **语言包管理** - 离线数据下载

## 数据流向架构

```
UI Layer (Compose)
    ↓
ViewModel (State Management)
    ↓
Use Cases (Business Logic)
    ↓
Repository (Data Abstraction)
    ↓
Data Sources (Local/Remote)
    ↓
External APIs / Local Database
```

## 安全特性

### 数据加密
- **Android Keystore** - 密钥管理
- **AES加密** - 敏感数据保护
- **Certificate Pinning** - 网络安全

### 隐私保护
- **数据匿名化** - 敏感信息处理
- **本地优先存储** - 减少数据传输
- **用户权限管理** - 最小权限原则

## 性能优化

### 缓存策略
- **多级缓存** - 内存+磁盘缓存
- **LRU算法** - 缓存淘汰策略
- **智能预加载** - 提升用户体验

### 资源优化
- **WebP图片格式** - 减少包体积
- **矢量图标** - 适配多分辨率
- **资源压缩** - 构建时优化

### 内存管理
- **弱引用** - 避免内存泄漏
- **对象池** - 减少GC压力
- **懒加载** - 按需初始化

## 国际化支持

### 多语言资源
- **中文** (zh) - 简体中文
- **英文** (en) - 英语
- **日文** (ja) - 日语
- **韩文** (ko) - 韩语

### 本地化特性
- **RTL支持** - 从右到左语言
- **日期时间格式** - 地区化显示
- **数字格式** - 本地化数字显示