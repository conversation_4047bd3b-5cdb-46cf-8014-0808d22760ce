package com.cadence.core.offline

import android.content.Context
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.ResponseBody
import timber.log.Timber
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.security.MessageDigest
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 模型下载服务
 * 负责从远程服务器下载离线翻译模型和词典
 */
@Singleton
class ModelDownloadService @Inject constructor(
    private val context: Context,
    private val modelManager: ModelManager,
    private val httpClient: OkHttpClient
) {
    
    companion object {
        // 模型下载URL配置
        private const val BASE_DOWNLOAD_URL = "https://api.cadence.com/models"
        private const val MODEL_FILE_URL = "$BASE_DOWNLOAD_URL/translation_model.tflite"
        private const val VOCAB_FILE_URL = "$BASE_DOWNLOAD_URL/vocab.txt"
        private const val DICTIONARY_FILE_URL = "$BASE_DOWNLOAD_URL/dictionary.json"
        
        // 文件校验和（实际部署时需要真实值）
        private const val MODEL_CHECKSUM = "placeholder_model_checksum"
        private const val VOCAB_CHECKSUM = "placeholder_vocab_checksum"
        private const val DICTIONARY_CHECKSUM = "placeholder_dictionary_checksum"
        
        // 下载配置
        private const val DOWNLOAD_TIMEOUT_MS = 300000L // 5分钟
        private const val CHUNK_SIZE = 8192 // 8KB
    }
    
    private val _downloadState = MutableStateFlow<DownloadState>(DownloadState.Idle)
    val downloadState: StateFlow<DownloadState> = _downloadState.asStateFlow()
    
    private val _downloadProgress = MutableStateFlow<DownloadProgress?>(null)
    val downloadProgress: StateFlow<DownloadProgress?> = _downloadProgress.asStateFlow()
    
    /**
     * 开始下载模型
     */
    suspend fun downloadModel(): DownloadResult = withContext(Dispatchers.IO) {
        try {
            if (_downloadState.value is DownloadState.Downloading) {
                return@withContext DownloadResult.Error("下载已在进行中")
            }
            
            _downloadState.value = DownloadState.Downloading
            _downloadProgress.value = DownloadProgress(
                currentFile = "准备下载...",
                fileProgress = 0f,
                overallProgress = 0f,
                downloadedBytes = 0L,
                totalBytes = 0L,
                speed = 0L
            )
            
            Timber.d("开始下载离线翻译模型")
            
            // 创建临时下载目录
            val tempDir = File(context.cacheDir, "model_download_temp")
            if (!tempDir.exists()) {
                tempDir.mkdirs()
            }
            
            val downloadTasks = listOf(
                DownloadTask("translation_model.tflite", MODEL_FILE_URL, MODEL_CHECKSUM),
                DownloadTask("vocab.txt", VOCAB_FILE_URL, VOCAB_CHECKSUM),
                DownloadTask("dictionary.json", DICTIONARY_FILE_URL, DICTIONARY_CHECKSUM)
            )
            
            val downloadedFiles = mutableMapOf<String, ByteArray>()
            var overallProgress = 0f
            
            // 逐个下载文件
            for ((index, task) in downloadTasks.withIndex()) {
                _downloadProgress.value = _downloadProgress.value?.copy(
                    currentFile = "下载 ${task.fileName}...",
                    overallProgress = overallProgress
                )
                
                val fileData = downloadFile(task, tempDir) { progress ->
                    val fileProgress = progress
                    overallProgress = (index + fileProgress) / downloadTasks.size
                    
                    _downloadProgress.value = _downloadProgress.value?.copy(
                        fileProgress = fileProgress,
                        overallProgress = overallProgress
                    )
                }
                
                if (fileData != null) {
                    downloadedFiles[task.fileName] = fileData
                    Timber.d("文件下载成功: ${task.fileName}")
                } else {
                    _downloadState.value = DownloadState.Failed("下载文件失败: ${task.fileName}")
                    return@withContext DownloadResult.Error("下载文件失败: ${task.fileName}")
                }
            }
            
            // 验证和保存模型
            _downloadProgress.value = _downloadProgress.value?.copy(
                currentFile = "验证和安装模型...",
                overallProgress = 0.9f
            )
            
            val modelData = downloadedFiles["translation_model.tflite"]
            val vocabData = downloadedFiles["vocab.txt"]
            val dictionaryData = downloadedFiles["dictionary.json"]
            
            if (modelData != null && vocabData != null) {
                val saveSuccess = modelManager.saveModel(modelData, vocabData)
                if (saveSuccess) {
                    // 导入词典数据（如果有）
                    if (dictionaryData != null) {
                        importDictionaryData(dictionaryData)
                    }
                    
                    _downloadProgress.value = _downloadProgress.value?.copy(
                        currentFile = "下载完成",
                        overallProgress = 1.0f
                    )
                    
                    _downloadState.value = DownloadState.Completed
                    
                    // 清理临时文件
                    tempDir.deleteRecursively()
                    
                    Timber.d("模型下载和安装完成")
                    DownloadResult.Success
                } else {
                    _downloadState.value = DownloadState.Failed("模型保存失败")
                    DownloadResult.Error("模型保存失败")
                }
            } else {
                _downloadState.value = DownloadState.Failed("关键文件缺失")
                DownloadResult.Error("关键文件缺失")
            }
            
        } catch (e: Exception) {
            Timber.e(e, "模型下载过程中发生异常")
            _downloadState.value = DownloadState.Failed("下载异常: ${e.message}")
            DownloadResult.Error("下载异常: ${e.message}")
        }
    }
    
    /**
     * 取消下载
     */
    suspend fun cancelDownload(): Boolean = withContext(Dispatchers.IO) {
        try {
            if (_downloadState.value is DownloadState.Downloading) {
                _downloadState.value = DownloadState.Cancelled
                _downloadProgress.value = null
                
                // 清理临时文件
                val tempDir = File(context.cacheDir, "model_download_temp")
                if (tempDir.exists()) {
                    tempDir.deleteRecursively()
                }
                
                Timber.d("模型下载已取消")
                true
            } else {
                false
            }
        } catch (e: Exception) {
            Timber.e(e, "取消下载失败")
            false
        }
    }
    
    /**
     * 检查模型更新
     */
    suspend fun checkForUpdates(): UpdateCheckResult = withContext(Dispatchers.IO) {
        try {
            // TODO: 实现版本检查逻辑
            // 这里应该调用服务器API检查最新版本
            
            val currentModelInfo = modelManager.getModelInfo()
            val latestVersion = "1.1.0" // 模拟最新版本
            
            if (currentModelInfo == null) {
                UpdateCheckResult.UpdateAvailable(
                    currentVersion = "无",
                    latestVersion = latestVersion,
                    updateSize = 50 * 1024 * 1024L, // 50MB
                    isRequired = false
                )
            } else if (currentModelInfo.version != latestVersion) {
                UpdateCheckResult.UpdateAvailable(
                    currentVersion = currentModelInfo.version,
                    latestVersion = latestVersion,
                    updateSize = 30 * 1024 * 1024L, // 30MB增量更新
                    isRequired = false
                )
            } else {
                UpdateCheckResult.UpToDate(currentModelInfo.version)
            }
        } catch (e: Exception) {
            Timber.e(e, "检查更新失败")
            UpdateCheckResult.Error("检查更新失败: ${e.message}")
        }
    }
    
    /**
     * 下载单个文件
     */
    private suspend fun downloadFile(
        task: DownloadTask,
        tempDir: File,
        onProgress: (Float) -> Unit
    ): ByteArray? = withContext(Dispatchers.IO) {
        try {
            val request = Request.Builder()
                .url(task.url)
                .build()
            
            val response = httpClient.newCall(request).execute()
            
            if (!response.isSuccessful) {
                Timber.e("下载失败: ${response.code} ${response.message}")
                return@withContext null
            }
            
            val responseBody = response.body ?: return@withContext null
            val contentLength = responseBody.contentLength()
            
            val inputStream = responseBody.byteStream()
            val tempFile = File(tempDir, task.fileName)
            val outputStream = FileOutputStream(tempFile)
            
            val buffer = ByteArray(CHUNK_SIZE)
            var downloadedBytes = 0L
            var bytesRead: Int
            
            while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                outputStream.write(buffer, 0, bytesRead)
                downloadedBytes += bytesRead
                
                if (contentLength > 0) {
                    val progress = downloadedBytes.toFloat() / contentLength.toFloat()
                    onProgress(progress)
                }
            }
            
            outputStream.close()
            inputStream.close()
            
            // 验证文件完整性
            val fileData = tempFile.readBytes()
            val checksum = calculateChecksum(fileData)
            
            if (checksum == task.expectedChecksum) {
                Timber.d("文件校验成功: ${task.fileName}")
                fileData
            } else {
                Timber.e("文件校验失败: ${task.fileName}, 期望: ${task.expectedChecksum}, 实际: $checksum")
                null
            }
            
        } catch (e: Exception) {
            Timber.e(e, "下载文件失败: ${task.fileName}")
            null
        }
    }
    
    /**
     * 导入词典数据
     */
    private suspend fun importDictionaryData(dictionaryData: ByteArray) {
        try {
            // TODO: 解析JSON格式的词典数据并导入到数据库
            // 这里应该解析JSON并调用OfflineDictionaryService.importDictionaryEntries
            Timber.d("词典数据导入功能待实现")
        } catch (e: Exception) {
            Timber.e(e, "导入词典数据失败")
        }
    }
    
    /**
     * 计算文件校验和
     */
    private fun calculateChecksum(data: ByteArray): String {
        return try {
            val digest = MessageDigest.getInstance("SHA-256")
            val hashBytes = digest.digest(data)
            hashBytes.joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            Timber.e(e, "计算校验和失败")
            ""
        }
    }
}

/**
 * 下载任务数据类
 */
private data class DownloadTask(
    val fileName: String,
    val url: String,
    val expectedChecksum: String
)

/**
 * 下载状态封装类
 */
sealed class DownloadState {
    object Idle : DownloadState()
    object Downloading : DownloadState()
    object Completed : DownloadState()
    object Cancelled : DownloadState()
    data class Failed(val error: String) : DownloadState()
}

/**
 * 下载进度数据类
 */
data class DownloadProgress(
    val currentFile: String,
    val fileProgress: Float,
    val overallProgress: Float,
    val downloadedBytes: Long,
    val totalBytes: Long,
    val speed: Long // bytes per second
)

/**
 * 下载结果封装类
 */
sealed class DownloadResult {
    object Success : DownloadResult()
    data class Error(val message: String) : DownloadResult()
}

/**
 * 更新检查结果封装类
 */
sealed class UpdateCheckResult {
    data class UpdateAvailable(
        val currentVersion: String,
        val latestVersion: String,
        val updateSize: Long,
        val isRequired: Boolean
    ) : UpdateCheckResult()
    
    data class UpToDate(val version: String) : UpdateCheckResult()
    data class Error(val message: String) : UpdateCheckResult()
}
