package com.cadence.core.ocr

import android.graphics.Bitmap
import android.graphics.Rect
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.Text
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.chinese.ChineseTextRecognizerOptions
import com.google.mlkit.vision.text.devanagari.DevanagariTextRecognizerOptions
import com.google.mlkit.vision.text.japanese.JapaneseTextRecognizerOptions
import com.google.mlkit.vision.text.korean.KoreanTextRecognizerOptions
import com.google.mlkit.vision.text.latin.TextRecognizerOptions
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.suspendCancellableCoroutine
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume

/**
 * 文字识别服务
 * 基于Google ML Kit实现多语言文字识别功能
 */
@Singleton
class TextRecognitionService @Inject constructor() {
    
    /**
     * 文字识别结果
     */
    sealed class RecognitionResult {
        object Processing : RecognitionResult()
        data class Success(
            val recognizedText: RecognizedText
        ) : RecognitionResult()
        data class Error(
            val message: String,
            val errorCode: Int
        ) : RecognitionResult()
    }
    
    /**
     * 识别的文字信息
     */
    data class RecognizedText(
        val fullText: String,
        val textBlocks: List<TextBlock>,
        val confidence: Float,
        val processingTime: Long
    )
    
    /**
     * 文字块信息
     */
    data class TextBlock(
        val text: String,
        val boundingBox: Rect?,
        val confidence: Float,
        val lines: List<TextLine>
    )
    
    /**
     * 文字行信息
     */
    data class TextLine(
        val text: String,
        val boundingBox: Rect?,
        val confidence: Float,
        val elements: List<TextElement>
    )
    
    /**
     * 文字元素信息
     */
    data class TextElement(
        val text: String,
        val boundingBox: Rect?,
        val confidence: Float
    )
    
    /**
     * 支持的语言类型
     */
    enum class RecognitionLanguage(val code: String, val displayName: String) {
        LATIN("latin", "拉丁文字"),
        CHINESE("chinese", "中文"),
        DEVANAGARI("devanagari", "梵文"),
        JAPANESE("japanese", "日文"),
        KOREAN("korean", "韩文")
    }
    
    /**
     * 识别配置
     */
    data class RecognitionConfig(
        val language: RecognitionLanguage = RecognitionLanguage.LATIN,
        val enableTextBlocks: Boolean = true,
        val enableLines: Boolean = true,
        val enableElements: Boolean = true,
        val minConfidence: Float = 0.5f
    )
    
    // 当前识别状态
    private val _recognitionState = MutableStateFlow<RecognitionResult?>(null)
    val recognitionState: Flow<RecognitionResult?> = _recognitionState.asStateFlow()
    
    // 错误代码常量
    companion object {
        const val ERROR_INVALID_IMAGE = 1001
        const val ERROR_RECOGNITION_FAILED = 1002
        const val ERROR_NO_TEXT_FOUND = 1003
        const val ERROR_UNSUPPORTED_LANGUAGE = 1004
        const val ERROR_LOW_CONFIDENCE = 1005
        const val ERROR_PROCESSING_TIMEOUT = 1006
    }
    
    /**
     * 识别图片中的文字
     * @param bitmap 要识别的图片
     * @param config 识别配置
     * @return 识别结果
     */
    suspend fun recognizeText(
        bitmap: Bitmap,
        config: RecognitionConfig = RecognitionConfig()
    ): RecognitionResult {
        return try {
            _recognitionState.value = RecognitionResult.Processing
            
            val startTime = System.currentTimeMillis()
            
            // 创建输入图像
            val inputImage = InputImage.fromBitmap(bitmap, 0)
            
            // 获取对应语言的识别器
            val recognizer = when (config.language) {
                RecognitionLanguage.LATIN -> TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS)
                RecognitionLanguage.CHINESE -> TextRecognition.getClient(ChineseTextRecognizerOptions.Builder().build())
                RecognitionLanguage.DEVANAGARI -> TextRecognition.getClient(DevanagariTextRecognizerOptions.Builder().build())
                RecognitionLanguage.JAPANESE -> TextRecognition.getClient(JapaneseTextRecognizerOptions.Builder().build())
                RecognitionLanguage.KOREAN -> TextRecognition.getClient(KoreanTextRecognizerOptions.Builder().build())
            }
            
            // 执行文字识别
            val result = suspendCancellableCoroutine<Text> { continuation ->
                recognizer.process(inputImage)
                    .addOnSuccessListener { visionText ->
                        continuation.resume(visionText)
                    }
                    .addOnFailureListener { exception ->
                        continuation.cancel(exception)
                    }
            }
            
            val processingTime = System.currentTimeMillis() - startTime
            
            // 处理识别结果
            val recognizedText = processRecognitionResult(result, config, processingTime)
            
            val finalResult = if (recognizedText.fullText.isBlank()) {
                RecognitionResult.Error("未识别到文字内容", ERROR_NO_TEXT_FOUND)
            } else if (recognizedText.confidence < config.minConfidence) {
                RecognitionResult.Error("识别置信度过低: ${recognizedText.confidence}", ERROR_LOW_CONFIDENCE)
            } else {
                RecognitionResult.Success(recognizedText)
            }
            
            _recognitionState.value = finalResult
            finalResult
            
        } catch (e: Exception) {
            val errorResult = RecognitionResult.Error(
                "文字识别失败: ${e.message}",
                ERROR_RECOGNITION_FAILED
            )
            _recognitionState.value = errorResult
            errorResult
        }
    }
    
    /**
     * 处理ML Kit识别结果
     */
    private fun processRecognitionResult(
        visionText: Text,
        config: RecognitionConfig,
        processingTime: Long
    ): RecognizedText {
        val textBlocks = mutableListOf<TextBlock>()
        var totalConfidence = 0f
        var confidenceCount = 0
        
        for (block in visionText.textBlocks) {
            if (!config.enableTextBlocks) continue
            
            val lines = mutableListOf<TextLine>()
            
            for (line in block.lines) {
                if (!config.enableLines) continue
                
                val elements = mutableListOf<TextElement>()
                
                for (element in line.elements) {
                    if (!config.enableElements) continue
                    
                    elements.add(
                        TextElement(
                            text = element.text,
                            boundingBox = element.boundingBox,
                            confidence = element.confidence ?: 0f
                        )
                    )
                    
                    element.confidence?.let { conf ->
                        totalConfidence += conf
                        confidenceCount++
                    }
                }
                
                lines.add(
                    TextLine(
                        text = line.text,
                        boundingBox = line.boundingBox,
                        confidence = line.confidence ?: 0f,
                        elements = elements
                    )
                )
            }
            
            textBlocks.add(
                TextBlock(
                    text = block.text,
                    boundingBox = block.boundingBox,
                    confidence = block.confidence ?: 0f,
                    lines = lines
                )
            )
        }
        
        val averageConfidence = if (confidenceCount > 0) {
            totalConfidence / confidenceCount
        } else {
            0f
        }
        
        return RecognizedText(
            fullText = visionText.text,
            textBlocks = textBlocks,
            confidence = averageConfidence,
            processingTime = processingTime
        )
    }
    
    /**
     * 获取支持的语言列表
     */
    fun getSupportedLanguages(): List<RecognitionLanguage> {
        return RecognitionLanguage.values().toList()
    }
    
    /**
     * 根据文本内容推荐识别语言
     */
    fun recommendLanguage(text: String): RecognitionLanguage {
        return when {
            text.any { it in '\u4e00'..'\u9fff' } -> RecognitionLanguage.CHINESE
            text.any { it in '\u3040'..'\u309f' || it in '\u30a0'..'\u30ff' } -> RecognitionLanguage.JAPANESE
            text.any { it in '\uac00'..'\ud7af' } -> RecognitionLanguage.KOREAN
            text.any { it in '\u0900'..'\u097f' } -> RecognitionLanguage.DEVANAGARI
            else -> RecognitionLanguage.LATIN
        }
    }
    
    /**
     * 验证图片是否适合文字识别
     */
    fun validateImage(bitmap: Bitmap): Pair<Boolean, String?> {
        return when {
            bitmap.isRecycled -> false to "图片已被回收"
            bitmap.width < 50 || bitmap.height < 50 -> false to "图片尺寸过小"
            bitmap.width > 4096 || bitmap.height > 4096 -> false to "图片尺寸过大"
            else -> true to null
        }
    }
    
    /**
     * 清除识别状态
     */
    fun clearState() {
        _recognitionState.value = null
    }
}
