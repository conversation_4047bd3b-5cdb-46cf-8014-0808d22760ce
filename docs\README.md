# 地方特色语言翻译应用 - 项目文档

## 项目概述

本项目是一款具有地方特色语言翻译功能的Android应用程序。与市面上常见的翻译软件不同，该应用的核心功能是通过Gemini API提供带有不同地方风格和特色的翻译结果，让用户能够体验到各地语言的独特魅力。

## 核心特色

### 🌍 地方特色翻译
- **中文方言**: 北京话、上海话、广东话、台湾腔、东北话、四川话、湖南话、河南话等
- **英语变体**: 美式（纽约、洛杉矶、旧金山、德州）、英式（伦敦、苏格兰、威尔士）、澳式、加拿大式、印式英语等
- **日语方言**: 关西腔、东京腔、九州腔等
- **韩语方言**: 首尔标准语、釜山方言、济州岛方言等

### 🎯 差异化功能
- **方言互译**: 支持不同方言之间的相互翻译
- **文化背景解释**: 提供翻译内容的文化背景和历史典故
- **语言学习模式**: 系统化的地方语言学习功能
- **智能语境识别**: 根据使用场景提供合适的翻译风格

### 🎨 现代化设计
- **Material Design 3**: 采用Google最新设计规范
- **响应式布局**: 适配不同屏幕尺寸
- **深色/浅色主题**: 支持主题切换
- **流畅动画**: 自然的交互动效

## 技术架构

### 开发语言
- **Kotlin** - 主要开发语言

### 架构模式
- **MVVM + Clean Architecture** - 分层架构设计
- **模块化架构** - 功能模块独立开发

### 核心技术栈
- **UI**: Jetpack Compose + Material Design 3
- **依赖注入**: Hilt
- **数据库**: Room
- **网络**: Retrofit + OkHttp
- **异步**: Kotlin Coroutines + Flow
- **AI服务**: Google Gemini API

## 项目结构

```
Cadence/
├── app/                    # 主应用模块
├── core/                   # 核心共享模块
├── feature/                # 功能模块
├── data/                   # 数据层
├── domain/                 # 领域层
├── buildSrc/              # 构建配置
├── docs/                  # 项目文档
└── scripts/               # 构建脚本
```

## 主要功能模块

### 1. 翻译功能 (feature/translation)
- 主翻译界面
- 批量翻译
- 方言互译

### 2. 历史记录 (feature/history)
- 翻译历史管理
- 搜索和筛选
- 历史详情查看

### 3. 收藏功能 (feature/favorites)
- 收藏翻译结果
- 标签管理
- 收藏夹组织

### 4. 设置功能 (feature/settings)
- 用户偏好设置
- 主题和语言设置
- 隐私和安全设置

### 5. 学习功能 (feature/learning)
- 语言学习模式
- 学习进度跟踪
- 练习和测试

### 6. 文化功能 (feature/cultural)
- 文化背景解释
- 文化知识库
- 历史典故展示

## 文档结构

### 📁 architecture/
- [目录结构设计](architecture/directory-structure.md) - 详细的项目目录结构说明

### 📁 technical/
- [技术栈说明](technical/tech-stack.md) - 完整的技术栈介绍

### 📁 api/
- API接口文档 (待创建)
- 数据模型定义 (待创建)

### 📁 design/
- UI设计规范 (待创建)
- 用户体验指南 (待创建)

### 📁 user-guide/
- 用户使用手册 (待创建)
- 常见问题解答 (待创建)

## 开发环境要求

### 必需工具
- **Android Studio**: Arctic Fox 2020.3.1 或更高版本
- **JDK**: 11 或更高版本
- **Gradle**: 7.0 或更高版本
- **Kotlin**: 1.8.0 或更高版本

### 推荐配置
- **内存**: 8GB RAM 或更多
- **存储**: 至少 10GB 可用空间
- **网络**: 稳定的互联网连接（用于API调用）

## 快速开始

### 1. 克隆项目
```bash
git clone https://github.com/BasicProtein/Cadence.git
cd Cadence
```

### 2. 配置API密钥
```bash
# 在 local.properties 中添加
GEMINI_API_KEY=your_gemini_api_key_here
```

### 3. 构建项目
```bash
./gradlew build
```

### 4. 运行应用
```bash
./gradlew installDebug
```

## 贡献指南

### 代码规范
- 遵循 Kotlin 官方编码规范
- 使用 ktlint 进行代码格式化
- 编写单元测试和集成测试

### 提交规范
- 使用语义化提交信息
- 每个功能使用独立的分支开发
- 提交前运行完整的测试套件

### 分支策略
- `main`: 主分支，保持稳定
- `develop`: 开发分支，集成新功能
- `feature/*`: 功能分支
- `hotfix/*`: 紧急修复分支

## 许可证

本项目采用 [MIT License](LICENSE) 许可证。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目Issues: [GitHub Issues]
- 邮箱: [项目邮箱]
- 文档反馈: [文档反馈地址]

---

*最后更新: 2024年*