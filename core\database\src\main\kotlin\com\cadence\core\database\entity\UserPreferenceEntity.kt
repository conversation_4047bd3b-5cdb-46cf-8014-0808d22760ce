package com.cadence.core.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.serialization.Serializable

/**
 * 用户偏好设置数据实体
 * 存储用户的个性化设置
 */
@Entity(tableName = "user_preferences")
@Serializable
data class UserPreferenceEntity(
    @PrimaryKey
    @ColumnInfo(name = "id")
    val id: String = "default_user",
    
    @ColumnInfo(name = "default_source_language")
    val defaultSourceLanguage: String = "zh",
    
    @ColumnInfo(name = "default_target_language")
    val defaultTargetLanguage: String = "en",
    
    @ColumnInfo(name = "default_source_region")
    val defaultSourceRegion: String = "CN",
    
    @ColumnInfo(name = "default_target_region")
    val defaultTargetRegion: String = "US",
    
    @ColumnInfo(name = "auto_detect_language")
    val autoDetectLanguage: Boolean = true,
    
    @ColumnInfo(name = "save_translation_history")
    val saveTranslationHistory: Boolean = true,
    
    @ColumnInfo(name = "enable_cultural_context")
    val enableCulturalContext: Boolean = true,
    
    @ColumnInfo(name = "theme_mode")
    val themeMode: String = "system", // light, dark, system
    
    @ColumnInfo(name = "font_size")
    val fontSize: String = "medium", // small, medium, large
    
    @ColumnInfo(name = "enable_voice_input")
    val enableVoiceInput: Boolean = true,
    
    @ColumnInfo(name = "enable_voice_output")
    val enableVoiceOutput: Boolean = true,
    
    @ColumnInfo(name = "enable_ocr")
    val enableOcr: Boolean = true,
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long,
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long
)
