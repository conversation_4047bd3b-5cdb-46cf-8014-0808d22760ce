package com.cadence.cadence.ui.theme

import androidx.compose.ui.graphics.Color

/**
 * Cadence应用程序颜色定义
 * 
 * 基于Material Design 3颜色系统，
 * 为地方特色翻译应用定制的颜色方案
 */

// 主色调 - 蓝色系（代表沟通和理解）
val Primary40 = Color(0xFF1565C0)      // 主蓝色
val Primary80 = Color(0xFF90CAF9)      // 浅蓝色
val Primary90 = Color(0xFFE3F2FD)      // 极浅蓝色

// 次要色调 - 绿色系（代表成功和准确）
val Secondary40 = Color(0xFF2E7D32)    // 主绿色
val Secondary80 = Color(0xFFA5D6A7)    // 浅绿色
val Secondary90 = Color(0xFFE8F5E8)    // 极浅绿色

// 第三色调 - 橙色系（代表文化和温暖）
val Tertiary40 = Color(0xFFE65100)     // 主橙色
val Tertiary80 = Color(0xFFFFCC80)     // 浅橙色
val Tertiary90 = Color(0xFFFFF3E0)     // 极浅橙色

// 错误色调 - 红色系
val Error40 = Color(0xFFD32F2F)        // 主红色
val Error80 = Color(0xFFEF9A9A)        // 浅红色
val Error90 = Color(0xFFFFEBEE)        // 极浅红色

// 中性色调
val Neutral10 = Color(0xFF1C1B1F)      // 深色文本
val Neutral20 = Color(0xFF313033)      // 次深色
val Neutral90 = Color(0xFFF5F5F5)      // 浅色背景
val Neutral95 = Color(0xFFFAFAFA)      // 极浅色背景
val Neutral99 = Color(0xFFFFFBFE)      // 白色背景

// 中性变体色调
val NeutralVariant30 = Color(0xFF49454F) // 深色边框
val NeutralVariant50 = Color(0xFF79747E) // 中性边框
val NeutralVariant80 = Color(0xFFCAC4D0) // 浅色边框
val NeutralVariant90 = Color(0xFFE7E0EC) // 极浅色边框

// 特殊功能色彩
val SuccessGreen = Color(0xFF4CAF50)    // 成功状态
val WarningAmber = Color(0xFFFF9800)    // 警告状态
val InfoBlue = Color(0xFF2196F3)        // 信息状态

// 地方特色色彩（用于不同地区的视觉标识）
val BeijingRed = Color(0xFFD32F2F)      // 北京红
val ShanghaiBlue = Color(0xFF1976D2)    // 上海蓝
val GuangzhouGold = Color(0xFFFFB300)   // 广州金
val TaiwanGreen = Color(0xFF388E3C)     // 台湾绿

// 语言标识色彩
val ChineseRed = Color(0xFFE53935)      // 中文红
val EnglishBlue = Color(0xFF1E88E5)     // 英文蓝
val JapaneseOrange = Color(0xFFFF7043)  // 日文橙
val KoreanPurple = Color(0xFF8E24AA)    // 韩文紫