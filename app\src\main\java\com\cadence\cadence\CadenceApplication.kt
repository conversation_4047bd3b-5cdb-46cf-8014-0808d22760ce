package com.cadence.cadence

import android.app.Application
import dagger.hilt.android.HiltAndroidApp
import timber.log.Timber

/**
 * Cadence应用程序主类
 * 
 * 负责应用程序的全局初始化，包括：
 * - Hilt依赖注入初始化
 * - Timber日志系统配置
 * - 全局异常处理
 * - 性能监控初始化
 */
@HiltAndroidApp
class CadenceApplication : Application() {
    
    override fun onCreate() {
        super.onCreate()
        
        // 初始化日志系统
        initializeLogging()
        
        // 初始化性能监控
        initializePerformanceMonitoring()
        
        Timber.i("Cadence应用程序启动完成")
    }
    
    /**
     * 初始化日志系统
     */
    private fun initializeLogging() {
        if (BuildConfig.DEBUG) {
            // Debug模式下使用详细日志
            Timber.plant(object : Timber.DebugTree() {
                override fun createStackElementTag(element: StackTraceElement): String {
                    return "Cadence_${super.createStackElementTag(element)}:${element.lineNumber}"
                }
            })
        } else {
            // Release模式下使用简化日志（可以集成Crashlytics等）
            Timber.plant(ReleaseTree())
        }
    }
    
    /**
     * 初始化性能监控
     */
    private fun initializePerformanceMonitoring() {
        // TODO: 集成性能监控工具（如Firebase Performance）
        Timber.d("性能监控初始化完成")
    }
    
    /**
     * Release模式下的日志树
     * 只记录警告和错误级别的日志
     */
    private class ReleaseTree : Timber.Tree() {
        override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
            if (priority >= android.util.Log.WARN) {
                // 在生产环境中，可以将日志发送到远程服务器
                // 例如：Crashlytics.log(message)
            }
        }
    }
}