package com.cadence.core.offline

import com.cadence.core.common.UserPreferenceManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 翻译模式管理器
 * 负责在线和离线翻译模式的智能切换
 */
@Singleton
class TranslationModeManager @Inject constructor(
    private val networkStateMonitor: NetworkStateMonitor,
    private val offlineTranslationService: OfflineTranslationService,
    private val userPreferenceManager: UserPreferenceManager
) {
    
    /**
     * 当前翻译模式流
     * 基于网络状态、用户偏好和离线服务可用性自动决定
     */
    val currentTranslationMode: Flow<TranslationMode> = combine(
        networkStateMonitor.networkState,
        userPreferenceManager.userPreferences.map { it.enableOfflineMode },
        offlineServiceAvailabilityFlow()
    ) { networkState, userOfflinePreference, offlineServiceAvailable ->
        
        val networkQuality = NetworkQuality.fromNetworkState(networkState)
        
        determineTranslationMode(
            networkQuality = networkQuality,
            userOfflinePreference = userOfflinePreference,
            offlineServiceAvailable = offlineServiceAvailable
        )
    }.distinctUntilChanged()
    
    /**
     * 获取当前推荐的翻译模式
     */
    suspend fun getRecommendedTranslationMode(
        sourceLanguage: String,
        targetLanguage: String
    ): TranslationModeRecommendation {
        try {
            val networkQuality = NetworkQuality.fromNetworkState(networkStateMonitor.getCurrentNetworkState())
            val userOfflinePreference = userPreferenceManager.getCurrentPreferences().enableOfflineMode
            val offlineServiceAvailable = offlineTranslationService.isAvailable()
            val languagePairSupported = offlineTranslationService.isSupportedLanguagePair(sourceLanguage, targetLanguage)
            
            val recommendedMode = when {
                // 用户强制启用离线模式且离线服务可用
                userOfflinePreference && offlineServiceAvailable && languagePairSupported -> {
                    TranslationMode.OFFLINE_ONLY
                }
                
                // 网络不可用，必须使用离线模式
                !networkQuality.isConnected && offlineServiceAvailable && languagePairSupported -> {
                    TranslationMode.OFFLINE_ONLY
                }
                
                // 网络质量差或计费网络，推荐离线模式
                networkQuality.recommendOfflineMode && offlineServiceAvailable && languagePairSupported -> {
                    TranslationMode.OFFLINE_PREFERRED
                }
                
                // 网络良好且用户未强制离线，使用在线模式
                networkQuality.isConnected && networkQuality.isHighQuality && !userOfflinePreference -> {
                    TranslationMode.ONLINE_PREFERRED
                }
                
                // 默认混合模式
                else -> {
                    TranslationMode.HYBRID
                }
            }
            
            return TranslationModeRecommendation(
                mode = recommendedMode,
                reason = getRecommendationReason(
                    recommendedMode,
                    networkQuality,
                    userOfflinePreference,
                    offlineServiceAvailable,
                    languagePairSupported
                ),
                networkQuality = networkQuality,
                offlineServiceAvailable = offlineServiceAvailable,
                languagePairSupported = languagePairSupported
            )
            
        } catch (e: Exception) {
            Timber.e(e, "获取推荐翻译模式失败")
            return TranslationModeRecommendation(
                mode = TranslationMode.ONLINE_PREFERRED,
                reason = "获取推荐模式失败，默认使用在线模式",
                networkQuality = NetworkQuality.fromNetworkState(NetworkState.Disconnected),
                offlineServiceAvailable = false,
                languagePairSupported = false
            )
        }
    }
    
    /**
     * 检查指定模式是否可用
     */
    suspend fun isModeAvailable(
        mode: TranslationMode,
        sourceLanguage: String,
        targetLanguage: String
    ): Boolean {
        return when (mode) {
            TranslationMode.ONLINE_ONLY, TranslationMode.ONLINE_PREFERRED -> {
                networkStateMonitor.isConnected()
            }
            
            TranslationMode.OFFLINE_ONLY, TranslationMode.OFFLINE_PREFERRED -> {
                offlineTranslationService.isAvailable() && 
                offlineTranslationService.isSupportedLanguagePair(sourceLanguage, targetLanguage)
            }
            
            TranslationMode.HYBRID -> {
                networkStateMonitor.isConnected() || 
                (offlineTranslationService.isAvailable() && 
                 offlineTranslationService.isSupportedLanguagePair(sourceLanguage, targetLanguage))
            }
        }
    }
    
    /**
     * 强制设置翻译模式
     */
    suspend fun setTranslationMode(mode: TranslationMode): Boolean {
        return try {
            when (mode) {
                TranslationMode.OFFLINE_ONLY, TranslationMode.OFFLINE_PREFERRED -> {
                    // 启用离线模式偏好
                    userPreferenceManager.updateOfflineMode(true)
                }
                TranslationMode.ONLINE_ONLY, TranslationMode.ONLINE_PREFERRED -> {
                    // 禁用离线模式偏好
                    userPreferenceManager.updateOfflineMode(false)
                }
                TranslationMode.HYBRID -> {
                    // 保持当前偏好设置
                }
            }
            
            Timber.d("翻译模式设置成功: $mode")
            true
        } catch (e: Exception) {
            Timber.e(e, "设置翻译模式失败: $mode")
            false
        }
    }
    
    /**
     * 获取模式切换建议
     */
    suspend fun getModeSwitchSuggestion(): ModeSwitchSuggestion? {
        try {
            val networkQuality = NetworkQuality.fromNetworkState(networkStateMonitor.getCurrentNetworkState())
            val userOfflinePreference = userPreferenceManager.getCurrentPreferences().enableOfflineMode
            val offlineServiceAvailable = offlineTranslationService.isAvailable()
            
            return when {
                // 建议启用离线模式
                !userOfflinePreference && 
                networkQuality.recommendOfflineMode && 
                offlineServiceAvailable -> {
                    ModeSwitchSuggestion(
                        suggestedMode = TranslationMode.OFFLINE_PREFERRED,
                        reason = if (networkQuality.isMetered) {
                            "检测到计费网络，建议使用离线模式以节省流量"
                        } else {
                            "网络质量较差，建议使用离线模式以获得更好体验"
                        },
                        priority = SuggestionPriority.MEDIUM
                    )
                }
                
                // 建议启用在线模式
                userOfflinePreference && 
                networkQuality.isConnected && 
                networkQuality.isHighQuality && 
                !networkQuality.isMetered -> {
                    ModeSwitchSuggestion(
                        suggestedMode = TranslationMode.ONLINE_PREFERRED,
                        reason = "检测到高质量网络连接，建议使用在线模式以获得更准确的翻译",
                        priority = SuggestionPriority.LOW
                    )
                }
                
                // 离线服务不可用时建议在线模式
                userOfflinePreference && !offlineServiceAvailable && networkQuality.isConnected -> {
                    ModeSwitchSuggestion(
                        suggestedMode = TranslationMode.ONLINE_ONLY,
                        reason = "离线翻译服务不可用，建议使用在线模式",
                        priority = SuggestionPriority.HIGH
                    )
                }
                
                else -> null
            }
        } catch (e: Exception) {
            Timber.e(e, "获取模式切换建议失败")
            null
        }
    }
    
    /**
     * 监控离线服务可用性
     */
    private fun offlineServiceAvailabilityFlow(): Flow<Boolean> {
        // 简化实现：定期检查离线服务状态
        // 实际实现可以使用更复杂的监控机制
        return kotlinx.coroutines.flow.flow {
            while (true) {
                emit(offlineTranslationService.isAvailable())
                kotlinx.coroutines.delay(30000) // 每30秒检查一次
            }
        }.distinctUntilChanged()
    }
    
    /**
     * 确定翻译模式
     */
    private fun determineTranslationMode(
        networkQuality: NetworkQuality,
        userOfflinePreference: Boolean,
        offlineServiceAvailable: Boolean
    ): TranslationMode {
        return when {
            userOfflinePreference && offlineServiceAvailable -> TranslationMode.OFFLINE_PREFERRED
            !networkQuality.isConnected && offlineServiceAvailable -> TranslationMode.OFFLINE_ONLY
            networkQuality.recommendOfflineMode && offlineServiceAvailable -> TranslationMode.OFFLINE_PREFERRED
            networkQuality.isConnected && networkQuality.isHighQuality -> TranslationMode.ONLINE_PREFERRED
            networkQuality.isConnected -> TranslationMode.HYBRID
            else -> TranslationMode.OFFLINE_ONLY
        }
    }
    
    /**
     * 获取推荐原因
     */
    private fun getRecommendationReason(
        mode: TranslationMode,
        networkQuality: NetworkQuality,
        userOfflinePreference: Boolean,
        offlineServiceAvailable: Boolean,
        languagePairSupported: Boolean
    ): String {
        return when (mode) {
            TranslationMode.OFFLINE_ONLY -> {
                when {
                    !networkQuality.isConnected -> "网络不可用"
                    userOfflinePreference -> "用户偏好离线模式"
                    !languagePairSupported -> "不支持的语言对，仅在线模式可用"
                    else -> "推荐离线模式"
                }
            }
            TranslationMode.OFFLINE_PREFERRED -> {
                when {
                    networkQuality.isMetered -> "计费网络，推荐离线模式节省流量"
                    !networkQuality.isHighQuality -> "网络质量较差，推荐离线模式"
                    else -> "推荐优先使用离线模式"
                }
            }
            TranslationMode.ONLINE_PREFERRED -> "网络质量良好，推荐在线模式获得更准确翻译"
            TranslationMode.ONLINE_ONLY -> "离线服务不可用，仅在线模式可用"
            TranslationMode.HYBRID -> "混合模式，根据实际情况自动选择"
        }
    }
}

/**
 * 翻译模式枚举
 */
enum class TranslationMode {
    ONLINE_ONLY,        // 仅在线模式
    ONLINE_PREFERRED,   // 优先在线模式
    OFFLINE_ONLY,       // 仅离线模式
    OFFLINE_PREFERRED,  // 优先离线模式
    HYBRID              // 混合模式
}

/**
 * 翻译模式推荐结果
 */
data class TranslationModeRecommendation(
    val mode: TranslationMode,
    val reason: String,
    val networkQuality: NetworkQuality,
    val offlineServiceAvailable: Boolean,
    val languagePairSupported: Boolean
)

/**
 * 模式切换建议
 */
data class ModeSwitchSuggestion(
    val suggestedMode: TranslationMode,
    val reason: String,
    val priority: SuggestionPriority
)

/**
 * 建议优先级
 */
enum class SuggestionPriority {
    LOW,    // 低优先级建议
    MEDIUM, // 中等优先级建议
    HIGH    // 高优先级建议
}
