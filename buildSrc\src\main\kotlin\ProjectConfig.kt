object ProjectConfig {
    const val applicationId = "com.cadence.cadence"
    const val compileSdk = 34
    const val minSdk = 24
    const val targetSdk = 34
    const val versionCode = 1
    const val versionName = "1.0.0"
    
    const val jvmTarget = "1.8"
    const val kotlinCompilerExtensionVersion = "1.5.5"
    
    const val testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    const val consumerProguardFiles = "consumer-rules.pro"
}