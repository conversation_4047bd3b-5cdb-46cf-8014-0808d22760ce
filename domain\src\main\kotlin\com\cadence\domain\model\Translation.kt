package com.cadence.domain.model

import kotlinx.serialization.Serializable

/**
 * 翻译领域模型
 * 表示一次完整的翻译操作结果
 */
@Serializable
data class Translation(
    val id: String,
    val sourceText: String,
    val translatedText: String,
    val sourceLanguage: Language,
    val targetLanguage: Language,
    val confidenceScore: Float? = null,
    val isFavorite: Boolean = false,
    val createdAt: Long,
    val updatedAt: Long,
    val translationType: TranslationType = TranslationType.TEXT,
    val culturalContext: String? = null
)

/**
 * 语言领域模型
 */
@Serializable
data class Language(
    val code: String, // ISO 639-1 语言代码，如 "zh", "en"
    val name: String, // 语言名称，如 "中文", "English"
    val region: Region? = null // 地区信息
)

/**
 * 地区领域模型
 */
@Serializable
data class Region(
    val code: String, // 地区代码，如 "CN", "TW", "HK"
    val name: String, // 地区名称，如 "大陆", "台湾", "香港"
    val dialectName: String? = null, // 方言名称，如 "粤语", "闽南语"
    val culturalInfo: String? = null // 文化背景信息
)

/**
 * 翻译类型枚举
 */
@Serializable
enum class TranslationType {
    TEXT,    // 文本翻译
    VOICE,   // 语音翻译
    IMAGE    // 图片翻译（OCR）
}

/**
 * 翻译请求模型
 */
@Serializable
data class TranslationRequest(
    val sourceText: String,
    val sourceLanguage: Language,
    val targetLanguage: Language,
    val translationType: TranslationType = TranslationType.TEXT,
    val includeCulturalContext: Boolean = false,
    val saveToHistory: Boolean = true
)

/**
 * 翻译结果模型
 */
@Serializable
data class TranslationResult(
    val translation: Translation,
    val isFromCache: Boolean = false,
    val processingTimeMs: Long = 0L
)

/**
 * 语言检测结果模型
 */
@Serializable
data class LanguageDetection(
    val detectedLanguage: Language,
    val confidence: Float,
    val isReliable: Boolean = true,
    val alternatives: List<LanguageAlternative> = emptyList()
)

/**
 * 语言检测备选项
 */
@Serializable
data class LanguageAlternative(
    val language: Language,
    val confidence: Float
)

/**
 * 翻译历史查询参数
 */
data class TranslationHistoryQuery(
    val languagePair: Pair<Language, Language>? = null,
    val searchQuery: String? = null,
    val favoritesOnly: Boolean = false,
    val translationType: TranslationType? = null,
    val limit: Int = 50,
    val offset: Int = 0
)

/**
 * 翻译统计信息
 */
@Serializable
data class TranslationStatistics(
    val totalTranslations: Int,
    val favoriteCount: Int,
    val mostUsedLanguagePairs: List<LanguagePairUsage>,
    val translationsByType: Map<TranslationType, Int>,
    val averageConfidenceScore: Float
)

/**
 * 语言对使用统计
 */
@Serializable
data class LanguagePairUsage(
    val sourceLanguage: Language,
    val targetLanguage: Language,
    val usageCount: Int,
    val lastUsedAt: Long
)
