package com.cadence.domain.usecase

import com.cadence.domain.model.LanguageDetection
import com.cadence.domain.repository.LanguageRepository
import com.cadence.domain.repository.TranslationRepository
import timber.log.Timber
import javax.inject.Inject

/**
 * 语言检测用例
 * 处理文本语言检测的业务逻辑
 */
class DetectLanguageUseCase @Inject constructor(
    private val translationRepository: TranslationRepository,
    private val languageRepository: LanguageRepository
) {
    
    /**
     * 检测文本语言
     * @param text 待检测文本
     * @param minConfidence 最小置信度阈值
     * @return 语言检测结果
     */
    suspend operator fun invoke(
        text: String,
        minConfidence: Float = 0.5f
    ): Result<LanguageDetection> {
        return try {
            // 验证输入
            if (text.isBlank()) {
                return Result.failure(IllegalArgumentException("待检测文本不能为空"))
            }
            
            if (text.length < 3) {
                return Result.failure(IllegalArgumentException("文本长度过短，无法准确检测语言"))
            }
            
            // 执行语言检测
            val detectionResult = translationRepository.detectLanguage(text.trim())
            
            if (detectionResult.isSuccess) {
                val detection = detectionResult.getOrThrow()
                
                // 检查置信度
                if (detection.confidence < minConfidence) {
                    Timber.w("语言检测置信度过低: ${detection.confidence}, 阈值: $minConfidence")
                    return Result.failure(
                        IllegalStateException("语言检测置信度过低，无法确定语言类型")
                    )
                }
                
                // 验证检测到的语言是否支持
                val isSupported = languageRepository.isLanguageSupported(detection.detectedLanguage.code)
                if (!isSupported) {
                    Timber.w("检测到的语言不受支持: ${detection.detectedLanguage.code}")
                    return Result.failure(
                        UnsupportedOperationException("检测到的语言 '${detection.detectedLanguage.name}' 暂不支持")
                    )
                }
                
                // 更新语言使用记录
                languageRepository.updateLanguageUsage(detection.detectedLanguage)
                
                Timber.d("语言检测成功: ${detection.detectedLanguage.name} (置信度: ${detection.confidence})")
                Result.success(detection)
            } else {
                Timber.e("语言检测失败: ${detectionResult.exceptionOrNull()?.message}")
                detectionResult
            }
            
        } catch (e: Exception) {
            Timber.e(e, "语言检测过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 批量检测语言
     * @param texts 文本列表
     * @param minConfidence 最小置信度阈值
     * @return 检测结果列表
     */
    suspend fun detectBatch(
        texts: List<String>,
        minConfidence: Float = 0.5f
    ): Result<List<LanguageDetection>> {
        return try {
            if (texts.isEmpty()) {
                return Result.success(emptyList())
            }
            
            val results = mutableListOf<LanguageDetection>()
            
            for (text in texts) {
                val result = invoke(text, minConfidence)
                if (result.isSuccess) {
                    results.add(result.getOrThrow())
                } else {
                    Timber.w("批量语言检测中的单个文本检测失败: $text")
                }
            }
            
            Result.success(results)
        } catch (e: Exception) {
            Timber.e(e, "批量语言检测过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 智能语言检测
     * 结合多种策略提高检测准确性
     * @param text 待检测文本
     * @return 语言检测结果
     */
    suspend fun smartDetect(text: String): Result<LanguageDetection> {
        return try {
            // 首先尝试基于字符特征的快速检测
            val quickResult = quickDetectByCharacteristics(text)
            if (quickResult != null) {
                Timber.d("快速语言检测成功: ${quickResult.detectedLanguage.name}")
                return Result.success(quickResult)
            }
            
            // 如果快速检测失败，使用API检测
            invoke(text, minConfidence = 0.3f) // 降低置信度阈值
            
        } catch (e: Exception) {
            Timber.e(e, "智能语言检测过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 基于字符特征的快速语言检测
     * @param text 文本
     * @return 检测结果（可能为null）
     */
    private suspend fun quickDetectByCharacteristics(text: String): LanguageDetection? {
        return try {
            val chineseCharCount = text.count { it in '\u4e00'..'\u9fff' }
            val japaneseCharCount = text.count { it in '\u3040'..'\u309f' || it in '\u30a0'..'\u30ff' }
            val koreanCharCount = text.count { it in '\uac00'..'\ud7af' }
            val englishCharCount = text.count { it.isLetter() && it in 'a'..'z' || it in 'A'..'Z' }
            
            val totalChars = text.length
            
            when {
                chineseCharCount.toFloat() / totalChars > 0.3f -> {
                    val language = languageRepository.getLanguageByCode("zh").getOrNull()
                    language?.let {
                        LanguageDetection(
                            detectedLanguage = it,
                            confidence = 0.8f,
                            isReliable = true
                        )
                    }
                }
                japaneseCharCount.toFloat() / totalChars > 0.2f -> {
                    val language = languageRepository.getLanguageByCode("ja").getOrNull()
                    language?.let {
                        LanguageDetection(
                            detectedLanguage = it,
                            confidence = 0.8f,
                            isReliable = true
                        )
                    }
                }
                koreanCharCount.toFloat() / totalChars > 0.2f -> {
                    val language = languageRepository.getLanguageByCode("ko").getOrNull()
                    language?.let {
                        LanguageDetection(
                            detectedLanguage = it,
                            confidence = 0.8f,
                            isReliable = true
                        )
                    }
                }
                englishCharCount.toFloat() / totalChars > 0.7f -> {
                    val language = languageRepository.getLanguageByCode("en").getOrNull()
                    language?.let {
                        LanguageDetection(
                            detectedLanguage = it,
                            confidence = 0.7f,
                            isReliable = true
                        )
                    }
                }
                else -> null
            }
        } catch (e: Exception) {
            Timber.w(e, "快速语言检测失败")
            null
        }
    }
}
