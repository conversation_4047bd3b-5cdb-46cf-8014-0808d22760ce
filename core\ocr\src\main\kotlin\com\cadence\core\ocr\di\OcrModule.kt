package com.cadence.core.ocr.di

import android.content.Context
import com.cadence.core.ocr.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * OCR模块依赖注入配置
 * 提供OCR相关服务的依赖注入
 */
@Module
@InstallIn(SingletonComponent::class)
object OcrModule {
    
    /**
     * 提供文字识别服务
     */
    @Provides
    @Singleton
    fun provideTextRecognitionService(): TextRecognitionService {
        return TextRecognitionService()
    }
    
    /**
     * 提供相机服务
     */
    @Provides
    @Singleton
    fun provideCameraService(
        @ApplicationContext context: Context
    ): CameraService {
        return CameraService(context)
    }
    
    /**
     * 提供图片处理服务
     */
    @Provides
    @Singleton
    fun provideImageProcessingService(
        @ApplicationContext context: Context
    ): ImageProcessingService {
        return ImageProcessingService(context)
    }
    
    /**
     * 提供文字区域检测服务
     */
    @Provides
    @Singleton
    fun provideTextRegionDetectionService(): TextRegionDetectionService {
        return TextRegionDetectionService()
    }
    
    /**
     * 提供OCR优化服务
     */
    @Provides
    @Singleton
    fun provideOcrOptimizationService(): OcrOptimizationService {
        return OcrOptimizationService()
    }
}
