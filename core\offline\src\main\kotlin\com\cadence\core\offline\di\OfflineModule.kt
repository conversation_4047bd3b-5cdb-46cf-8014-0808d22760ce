package com.cadence.core.offline.di

import android.content.Context
import androidx.work.WorkManager
import com.cadence.core.common.UserPreferenceManager
import com.cadence.core.database.dao.OfflineDictionaryDao
import com.cadence.core.offline.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import javax.inject.Singleton

/**
 * 离线翻译模块依赖注入配置
 */
@Module
@InstallIn(SingletonComponent::class)
object OfflineModule {
    
    /**
     * 提供模型管理器
     */
    @Provides
    @Singleton
    fun provideModelManager(
        @ApplicationContext context: Context
    ): ModelManager {
        return ModelManager(context)
    }
    
    /**
     * 提供TensorFlow Lite推理引擎
     */
    @Provides
    @Singleton
    fun provideTensorFlowLiteEngine(
        @ApplicationContext context: Context
    ): TensorFlowLiteEngine {
        return TensorFlowLiteEngine(context)
    }
    
    /**
     * 提供离线翻译服务
     */
    @Provides
    @Singleton
    fun provideOfflineTranslationService(
        @ApplicationContext context: Context,
        tensorFlowLiteEngine: TensorFlowLiteEngine,
        modelManager: ModelManager
    ): OfflineTranslationService {
        return OfflineTranslationService(context, tensorFlowLiteEngine, modelManager)
    }

    /**
     * 提供离线词典服务
     */
    @Provides
    @Singleton
    fun provideOfflineDictionaryService(
        offlineDictionaryDao: OfflineDictionaryDao
    ): OfflineDictionaryService {
        return OfflineDictionaryService(offlineDictionaryDao)
    }

    /**
     * 提供网络状态监控器
     */
    @Provides
    @Singleton
    fun provideNetworkStateMonitor(
        @ApplicationContext context: Context
    ): NetworkStateMonitor {
        return NetworkStateMonitor(context)
    }

    /**
     * 提供翻译模式管理器
     */
    @Provides
    @Singleton
    fun provideTranslationModeManager(
        networkStateMonitor: NetworkStateMonitor,
        offlineTranslationService: OfflineTranslationService,
        userPreferenceManager: UserPreferenceManager
    ): TranslationModeManager {
        return TranslationModeManager(networkStateMonitor, offlineTranslationService, userPreferenceManager)
    }

    /**
     * 提供模型下载服务
     */
    @Provides
    @Singleton
    fun provideModelDownloadService(
        @ApplicationContext context: Context,
        modelManager: ModelManager,
        httpClient: OkHttpClient
    ): ModelDownloadService {
        return ModelDownloadService(context, modelManager, httpClient)
    }

    /**
     * 提供WorkManager实例
     */
    @Provides
    @Singleton
    fun provideWorkManager(
        @ApplicationContext context: Context
    ): WorkManager {
        return WorkManager.getInstance(context)
    }

    /**
     * 提供模型下载管理器
     */
    @Provides
    @Singleton
    fun provideModelDownloadManager(
        @ApplicationContext context: Context,
        workManager: WorkManager
    ): ModelDownloadManager {
        return ModelDownloadManager(context, workManager)
    }
}
