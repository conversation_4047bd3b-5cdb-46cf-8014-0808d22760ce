package com.cadence.feature.translation.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cadence.domain.model.*
import com.cadence.domain.usecase.ManageTranslationHistoryUseCase
import com.cadence.domain.repository.LanguageRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * 翻译历史ViewModel
 * 管理翻译历史界面的状态和业务逻辑
 */
@HiltViewModel
class TranslationHistoryViewModel @Inject constructor(
    private val manageTranslationHistoryUseCase: ManageTranslationHistoryUseCase,
    private val languageRepository: LanguageRepository
) : ViewModel() {
    
    // UI状态
    private val _uiState = MutableStateFlow(TranslationHistoryUiState())
    val uiState: StateFlow<TranslationHistoryUiState> = _uiState.asStateFlow()
    
    // 搜索查询
    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()
    
    // 翻译历史记录
    val translationHistory: StateFlow<List<Translation>> = combine(
        _searchQuery,
        _uiState.map { it.selectedLanguagePair },
        _uiState.map { it.favoritesOnly }
    ) { query, languagePair, favoritesOnly ->
        Triple(query, languagePair, favoritesOnly)
    }.flatMapLatest { (query, languagePair, favoritesOnly) ->
        if (query.isNotBlank()) {
            manageTranslationHistoryUseCase.searchTranslations(query)
        } else {
            manageTranslationHistoryUseCase.getTranslationHistory(
                TranslationHistoryQuery(
                    languagePair = languagePair,
                    favoritesOnly = favoritesOnly,
                    limit = 100
                )
            )
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = emptyList()
    )
    
    // 收藏的翻译记录
    val favoriteTranslations: StateFlow<List<Translation>> = manageTranslationHistoryUseCase
        .getFavoriteTranslations()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )
    
    // 支持的语言列表
    val supportedLanguages: StateFlow<List<Language>> = languageRepository
        .getSupportedLanguages()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )
    
    init {
        // 加载翻译统计信息
        loadTranslationStatistics()
    }
    
    /**
     * 更新搜索查询
     */
    fun updateSearchQuery(query: String) {
        _searchQuery.value = query
    }
    
    /**
     * 清空搜索查询
     */
    fun clearSearchQuery() {
        _searchQuery.value = ""
    }
    
    /**
     * 设置语言对筛选
     */
    fun setLanguagePairFilter(sourceLanguage: Language?, targetLanguage: Language?) {
        val languagePair = if (sourceLanguage != null && targetLanguage != null) {
            Pair(sourceLanguage, targetLanguage)
        } else null
        
        _uiState.update { it.copy(selectedLanguagePair = languagePair) }
    }
    
    /**
     * 清除语言对筛选
     */
    fun clearLanguagePairFilter() {
        _uiState.update { it.copy(selectedLanguagePair = null) }
    }
    
    /**
     * 切换仅显示收藏
     */
    fun toggleFavoritesOnly() {
        _uiState.update { it.copy(favoritesOnly = !it.favoritesOnly) }
    }
    
    /**
     * 更新收藏状态
     */
    fun updateFavoriteStatus(translationId: String, isFavorite: Boolean) {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }
            
            try {
                val result = manageTranslationHistoryUseCase.updateFavoriteStatus(translationId, isFavorite)
                
                if (result.isSuccess) {
                    Timber.d("更新收藏状态成功: $translationId -> $isFavorite")
                } else {
                    val error = result.exceptionOrNull()?.message ?: "更新收藏状态失败"
                    _uiState.update { it.copy(error = error) }
                    Timber.e("更新收藏状态失败: $error")
                }
            } catch (e: Exception) {
                _uiState.update { it.copy(error = e.message ?: "更新收藏状态时发生未知错误") }
                Timber.e(e, "更新收藏状态过程中发生异常")
            } finally {
                _uiState.update { it.copy(isLoading = false) }
            }
        }
    }
    
    /**
     * 删除翻译记录
     */
    fun deleteTranslation(translationId: String) {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }
            
            try {
                val result = manageTranslationHistoryUseCase.deleteTranslation(translationId)
                
                if (result.isSuccess) {
                    Timber.d("删除翻译记录成功: $translationId")
                } else {
                    val error = result.exceptionOrNull()?.message ?: "删除翻译记录失败"
                    _uiState.update { it.copy(error = error) }
                    Timber.e("删除翻译记录失败: $error")
                }
            } catch (e: Exception) {
                _uiState.update { it.copy(error = e.message ?: "删除翻译记录时发生未知错误") }
                Timber.e(e, "删除翻译记录过程中发生异常")
            } finally {
                _uiState.update { it.copy(isLoading = false) }
            }
        }
    }
    
    /**
     * 批量删除翻译记录
     */
    fun deleteTranslations(translationIds: List<String>) {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }
            
            try {
                val result = manageTranslationHistoryUseCase.deleteTranslations(translationIds)
                
                if (result.isSuccess) {
                    Timber.d("批量删除翻译记录成功: ${translationIds.size}条")
                } else {
                    val error = result.exceptionOrNull()?.message ?: "批量删除翻译记录失败"
                    _uiState.update { it.copy(error = error) }
                    Timber.e("批量删除翻译记录失败: $error")
                }
            } catch (e: Exception) {
                _uiState.update { it.copy(error = e.message ?: "批量删除翻译记录时发生未知错误") }
                Timber.e(e, "批量删除翻译记录过程中发生异常")
            } finally {
                _uiState.update { it.copy(isLoading = false) }
            }
        }
    }
    
    /**
     * 清空翻译历史
     */
    fun clearTranslationHistory() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }
            
            try {
                val result = manageTranslationHistoryUseCase.clearTranslationHistory()
                
                if (result.isSuccess) {
                    Timber.d("清空翻译历史成功")
                } else {
                    val error = result.exceptionOrNull()?.message ?: "清空翻译历史失败"
                    _uiState.update { it.copy(error = error) }
                    Timber.e("清空翻译历史失败: $error")
                }
            } catch (e: Exception) {
                _uiState.update { it.copy(error = e.message ?: "清空翻译历史时发生未知错误") }
                Timber.e(e, "清空翻译历史过程中发生异常")
            } finally {
                _uiState.update { it.copy(isLoading = false) }
            }
        }
    }
    
    /**
     * 加载翻译统计信息
     */
    fun loadTranslationStatistics() {
        viewModelScope.launch {
            try {
                val result = manageTranslationHistoryUseCase.getTranslationStatistics()
                
                if (result.isSuccess) {
                    val statistics = result.getOrThrow()
                    _uiState.update { it.copy(statistics = statistics) }
                    Timber.d("加载翻译统计信息成功")
                } else {
                    Timber.w("加载翻译统计信息失败: ${result.exceptionOrNull()?.message}")
                }
            } catch (e: Exception) {
                Timber.e(e, "加载翻译统计信息过程中发生异常")
            }
        }
    }
    
    /**
     * 导出翻译历史
     */
    fun exportTranslationHistory(format: ExportFormat) {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }
            
            try {
                val result = manageTranslationHistoryUseCase.exportTranslationHistory(format)
                
                if (result.isSuccess) {
                    val filePath = result.getOrThrow()
                    _uiState.update { it.copy(exportedFilePath = filePath) }
                    Timber.d("导出翻译历史成功: $filePath")
                } else {
                    val error = result.exceptionOrNull()?.message ?: "导出翻译历史失败"
                    _uiState.update { it.copy(error = error) }
                    Timber.e("导出翻译历史失败: $error")
                }
            } catch (e: Exception) {
                _uiState.update { it.copy(error = e.message ?: "导出翻译历史时发生未知错误") }
                Timber.e(e, "导出翻译历史过程中发生异常")
            } finally {
                _uiState.update { it.copy(isLoading = false) }
            }
        }
    }
    
    /**
     * 清除错误信息
     */
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }
    
    /**
     * 清除导出文件路径
     */
    fun clearExportedFilePath() {
        _uiState.update { it.copy(exportedFilePath = null) }
    }
}

/**
 * 翻译历史界面UI状态
 */
data class TranslationHistoryUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val selectedLanguagePair: Pair<Language, Language>? = null,
    val favoritesOnly: Boolean = false,
    val statistics: TranslationStatistics? = null,
    val exportedFilePath: String? = null
)
