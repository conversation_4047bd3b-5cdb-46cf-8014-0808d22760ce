package com.cadence.domain.usecase

import com.cadence.core.speech.SpeechRecognitionService
import com.cadence.domain.model.Language
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

/**
 * 语音转文字用例
 * 处理语音识别相关的业务逻辑
 */
class SpeechToTextUseCase @Inject constructor(
    private val speechRecognitionService: SpeechRecognitionService
) {
    
    /**
     * 语音识别结果
     */
    sealed class SpeechToTextResult {
        object Ready : SpeechToTextResult()
        object Listening : SpeechToTextResult()
        data class PartialResult(val text: String, val confidence: Float) : SpeechToTextResult()
        data class FinalResult(val text: String, val confidence: Float) : SpeechToTextResult()
        data class Error(val message: String, val errorCode: Int) : SpeechToTextResult()
        object Stopped : SpeechToTextResult()
    }
    
    /**
     * 语音识别配置
     */
    data class SpeechConfig(
        val language: Language,
        val enablePartialResults: Boolean = true,
        val enableOfflineRecognition: Boolean = false,
        val maxSpeechTimeoutMs: Long = 10000L,
        val maxNoSpeechTimeoutMs: Long = 5000L
    )
    
    /**
     * 检查语音识别是否可用
     */
    fun isAvailable(): Boolean {
        return speechRecognitionService.isAvailable()
    }
    
    /**
     * 获取支持的语言列表
     */
    fun getSupportedLanguages(): List<Language> {
        val supportedCodes = speechRecognitionService.getSupportedLanguages()
        return supportedCodes.map { code ->
            Language(
                code = code,
                name = speechRecognitionService.getLanguageDisplayName(code)
            )
        }
    }
    
    /**
     * 开始语音识别
     * @param config 语音识别配置
     * @return 语音识别结果流
     */
    fun startRecognition(config: SpeechConfig): Flow<SpeechToTextResult> {
        val speechConfig = SpeechRecognitionService.SpeechConfig(
            language = speechRecognitionService.mapTranslationLanguageToSpeech(config.language.code),
            partialResults = config.enablePartialResults,
            offlineOnly = config.enableOfflineRecognition,
            maxSpeechTimeoutMs = config.maxSpeechTimeoutMs,
            maxNoSpeechTimeoutMs = config.maxNoSpeechTimeoutMs
        )
        
        return speechRecognitionService.startRecognition(speechConfig).map { result ->
            when (result) {
                is SpeechRecognitionService.SpeechResult.Ready -> SpeechToTextResult.Ready
                is SpeechRecognitionService.SpeechResult.Listening -> SpeechToTextResult.Listening
                is SpeechRecognitionService.SpeechResult.PartialResult -> 
                    SpeechToTextResult.PartialResult(result.text, result.confidence)
                is SpeechRecognitionService.SpeechResult.Success -> 
                    SpeechToTextResult.FinalResult(result.text, result.confidence)
                is SpeechRecognitionService.SpeechResult.Error -> 
                    SpeechToTextResult.Error(result.message, result.errorCode)
                is SpeechRecognitionService.SpeechResult.Stopped -> SpeechToTextResult.Stopped
            }
        }
    }
    
    /**
     * 停止语音识别
     */
    fun stopRecognition() {
        speechRecognitionService.stopRecognition()
    }
    
    /**
     * 根据文本内容推荐最佳语音识别语言
     * @param text 文本内容
     * @return 推荐的语言
     */
    fun recommendLanguageForText(text: String): Language? {
        // 简单的语言检测逻辑
        return when {
            text.any { it in '\u4e00'..'\u9fff' } -> Language("zh-CN", "中文（简体）")
            text.any { it in '\u3040'..'\u309f' || it in '\u30a0'..'\u30ff' } -> Language("ja-JP", "日本語")
            text.any { it in '\uac00'..'\ud7af' } -> Language("ko-KR", "한국어")
            text.matches(Regex(".*[a-zA-Z].*")) -> Language("en-US", "English (US)")
            else -> null
        }
    }
    
    /**
     * 验证语音识别配置
     * @param config 配置
     * @return 验证结果和错误信息
     */
    fun validateConfig(config: SpeechConfig): Pair<Boolean, String?> {
        // 检查语言是否支持
        val supportedLanguages = getSupportedLanguages()
        if (!supportedLanguages.any { it.code == config.language.code }) {
            return false to "不支持的语言: ${config.language.name}"
        }
        
        // 检查超时设置
        if (config.maxSpeechTimeoutMs < 1000L) {
            return false to "语音超时时间不能少于1秒"
        }
        
        if (config.maxNoSpeechTimeoutMs < 1000L) {
            return false to "静音超时时间不能少于1秒"
        }
        
        if (config.maxSpeechTimeoutMs > 60000L) {
            return false to "语音超时时间不能超过60秒"
        }
        
        return true to null
    }
    
    /**
     * 获取语音识别质量评估
     * @param result 识别结果
     * @return 质量评分 (0.0 - 1.0)
     */
    fun evaluateRecognitionQuality(result: SpeechToTextResult.FinalResult): Float {
        return when {
            result.confidence >= 0.9f -> 1.0f
            result.confidence >= 0.8f -> 0.8f
            result.confidence >= 0.7f -> 0.6f
            result.confidence >= 0.6f -> 0.4f
            else -> 0.2f
        }
    }
    
    /**
     * 获取语音识别建议
     * @param result 识别结果
     * @return 改进建议
     */
    fun getRecognitionSuggestions(result: SpeechToTextResult): List<String> {
        return when (result) {
            is SpeechToTextResult.Error -> {
                when (result.errorCode) {
                    SpeechRecognitionService.ERROR_NETWORK_TIMEOUT -> 
                        listOf("网络连接超时", "请检查网络连接", "尝试使用离线识别")
                    SpeechRecognitionService.ERROR_NETWORK -> 
                        listOf("网络连接失败", "请检查网络设置", "尝试重新连接")
                    SpeechRecognitionService.ERROR_AUDIO -> 
                        listOf("音频录制失败", "请检查麦克风权限", "确保麦克风正常工作")
                    SpeechRecognitionService.ERROR_CLIENT -> 
                        listOf("客户端错误", "请重启应用", "清除应用缓存")
                    SpeechRecognitionService.ERROR_INSUFFICIENT_PERMISSIONS -> 
                        listOf("权限不足", "请授予录音权限", "检查应用权限设置")
                    SpeechRecognitionService.ERROR_NO_MATCH -> 
                        listOf("未识别到语音", "请说话更清晰", "靠近麦克风", "减少环境噪音")
                    SpeechRecognitionService.ERROR_RECOGNIZER_BUSY -> 
                        listOf("识别器忙碌", "请稍后重试", "等待当前识别完成")
                    SpeechRecognitionService.ERROR_SERVER -> 
                        listOf("服务器错误", "请稍后重试", "检查服务状态")
                    SpeechRecognitionService.ERROR_SPEECH_TIMEOUT -> 
                        listOf("语音超时", "请在规定时间内说话", "调整语音超时设置")
                    else -> listOf("未知错误", "请重试", "联系技术支持")
                }
            }
            is SpeechToTextResult.FinalResult -> {
                when {
                    result.confidence < 0.6f -> 
                        listOf("识别置信度较低", "请说话更清晰", "减少背景噪音", "靠近麦克风")
                    result.text.length < 3 -> 
                        listOf("识别文本较短", "请说完整的句子", "确保语音清晰")
                    else -> emptyList()
                }
            }
            else -> emptyList()
        }
    }
}
