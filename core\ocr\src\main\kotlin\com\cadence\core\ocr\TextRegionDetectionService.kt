package com.cadence.core.ocr

import android.graphics.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.*

/**
 * 文字区域检测服务
 * 提供智能文字区域检测和分析功能
 */
@Singleton
class TextRegionDetectionService @Inject constructor() {
    
    /**
     * 检测结果
     */
    sealed class DetectionResult {
        data class Success(
            val textRegions: List<TextRegion>,
            val confidence: Float,
            val processingTime: Long
        ) : DetectionResult()
        data class Error(
            val message: String,
            val errorCode: Int
        ) : DetectionResult()
    }
    
    /**
     * 文字区域信息
     */
    data class TextRegion(
        val boundingBox: Rect,
        val confidence: Float,
        val textDensity: Float,
        val orientation: Float,
        val regionType: RegionType
    )
    
    /**
     * 区域类型
     */
    enum class RegionType {
        PARAGRAPH,      // 段落
        LINE,          // 行
        WORD,          // 单词
        CHARACTER,     // 字符
        TITLE,         // 标题
        CAPTION        // 说明文字
    }
    
    /**
     * 检测配置
     */
    data class DetectionConfig(
        val minRegionSize: Int = 20,
        val maxRegionSize: Int = 1000,
        val minConfidence: Float = 0.6f,
        val enableHierarchy: Boolean = true,
        val detectOrientation: Boolean = true,
        val mergeNearbyRegions: Boolean = true,
        val filterNoiseRegions: Boolean = true
    )
    
    // 错误代码常量
    companion object {
        const val ERROR_INVALID_IMAGE = 4001
        const val ERROR_DETECTION_FAILED = 4002
        const val ERROR_NO_REGIONS_FOUND = 4003
        const val ERROR_PROCESSING_TIMEOUT = 4004
    }
    
    /**
     * 检测文字区域
     */
    suspend fun detectTextRegions(
        bitmap: Bitmap,
        config: DetectionConfig = DetectionConfig()
    ): DetectionResult = withContext(Dispatchers.Default) {
        try {
            val startTime = System.currentTimeMillis()
            
            // 1. 图片预处理
            val preprocessedBitmap = preprocessImage(bitmap)
            
            // 2. 边缘检测
            val edgeMap = detectEdges(preprocessedBitmap)
            
            // 3. 连通组件分析
            val components = findConnectedComponents(edgeMap)
            
            // 4. 文字区域候选
            val candidates = filterTextCandidates(components, config)
            
            // 5. 区域合并
            val mergedRegions = if (config.mergeNearbyRegions) {
                mergeNearbyRegions(candidates, config)
            } else {
                candidates
            }
            
            // 6. 层次结构分析
            val finalRegions = if (config.enableHierarchy) {
                analyzeHierarchy(mergedRegions, config)
            } else {
                mergedRegions
            }
            
            // 7. 噪声过滤
            val filteredRegions = if (config.filterNoiseRegions) {
                filterNoiseRegions(finalRegions, config)
            } else {
                finalRegions
            }
            
            val processingTime = System.currentTimeMillis() - startTime
            val averageConfidence = filteredRegions.map { it.confidence }.average().toFloat()
            
            if (filteredRegions.isEmpty()) {
                DetectionResult.Error("未检测到文字区域", ERROR_NO_REGIONS_FOUND)
            } else {
                DetectionResult.Success(filteredRegions, averageConfidence, processingTime)
            }
            
        } catch (e: Exception) {
            DetectionResult.Error("文字区域检测失败: ${e.message}", ERROR_DETECTION_FAILED)
        }
    }
    
    /**
     * 图片预处理
     */
    private fun preprocessImage(bitmap: Bitmap): Bitmap {
        // 转换为灰度图
        val grayBitmap = convertToGrayscale(bitmap)
        
        // 自适应阈值处理
        return applyAdaptiveThreshold(grayBitmap)
    }
    
    /**
     * 转换为灰度图
     */
    private fun convertToGrayscale(bitmap: Bitmap): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        val grayBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        
        val pixels = IntArray(width * height)
        bitmap.getPixels(pixels, 0, width, 0, 0, width, height)
        
        for (i in pixels.indices) {
            val pixel = pixels[i]
            val r = Color.red(pixel)
            val g = Color.green(pixel)
            val b = Color.blue(pixel)
            val gray = (0.299 * r + 0.587 * g + 0.114 * b).toInt()
            pixels[i] = Color.rgb(gray, gray, gray)
        }
        
        grayBitmap.setPixels(pixels, 0, width, 0, 0, width, height)
        return grayBitmap
    }
    
    /**
     * 自适应阈值处理
     */
    private fun applyAdaptiveThreshold(bitmap: Bitmap): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        val result = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        
        val pixels = IntArray(width * height)
        bitmap.getPixels(pixels, 0, width, 0, 0, width, height)
        
        val blockSize = 15
        val c = 10
        
        for (y in 0 until height) {
            for (x in 0 until width) {
                val mean = calculateLocalMean(pixels, x, y, width, height, blockSize)
                val pixel = Color.red(pixels[y * width + x])
                val threshold = mean - c
                
                val newPixel = if (pixel > threshold) Color.WHITE else Color.BLACK
                pixels[y * width + x] = newPixel
            }
        }
        
        result.setPixels(pixels, 0, width, 0, 0, width, height)
        return result
    }
    
    /**
     * 计算局部均值
     */
    private fun calculateLocalMean(
        pixels: IntArray,
        x: Int,
        y: Int,
        width: Int,
        height: Int,
        blockSize: Int
    ): Int {
        val halfBlock = blockSize / 2
        var sum = 0
        var count = 0
        
        for (dy in -halfBlock..halfBlock) {
            for (dx in -halfBlock..halfBlock) {
                val nx = x + dx
                val ny = y + dy
                
                if (nx in 0 until width && ny in 0 until height) {
                    sum += Color.red(pixels[ny * width + nx])
                    count++
                }
            }
        }
        
        return if (count > 0) sum / count else 0
    }
    
    /**
     * 边缘检测
     */
    private fun detectEdges(bitmap: Bitmap): Bitmap {
        val sobelX = floatArrayOf(
            -1f, 0f, 1f,
            -2f, 0f, 2f,
            -1f, 0f, 1f
        )
        
        val sobelY = floatArrayOf(
            -1f, -2f, -1f,
            0f, 0f, 0f,
            1f, 2f, 1f
        )
        
        val edgeX = applyConvolution(bitmap, sobelX, 3)
        val edgeY = applyConvolution(bitmap, sobelY, 3)
        
        return combineEdges(edgeX, edgeY)
    }
    
    /**
     * 应用卷积核
     */
    private fun applyConvolution(bitmap: Bitmap, kernel: FloatArray, kernelSize: Int): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        val result = Bitmap.createBitmap(width, height, bitmap.config)
        
        val pixels = IntArray(width * height)
        bitmap.getPixels(pixels, 0, width, 0, 0, width, height)
        
        val newPixels = IntArray(width * height)
        val offset = kernelSize / 2
        
        for (y in offset until height - offset) {
            for (x in offset until width - offset) {
                var sum = 0f
                
                for (ky in 0 until kernelSize) {
                    for (kx in 0 until kernelSize) {
                        val pixelIndex = (y + ky - offset) * width + (x + kx - offset)
                        val pixel = Color.red(pixels[pixelIndex])
                        val kernelValue = kernel[ky * kernelSize + kx]
                        sum += pixel * kernelValue
                    }
                }
                
                val newValue = abs(sum).toInt().coerceIn(0, 255)
                newPixels[y * width + x] = Color.rgb(newValue, newValue, newValue)
            }
        }
        
        result.setPixels(newPixels, 0, width, 0, 0, width, height)
        return result
    }
    
    /**
     * 合并边缘
     */
    private fun combineEdges(edgeX: Bitmap, edgeY: Bitmap): Bitmap {
        val width = edgeX.width
        val height = edgeX.height
        val result = Bitmap.createBitmap(width, height, edgeX.config)
        
        val pixelsX = IntArray(width * height)
        val pixelsY = IntArray(width * height)
        edgeX.getPixels(pixelsX, 0, width, 0, 0, width, height)
        edgeY.getPixels(pixelsY, 0, width, 0, 0, width, height)
        
        val combinedPixels = IntArray(width * height)
        
        for (i in pixelsX.indices) {
            val x = Color.red(pixelsX[i])
            val y = Color.red(pixelsY[i])
            val magnitude = sqrt((x * x + y * y).toFloat()).toInt().coerceIn(0, 255)
            combinedPixels[i] = Color.rgb(magnitude, magnitude, magnitude)
        }
        
        result.setPixels(combinedPixels, 0, width, 0, 0, width, height)
        return result
    }
    
    /**
     * 查找连通组件
     */
    private fun findConnectedComponents(bitmap: Bitmap): List<Rect> {
        val width = bitmap.width
        val height = bitmap.height
        val pixels = IntArray(width * height)
        bitmap.getPixels(pixels, 0, width, 0, 0, width, height)
        
        val visited = BooleanArray(width * height)
        val components = mutableListOf<Rect>()
        
        for (y in 0 until height) {
            for (x in 0 until width) {
                val index = y * width + x
                if (!visited[index] && Color.red(pixels[index]) > 128) {
                    val component = floodFill(pixels, visited, x, y, width, height)
                    if (component != null) {
                        components.add(component)
                    }
                }
            }
        }
        
        return components
    }
    
    /**
     * 洪水填充算法
     */
    private fun floodFill(
        pixels: IntArray,
        visited: BooleanArray,
        startX: Int,
        startY: Int,
        width: Int,
        height: Int
    ): Rect? {
        val stack = mutableListOf<Pair<Int, Int>>()
        stack.add(Pair(startX, startY))
        
        var minX = startX
        var maxX = startX
        var minY = startY
        var maxY = startY
        var pixelCount = 0
        
        while (stack.isNotEmpty()) {
            val (x, y) = stack.removeAt(stack.size - 1)
            val index = y * width + x
            
            if (x < 0 || x >= width || y < 0 || y >= height || visited[index] || Color.red(pixels[index]) <= 128) {
                continue
            }
            
            visited[index] = true
            pixelCount++
            
            minX = minOf(minX, x)
            maxX = maxOf(maxX, x)
            minY = minOf(minY, y)
            maxY = maxOf(maxY, y)
            
            stack.add(Pair(x + 1, y))
            stack.add(Pair(x - 1, y))
            stack.add(Pair(x, y + 1))
            stack.add(Pair(x, y - 1))
        }
        
        return if (pixelCount > 10) {
            Rect(minX, minY, maxX, maxY)
        } else {
            null
        }
    }
    
    /**
     * 过滤文字候选区域
     */
    private fun filterTextCandidates(
        components: List<Rect>,
        config: DetectionConfig
    ): List<TextRegion> {
        return components.mapNotNull { rect ->
            val width = rect.width()
            val height = rect.height()
            
            // 尺寸过滤
            if (width < config.minRegionSize || height < config.minRegionSize ||
                width > config.maxRegionSize || height > config.maxRegionSize) {
                return@mapNotNull null
            }
            
            // 宽高比过滤
            val aspectRatio = width.toFloat() / height
            if (aspectRatio < 0.1f || aspectRatio > 20f) {
                return@mapNotNull null
            }
            
            // 计算置信度
            val confidence = calculateRegionConfidence(rect)
            if (confidence < config.minConfidence) {
                return@mapNotNull null
            }
            
            // 计算文字密度
            val textDensity = calculateTextDensity(rect)
            
            // 检测方向
            val orientation = if (config.detectOrientation) {
                detectOrientation(rect)
            } else {
                0f
            }
            
            // 确定区域类型
            val regionType = determineRegionType(rect, textDensity)
            
            TextRegion(rect, confidence, textDensity, orientation, regionType)
        }
    }
    
    /**
     * 计算区域置信度
     */
    private fun calculateRegionConfidence(rect: Rect): Float {
        // 基于区域特征计算置信度
        val area = rect.width() * rect.height()
        val perimeter = 2 * (rect.width() + rect.height())
        val compactness = (4 * PI * area) / (perimeter * perimeter)
        
        return compactness.toFloat().coerceIn(0f, 1f)
    }
    
    /**
     * 计算文字密度
     */
    private fun calculateTextDensity(rect: Rect): Float {
        // 简化实现：基于区域大小估算
        val area = rect.width() * rect.height()
        return (1000f / area).coerceIn(0f, 1f)
    }
    
    /**
     * 检测方向
     */
    private fun detectOrientation(rect: Rect): Float {
        // 简化实现：基于宽高比估算
        return if (rect.width() > rect.height()) 0f else 90f
    }
    
    /**
     * 确定区域类型
     */
    private fun determineRegionType(rect: Rect, textDensity: Float): RegionType {
        val area = rect.width() * rect.height()
        val aspectRatio = rect.width().toFloat() / rect.height()
        
        return when {
            area > 10000 && aspectRatio < 3f -> RegionType.PARAGRAPH
            aspectRatio > 5f -> RegionType.LINE
            area < 500 -> RegionType.CHARACTER
            textDensity > 0.8f -> RegionType.TITLE
            else -> RegionType.WORD
        }
    }
    
    /**
     * 合并相邻区域
     */
    private fun mergeNearbyRegions(
        regions: List<TextRegion>,
        config: DetectionConfig
    ): List<TextRegion> {
        val merged = mutableListOf<TextRegion>()
        val used = BooleanArray(regions.size)
        
        for (i in regions.indices) {
            if (used[i]) continue
            
            var currentRegion = regions[i]
            used[i] = true
            
            for (j in i + 1 until regions.size) {
                if (used[j]) continue
                
                val otherRegion = regions[j]
                if (shouldMergeRegions(currentRegion, otherRegion)) {
                    currentRegion = mergeRegions(currentRegion, otherRegion)
                    used[j] = true
                }
            }
            
            merged.add(currentRegion)
        }
        
        return merged
    }
    
    /**
     * 判断是否应该合并区域
     */
    private fun shouldMergeRegions(region1: TextRegion, region2: TextRegion): Boolean {
        val rect1 = region1.boundingBox
        val rect2 = region2.boundingBox
        
        // 计算距离
        val distance = calculateDistance(rect1, rect2)
        val minSize = minOf(rect1.width(), rect1.height(), rect2.width(), rect2.height())
        
        return distance < minSize * 0.5f
    }
    
    /**
     * 计算两个矩形的距离
     */
    private fun calculateDistance(rect1: Rect, rect2: Rect): Float {
        val centerX1 = rect1.centerX()
        val centerY1 = rect1.centerY()
        val centerX2 = rect2.centerX()
        val centerY2 = rect2.centerY()
        
        return sqrt(((centerX1 - centerX2) * (centerX1 - centerX2) + (centerY1 - centerY2) * (centerY1 - centerY2)).toFloat())
    }
    
    /**
     * 合并两个区域
     */
    private fun mergeRegions(region1: TextRegion, region2: TextRegion): TextRegion {
        val mergedRect = Rect()
        mergedRect.union(region1.boundingBox)
        mergedRect.union(region2.boundingBox)
        
        val avgConfidence = (region1.confidence + region2.confidence) / 2
        val avgDensity = (region1.textDensity + region2.textDensity) / 2
        val avgOrientation = (region1.orientation + region2.orientation) / 2
        
        return TextRegion(mergedRect, avgConfidence, avgDensity, avgOrientation, RegionType.PARAGRAPH)
    }
    
    /**
     * 分析层次结构
     */
    private fun analyzeHierarchy(
        regions: List<TextRegion>,
        config: DetectionConfig
    ): List<TextRegion> {
        // 简化实现：按面积排序
        return regions.sortedByDescending { it.boundingBox.width() * it.boundingBox.height() }
    }
    
    /**
     * 过滤噪声区域
     */
    private fun filterNoiseRegions(
        regions: List<TextRegion>,
        config: DetectionConfig
    ): List<TextRegion> {
        return regions.filter { region ->
            region.confidence >= config.minConfidence &&
            region.textDensity > 0.1f &&
            region.boundingBox.width() >= config.minRegionSize &&
            region.boundingBox.height() >= config.minRegionSize
        }
    }
}
