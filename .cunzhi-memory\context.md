# 项目上下文信息

- Cadence Android翻译应用开发项目。基础架构已完成，当前执行第二阶段核心功能开发。执行顺序：数据层→网络层→业务层→UI层。技术栈：Kotlin+Compose+Hilt+Room+Retrofit+Gemini API
- 任务6语音功能已完成，包括SpeechRecognitionService、TextToSpeechService、权限管理、UI组件和用例实现。支持25种语言，完整的Clean Architecture实现。现在开始任务7 - OCR图片翻译功能开发。
- 任务8离线翻译功能开始实施。项目已完成语音功能(任务6)和OCR功能(任务7)。当前架构：Clean Architecture + Hilt + Room + Retrofit + Compose。现有翻译服务基于Gemini API，支持缓存、语言检测、文化背景。需要集成TensorFlow Lite模型、离线词典、模型下载管理、在线/离线模式切换。用户偏好已支持enableOfflineMode字段。
- 任务8离线翻译功能已完成实现，包含5个子任务：8.1 ML模型集成基础设施、8.2 离线词典功能、8.3 翻译模式管理器、8.4 集成到现有翻译仓库、8.5 后台模型下载。核心实现包括TensorFlowLite推理引擎、离线词典服务、智能模式切换、WorkManager后台下载等功能，支持完整的在线/离线翻译无缝切换。
