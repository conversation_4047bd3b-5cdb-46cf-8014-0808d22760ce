package com.cadence.core.network.api

import com.cadence.core.network.dto.*
import retrofit2.Response
import retrofit2.http.*

/**
 * Gemini API服务接口
 * 定义与Google Gemini API的交互方法
 */
interface GeminiApiService {
    
    /**
     * 生成内容（用于翻译）
     * @param model 模型名称，如 "gemini-pro"
     * @param request 请求体
     */
    @POST("v1beta/models/{model}:generateContent")
    suspend fun generateContent(
        @Path("model") model: String = "gemini-pro",
        @Body request: GeminiTranslationRequest
    ): Response<GeminiResponse>
    
    /**
     * 流式生成内容
     * @param model 模型名称
     * @param request 请求体
     */
    @POST("v1beta/models/{model}:streamGenerateContent")
    suspend fun streamGenerateContent(
        @Path("model") model: String = "gemini-pro",
        @Body request: GeminiTranslationRequest
    ): Response<GeminiResponse>
    
    /**
     * 获取模型信息
     * @param model 模型名称
     */
    @GET("v1beta/models/{model}")
    suspend fun getModelInfo(
        @Path("model") model: String
    ): Response<ModelInfo>
    
    /**
     * 获取支持的模型列表
     */
    @GET("v1beta/models")
    suspend fun listModels(): Response<ModelList>
    
    companion object {
        const val BASE_URL = "https://generativelanguage.googleapis.com/"
        
        // 支持的模型
        const val MODEL_GEMINI_PRO = "gemini-pro"
        const val MODEL_GEMINI_PRO_VISION = "gemini-pro-vision"
        
        // API版本
        const val API_VERSION = "v1beta"
    }
}

/**
 * 模型信息响应
 */
@kotlinx.serialization.Serializable
data class ModelInfo(
    @kotlinx.serialization.SerialName("name")
    val name: String,
    
    @kotlinx.serialization.SerialName("displayName")
    val displayName: String,
    
    @kotlinx.serialization.SerialName("description")
    val description: String,
    
    @kotlinx.serialization.SerialName("inputTokenLimit")
    val inputTokenLimit: Int,
    
    @kotlinx.serialization.SerialName("outputTokenLimit")
    val outputTokenLimit: Int,
    
    @kotlinx.serialization.SerialName("supportedGenerationMethods")
    val supportedGenerationMethods: List<String>
)

/**
 * 模型列表响应
 */
@kotlinx.serialization.Serializable
data class ModelList(
    @kotlinx.serialization.SerialName("models")
    val models: List<ModelInfo>
)
