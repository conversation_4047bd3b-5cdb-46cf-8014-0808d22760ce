package com.cadence.core.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.serialization.Serializable

/**
 * 翻译记录数据实体
 * 存储用户的翻译历史记录
 */
@Entity(tableName = "translations")
@Serializable
data class TranslationEntity(
    @PrimaryKey
    @ColumnInfo(name = "id")
    val id: String,
    
    @ColumnInfo(name = "source_text")
    val sourceText: String,
    
    @ColumnInfo(name = "translated_text")
    val translatedText: String,
    
    @ColumnInfo(name = "source_language")
    val sourceLanguage: String,
    
    @ColumnInfo(name = "target_language")
    val targetLanguage: String,
    
    @ColumnInfo(name = "source_region")
    val sourceRegion: String? = null,
    
    @ColumnInfo(name = "target_region")
    val targetRegion: String? = null,
    
    @ColumnInfo(name = "confidence_score")
    val confidenceScore: Float? = null,
    
    @ColumnInfo(name = "is_favorite")
    val isFavorite: Boolean = false,
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long,
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long,
    
    @ColumnInfo(name = "translation_type")
    val translationType: String = "text", // text, voice, image
    
    @ColumnInfo(name = "cultural_context")
    val culturalContext: String? = null
)
