package com.cadence.core.database.di

import android.content.Context
import androidx.room.Room
import com.cadence.core.database.CadenceDatabase
import com.cadence.core.database.dao.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 数据库依赖注入模块
 * 提供数据库和DAO的依赖注入配置
 */
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    /**
     * 提供数据库实例
     */
    @Provides
    @Singleton
    fun provideDatabase(@ApplicationContext context: Context): CadenceDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            CadenceDatabase::class.java,
            CadenceDatabase.DATABASE_NAME
        )
            .addCallback(DatabaseInitCallback())
            .build()
    }
    
    /**
     * 提供翻译记录DAO
     */
    @Provides
    fun provideTranslationDao(database: CadenceDatabase): TranslationDao {
        return database.translationDao()
    }
    
    /**
     * 提供语言区域DAO
     */
    @Provides
    fun provideLanguageRegionDao(database: CadenceDatabase): LanguageRegionDao {
        return database.languageRegionDao()
    }
    
    /**
     * 提供用户偏好DAO
     */
    @Provides
    fun provideUserPreferenceDao(database: CadenceDatabase): UserPreferenceDao {
        return database.userPreferenceDao()
    }
    
    /**
     * 提供翻译缓存DAO
     */
    @Provides
    fun provideTranslationCacheDao(database: CadenceDatabase): TranslationCacheDao {
        return database.translationCacheDao()
    }
}

/**
 * 数据库初始化回调
 * 在数据库创建时执行初始化操作
 */
private class DatabaseInitCallback : androidx.room.RoomDatabase.Callback() {
    // 可以在这里添加数据库初始化逻辑
    // 例如：预填充语言区域数据
}
