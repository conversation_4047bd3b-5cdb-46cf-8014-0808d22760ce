package com.cadence.core.offline

import android.content.Context
import androidx.hilt.work.HiltWorker
import androidx.work.*
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import kotlinx.coroutines.flow.first
import timber.log.Timber
import java.util.concurrent.TimeUnit

/**
 * 模型下载后台任务
 * 使用WorkManager在后台下载离线翻译模型
 */
@HiltWorker
class ModelDownloadWorker @AssistedInject constructor(
    @Assisted context: Context,
    @Assisted workerParams: WorkerParameters,
    private val modelDownloadService: ModelDownloadService,
    private val networkStateMonitor: NetworkStateMonitor
) : CoroutineWorker(context, workerParams) {
    
    companion object {
        const val WORK_NAME = "model_download_work"
        const val PROGRESS_KEY = "download_progress"
        const val CURRENT_FILE_KEY = "current_file"
        const val ERROR_MESSAGE_KEY = "error_message"
        
        // 输入参数键
        const val FORCE_DOWNLOAD_KEY = "force_download"
        const val WIFI_ONLY_KEY = "wifi_only"
        
        /**
         * 创建一次性下载任务
         */
        fun createOneTimeWork(
            forceDownload: Boolean = false,
            wifiOnly: Boolean = true
        ): OneTimeWorkRequest {
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(
                    if (wifiOnly) NetworkType.UNMETERED else NetworkType.CONNECTED
                )
                .setRequiresBatteryNotLow(true)
                .setRequiresStorageNotLow(true)
                .build()
            
            val inputData = Data.Builder()
                .putBoolean(FORCE_DOWNLOAD_KEY, forceDownload)
                .putBoolean(WIFI_ONLY_KEY, wifiOnly)
                .build()
            
            return OneTimeWorkRequestBuilder<ModelDownloadWorker>()
                .setConstraints(constraints)
                .setInputData(inputData)
                .setBackoffCriteria(
                    BackoffPolicy.EXPONENTIAL,
                    WorkRequest.MIN_BACKOFF_MILLIS,
                    TimeUnit.MILLISECONDS
                )
                .build()
        }
        
        /**
         * 创建定期检查更新任务
         */
        fun createPeriodicUpdateCheckWork(): PeriodicWorkRequest {
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .setRequiresBatteryNotLow(true)
                .build()
            
            return PeriodicWorkRequestBuilder<ModelDownloadWorker>(
                1, TimeUnit.DAYS // 每天检查一次更新
            )
                .setConstraints(constraints)
                .setBackoffCriteria(
                    BackoffPolicy.EXPONENTIAL,
                    WorkRequest.MIN_BACKOFF_MILLIS,
                    TimeUnit.MILLISECONDS
                )
                .build()
        }
    }
    
    override suspend fun doWork(): Result {
        return try {
            Timber.d("开始执行模型下载任务")
            
            // 检查网络状态
            val networkState = networkStateMonitor.getCurrentNetworkState()
            val wifiOnly = inputData.getBoolean(WIFI_ONLY_KEY, true)
            
            if (!isNetworkSuitable(networkState, wifiOnly)) {
                Timber.w("网络条件不满足下载要求")
                return Result.retry()
            }
            
            // 设置进度监听
            setupProgressMonitoring()
            
            val forceDownload = inputData.getBoolean(FORCE_DOWNLOAD_KEY, false)
            
            if (!forceDownload) {
                // 检查是否需要更新
                val updateCheck = modelDownloadService.checkForUpdates()
                when (updateCheck) {
                    is UpdateCheckResult.UpToDate -> {
                        Timber.d("模型已是最新版本，无需下载")
                        return Result.success()
                    }
                    is UpdateCheckResult.Error -> {
                        Timber.e("检查更新失败: ${updateCheck.message}")
                        return Result.retry()
                    }
                    is UpdateCheckResult.UpdateAvailable -> {
                        Timber.d("发现模型更新: ${updateCheck.currentVersion} -> ${updateCheck.latestVersion}")
                        // 继续执行下载
                    }
                }
            }
            
            // 执行下载
            val downloadResult = modelDownloadService.downloadModel()
            
            when (downloadResult) {
                is DownloadResult.Success -> {
                    Timber.d("模型下载任务完成")
                    setProgressAsync(
                        Data.Builder()
                            .putFloat(PROGRESS_KEY, 1.0f)
                            .putString(CURRENT_FILE_KEY, "下载完成")
                            .build()
                    )
                    Result.success()
                }
                is DownloadResult.Error -> {
                    Timber.e("模型下载失败: ${downloadResult.message}")
                    setProgressAsync(
                        Data.Builder()
                            .putString(ERROR_MESSAGE_KEY, downloadResult.message)
                            .build()
                    )
                    
                    // 根据错误类型决定是否重试
                    if (shouldRetryOnError(downloadResult.message)) {
                        Result.retry()
                    } else {
                        Result.failure()
                    }
                }
            }
            
        } catch (e: Exception) {
            Timber.e(e, "模型下载任务异常")
            setProgressAsync(
                Data.Builder()
                    .putString(ERROR_MESSAGE_KEY, "下载任务异常: ${e.message}")
                    .build()
            )
            Result.failure()
        }
    }
    
    /**
     * 设置进度监听
     */
    private suspend fun setupProgressMonitoring() {
        try {
            // 监听下载进度并更新WorkManager进度
            kotlinx.coroutines.launch {
                modelDownloadService.downloadProgress.collect { progress ->
                    progress?.let {
                        setProgressAsync(
                            Data.Builder()
                                .putFloat(PROGRESS_KEY, it.overallProgress)
                                .putString(CURRENT_FILE_KEY, it.currentFile)
                                .build()
                        )
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "设置进度监听失败")
        }
    }
    
    /**
     * 检查网络是否适合下载
     */
    private fun isNetworkSuitable(networkState: NetworkState, wifiOnly: Boolean): Boolean {
        return when (networkState) {
            is NetworkState.Connected -> {
                if (wifiOnly) {
                    // 仅WiFi模式下，检查是否为非计费网络
                    !networkState.isMetered
                } else {
                    // 允许移动网络，但检查网络质量
                    networkState.quality != NetworkQuality.POOR
                }
            }
            is NetworkState.Disconnected -> false
        }
    }
    
    /**
     * 根据错误信息判断是否应该重试
     */
    private fun shouldRetryOnError(errorMessage: String): Boolean {
        return when {
            errorMessage.contains("网络") -> true
            errorMessage.contains("超时") -> true
            errorMessage.contains("连接") -> true
            errorMessage.contains("服务器") -> true
            errorMessage.contains("校验失败") -> false // 文件损坏，重试无意义
            errorMessage.contains("存储空间") -> false // 存储不足，重试无意义
            else -> false
        }
    }
    
    override suspend fun getForegroundInfo(): ForegroundInfo {
        return createForegroundInfo()
    }
    
    /**
     * 创建前台服务信息
     */
    private fun createForegroundInfo(): ForegroundInfo {
        val notificationId = 1001
        val channelId = "model_download_channel"
        
        // 创建通知渠道（Android 8.0+）
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            val channel = android.app.NotificationChannel(
                channelId,
                "模型下载",
                android.app.NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "离线翻译模型下载进度"
                setSound(null, null)
                enableVibration(false)
            }
            
            val notificationManager = applicationContext.getSystemService(Context.NOTIFICATION_SERVICE) 
                as android.app.NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
        
        // 创建通知
        val notification = androidx.core.app.NotificationCompat.Builder(applicationContext, channelId)
            .setContentTitle("正在下载离线翻译模型")
            .setContentText("准备下载...")
            .setSmallIcon(android.R.drawable.stat_sys_download)
            .setProgress(100, 0, true)
            .setOngoing(true)
            .setSilent(true)
            .build()
        
        return ForegroundInfo(notificationId, notification)
    }
}

/**
 * 模型下载管理器
 * 提供便捷的下载任务管理接口
 */
class ModelDownloadManager @Inject constructor(
    private val context: Context,
    private val workManager: WorkManager
) {
    
    /**
     * 开始下载模型
     */
    fun startDownload(
        forceDownload: Boolean = false,
        wifiOnly: Boolean = true
    ): LiveData<WorkInfo> {
        val workRequest = ModelDownloadWorker.createOneTimeWork(forceDownload, wifiOnly)
        
        workManager.enqueueUniqueWork(
            ModelDownloadWorker.WORK_NAME,
            ExistingWorkPolicy.REPLACE,
            workRequest
        )
        
        return workManager.getWorkInfoByIdLiveData(workRequest.id)
    }
    
    /**
     * 取消下载
     */
    fun cancelDownload() {
        workManager.cancelUniqueWork(ModelDownloadWorker.WORK_NAME)
    }
    
    /**
     * 获取下载状态
     */
    fun getDownloadStatus(): LiveData<List<WorkInfo>> {
        return workManager.getWorkInfosForUniqueWorkLiveData(ModelDownloadWorker.WORK_NAME)
    }
    
    /**
     * 启用自动更新检查
     */
    fun enableAutoUpdateCheck() {
        val workRequest = ModelDownloadWorker.createPeriodicUpdateCheckWork()
        
        workManager.enqueueUniquePeriodicWork(
            "model_update_check",
            ExistingPeriodicWorkPolicy.KEEP,
            workRequest
        )
    }
    
    /**
     * 禁用自动更新检查
     */
    fun disableAutoUpdateCheck() {
        workManager.cancelUniqueWork("model_update_check")
    }
}
