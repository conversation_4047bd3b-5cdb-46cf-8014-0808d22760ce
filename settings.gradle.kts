pluginManagement {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.name = "Cadence"

include(":app")

// Core modules
include(":core:common")
include(":core:network")
include(":core:database")
include(":core:ui")
include(":core:offline")

// Domain module
include(":domain")

// Data module
include(":data")

// Feature modules
include(":feature:translation")
include(":feature:history")
include(":feature:favorites")
include(":feature:settings")
include(":feature:learning")
include(":feature:cultural")