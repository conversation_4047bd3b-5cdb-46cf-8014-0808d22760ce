package com.cadence.core.speech

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import androidx.core.content.ContextCompat
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 语音功能权限管理器
 * 管理语音识别和录音相关权限
 */
@Singleton
class SpeechPermissionManager @Inject constructor(
    private val context: Context
) {
    
    companion object {
        /**
         * 语音识别所需权限
         */
        val SPEECH_PERMISSIONS = arrayOf(
            Manifest.permission.RECORD_AUDIO
        )
        
        /**
         * 权限请求码
         */
        const val SPEECH_PERMISSION_REQUEST_CODE = 1001
    }
    
    /**
     * 检查是否有录音权限
     */
    fun hasRecordAudioPermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 检查是否有所有语音相关权限
     */
    fun hasAllSpeechPermissions(): Boolean {
        return SPEECH_PERMISSIONS.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 获取缺失的权限列表
     */
    fun getMissingPermissions(): List<String> {
        return SPEECH_PERMISSIONS.filter { permission ->
            ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 获取权限说明文本
     */
    fun getPermissionRationale(permission: String): String {
        return when (permission) {
            Manifest.permission.RECORD_AUDIO -> 
                "需要录音权限来进行语音识别，将您的语音转换为文字进行翻译。"
            else -> "需要此权限来提供完整的语音功能。"
        }
    }
    
    /**
     * 获取所有权限的说明文本
     */
    fun getAllPermissionsRationale(): String {
        return "为了提供语音翻译功能，应用需要以下权限：\n\n" +
                "• 录音权限：用于语音识别，将您的语音转换为文字\n\n" +
                "这些权限仅用于语音翻译功能，不会用于其他目的。"
    }
}
