package com.cadence.data.repository

import com.cadence.core.database.dao.LanguageRegionDao
import com.cadence.data.mapper.LanguageMapper
import com.cadence.data.mapper.LanguageMapper.toLanguage
import com.cadence.data.mapper.LanguageMapper.toLanguageList
import com.cadence.data.mapper.LanguageMapper.toUniqueLanguageList
import com.cadence.data.mapper.LanguageMapper.getRegionsByLanguageCode
import com.cadence.data.mapper.LanguageMapper.createDefaultLanguageEntity
import com.cadence.domain.model.Language
import com.cadence.domain.model.Region
import com.cadence.domain.repository.LanguageRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 语言数据仓库实现
 * 管理语言和地区相关的数据操作
 */
@Singleton
class LanguageRepositoryImpl @Inject constructor(
    private val languageRegionDao: LanguageRegionDao
) : LanguageRepository {
    
    override fun getSupportedLanguages(): Flow<List<Language>> {
        return languageRegionDao.getSupportedLanguages().map { it.toUniqueLanguageList() }
    }
    
    override suspend fun getLanguageByCode(languageCode: String): Result<Language?> {
        return withContext(Dispatchers.IO) {
            try {
                val entity = languageRegionDao.getLanguageByCode(languageCode)
                val language = entity?.toLanguage()
                Result.success(language)
            } catch (e: Exception) {
                Timber.e(e, "获取语言信息失败: $languageCode")
                Result.failure(e)
            }
        }
    }
    
    override fun getRegionsByLanguage(languageCode: String): Flow<List<Region>> {
        return languageRegionDao.getRegionsByLanguage(languageCode).map { entities ->
            entities.getRegionsByLanguageCode(languageCode)
        }
    }
    
    override suspend fun getRegionByCode(regionCode: String): Result<Region?> {
        return withContext(Dispatchers.IO) {
            try {
                val entity = languageRegionDao.getRegionByCode(regionCode)
                val region = entity?.let { LanguageMapper.toRegion(it) }
                Result.success(region)
            } catch (e: Exception) {
                Timber.e(e, "获取地区信息失败: $regionCode")
                Result.failure(e)
            }
        }
    }
    
    override fun searchLanguages(query: String): Flow<List<Language>> {
        return languageRegionDao.searchLanguages(query).map { it.toLanguageList() }
    }
    
    override fun getPopularLanguages(): Flow<List<Language>> {
        return languageRegionDao.getPopularLanguages().map { it.toLanguageList() }
    }
    
    override fun getRecentlyUsedLanguages(limit: Int): Flow<List<Language>> {
        return languageRegionDao.getRecentlyUsedLanguages(limit).map { it.toLanguageList() }
    }
    
    override suspend fun updateLanguageUsage(language: Language): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val currentTime = System.currentTimeMillis()
                languageRegionDao.updateLanguageUsage(
                    languageCode = language.code,
                    regionCode = language.region?.code,
                    lastUsedAt = currentTime
                )
                Timber.d("更新语言使用记录成功: ${language.code}")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "更新语言使用记录失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun addCustomLanguage(language: Language): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val entity = LanguageMapper.toEntity(language)
                languageRegionDao.insertLanguageRegion(entity)
                Timber.d("添加自定义语言成功: ${language.code}")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "添加自定义语言失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun updateLanguage(language: Language): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val entity = LanguageMapper.toEntity(language).copy(
                    updatedAt = System.currentTimeMillis()
                )
                languageRegionDao.updateLanguageRegion(entity)
                Timber.d("更新语言信息成功: ${language.code}")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "更新语言信息失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun deleteCustomLanguage(languageCode: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                languageRegionDao.deleteLanguage(languageCode)
                Timber.d("删除自定义语言成功: $languageCode")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "删除自定义语言失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun isLanguageSupported(languageCode: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                languageRegionDao.isLanguageSupported(languageCode)
            } catch (e: Exception) {
                Timber.e(e, "检查语言支持状态失败: $languageCode")
                false
            }
        }
    }
    
    override suspend fun isLanguagePairSupported(sourceLanguage: String, targetLanguage: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val sourceSupported = languageRegionDao.isLanguageSupported(sourceLanguage)
                val targetSupported = languageRegionDao.isLanguageSupported(targetLanguage)
                sourceSupported && targetSupported
            } catch (e: Exception) {
                Timber.e(e, "检查语言对支持状态失败: $sourceLanguage -> $targetLanguage")
                false
            }
        }
    }
    
    override suspend fun initializeDefaultLanguages(): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val defaultLanguages = createDefaultLanguageEntities()
                
                for (language in defaultLanguages) {
                    try {
                        languageRegionDao.insertLanguageRegion(language)
                    } catch (e: Exception) {
                        // 忽略重复插入错误
                        Timber.w("语言已存在，跳过插入: ${language.languageCode}")
                    }
                }
                
                Timber.d("初始化默认语言数据完成")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "初始化默认语言数据失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun syncLanguageData(): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                // TODO: 实现从远程服务器同步语言数据的逻辑
                Timber.d("语言数据同步功能尚未实现")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "同步语言数据失败")
                Result.failure(e)
            }
        }
    }
    
    /**
     * 创建默认语言实体列表
     */
    private fun createDefaultLanguageEntities() = listOf(
        // 中文及其地区变体
        createDefaultLanguageEntity("zh", "中文", "CN", "大陆", "普通话", true),
        createDefaultLanguageEntity("zh", "中文", "TW", "台湾", "繁体中文", true),
        createDefaultLanguageEntity("zh", "中文", "HK", "香港", "粤语", true),
        createDefaultLanguageEntity("zh", "中文", "SG", "新加坡", "简体中文", false),
        
        // 英语及其地区变体
        createDefaultLanguageEntity("en", "English", "US", "美国", "美式英语", true),
        createDefaultLanguageEntity("en", "English", "GB", "英国", "英式英语", true),
        createDefaultLanguageEntity("en", "English", "AU", "澳大利亚", "澳式英语", false),
        createDefaultLanguageEntity("en", "English", "CA", "加拿大", "加式英语", false),
        
        // 其他主要语言
        createDefaultLanguageEntity("ja", "日本語", "JP", "日本", null, true),
        createDefaultLanguageEntity("ko", "한국어", "KR", "韩国", null, true),
        createDefaultLanguageEntity("fr", "Français", "FR", "法国", null, true),
        createDefaultLanguageEntity("de", "Deutsch", "DE", "德国", null, true),
        createDefaultLanguageEntity("es", "Español", "ES", "西班牙", null, true),
        createDefaultLanguageEntity("it", "Italiano", "IT", "意大利", null, false),
        createDefaultLanguageEntity("pt", "Português", "PT", "葡萄牙", null, false),
        createDefaultLanguageEntity("ru", "Русский", "RU", "俄罗斯", null, false),
        createDefaultLanguageEntity("ar", "العربية", "SA", "沙特阿拉伯", null, false),
        createDefaultLanguageEntity("hi", "हिन्दी", "IN", "印度", null, false),
        createDefaultLanguageEntity("th", "ไทย", "TH", "泰国", null, false),
        createDefaultLanguageEntity("vi", "Tiếng Việt", "VN", "越南", null, false),
        createDefaultLanguageEntity("id", "Bahasa Indonesia", "ID", "印度尼西亚", null, false),
        createDefaultLanguageEntity("ms", "Bahasa Melayu", "MY", "马来西亚", null, false),
        createDefaultLanguageEntity("tr", "Türkçe", "TR", "土耳其", null, false)
    )
}
