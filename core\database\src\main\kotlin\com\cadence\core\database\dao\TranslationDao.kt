package com.cadence.core.database.dao

import androidx.room.*
import com.cadence.core.database.entity.TranslationEntity
import kotlinx.coroutines.flow.Flow

/**
 * 翻译记录数据访问对象
 * 提供翻译历史的CRUD操作
 */
@Dao
interface TranslationDao {
    
    /**
     * 插入翻译记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTranslation(translation: TranslationEntity)
    
    /**
     * 批量插入翻译记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTranslations(translations: List<TranslationEntity>)
    
    /**
     * 更新翻译记录
     */
    @Update
    suspend fun updateTranslation(translation: TranslationEntity)
    
    /**
     * 删除翻译记录
     */
    @Delete
    suspend fun deleteTranslation(translation: TranslationEntity)
    
    /**
     * 根据ID删除翻译记录
     */
    @Query("DELETE FROM translations WHERE id = :id")
    suspend fun deleteTranslationById(id: String)
    
    /**
     * 清空所有翻译记录
     */
    @Query("DELETE FROM translations")
    suspend fun clearAllTranslations()
    
    /**
     * 根据ID获取翻译记录
     */
    @Query("SELECT * FROM translations WHERE id = :id")
    suspend fun getTranslationById(id: String): TranslationEntity?
    
    /**
     * 获取所有翻译记录（按时间倒序）
     */
    @Query("SELECT * FROM translations ORDER BY created_at DESC")
    fun getAllTranslations(): Flow<List<TranslationEntity>>
    
    /**
     * 获取收藏的翻译记录
     */
    @Query("SELECT * FROM translations WHERE is_favorite = 1 ORDER BY updated_at DESC")
    fun getFavoriteTranslations(): Flow<List<TranslationEntity>>
    
    /**
     * 根据语言对获取翻译记录
     */
    @Query("""
        SELECT * FROM translations 
        WHERE source_language = :sourceLanguage 
        AND target_language = :targetLanguage 
        ORDER BY created_at DESC
    """)
    fun getTranslationsByLanguagePair(
        sourceLanguage: String,
        targetLanguage: String
    ): Flow<List<TranslationEntity>>
    
    /**
     * 搜索翻译记录
     */
    @Query("""
        SELECT * FROM translations 
        WHERE source_text LIKE '%' || :query || '%' 
        OR translated_text LIKE '%' || :query || '%'
        ORDER BY created_at DESC
    """)
    fun searchTranslations(query: String): Flow<List<TranslationEntity>>
    
    /**
     * 获取最近的翻译记录（限制数量）
     */
    @Query("SELECT * FROM translations ORDER BY created_at DESC LIMIT :limit")
    fun getRecentTranslations(limit: Int): Flow<List<TranslationEntity>>
    
    /**
     * 更新收藏状态
     */
    @Query("UPDATE translations SET is_favorite = :isFavorite, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateFavoriteStatus(id: String, isFavorite: Boolean, updatedAt: Long)
    
    /**
     * 获取翻译记录总数
     */
    @Query("SELECT COUNT(*) FROM translations")
    suspend fun getTranslationCount(): Int
    
    /**
     * 删除指定时间之前的翻译记录
     */
    @Query("DELETE FROM translations WHERE created_at < :timestamp")
    suspend fun deleteTranslationsBefore(timestamp: Long)
}
