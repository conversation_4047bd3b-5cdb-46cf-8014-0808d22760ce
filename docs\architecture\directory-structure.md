# 项目目录结构设计

## 总体架构概述

本项目采用模块化的Clean Architecture设计，将代码按照功能和职责进行清晰的分层和模块划分。

## 根目录结构

```
Cadence/
├── app/                          # 主应用模块
├── buildSrc/                     # 构建脚本和依赖管理
├── core/                         # 核心共享模块
├── feature/                      # 功能模块
├── data/                         # 数据层模块
├── domain/                       # 领域层模块
├── gradle/                       # Gradle配置
├── docs/                         # 项目文档
└── scripts/                      # 构建和部署脚本
```

## 详细模块说明

### 1. app模块 - 应用入口

```
app/
├── src/
│   ├── main/
│   │   ├── java/com/cadence/regionaltranslator/
│   │   │   ├── MainActivity.kt                    # 主Activity
│   │   │   ├── MainApplication.kt                 # 应用程序类
│   │   │   ├── di/                                # 应用级依赖注入
│   │   │   │   ├── AppModule.kt
│   │   │   │   ├── DatabaseModule.kt
│   │   │   │   ├── NetworkModule.kt
│   │   │   │   └── RepositoryModule.kt
│   │   │   ├── navigation/                        # 应用导航
│   │   │   │   ├── AppNavigation.kt
│   │   │   │   ├── NavigationDestinations.kt
│   │   │   │   └── NavigationArgs.kt
│   │   │   └── ui/
│   │   │       ├── theme/                         # 应用主题
│   │   │       │   ├── Color.kt
│   │   │       │   ├── Theme.kt
│   │   │       │   ├── Type.kt
│   │   │       │   └── Shape.kt
│   │   │       └── components/                    # 全局UI组件
│   │   │           ├── LoadingIndicator.kt
│   │   │           ├── ErrorMessage.kt
│   │   │           └── CommonDialogs.kt
│   │   ├── res/                                   # 资源文件
│   │   │   ├── drawable/                          # 图标和图片
│   │   │   ├── values/                            # 字符串、颜色、尺寸
│   │   │   ├── values-night/                      # 深色主题资源
│   │   │   ├── values-zh/                         # 中文本地化
│   │   │   ├── values-en/                         # 英文本地化
│   │   │   ├── values-ja/                         # 日文本地化
│   │   │   └── values-ko/                         # 韩文本地化
│   │   └── AndroidManifest.xml
│   ├── test/                                      # 单元测试
│   └── androidTest/                               # 集成测试
├── build.gradle.kts
└── proguard-rules.pro
```

### 2. core模块 - 核心共享功能

#### core/common - 通用工具
```
core/common/
├── src/main/java/com/cadence/core/common/
│   ├── utils/                                     # 工具类
│   │   ├── DateUtils.kt
│   │   ├── StringUtils.kt
│   │   ├── NetworkUtils.kt
│   │   └── CryptoUtils.kt
│   ├── extensions/                                # 扩展函数
│   │   ├── StringExtensions.kt
│   │   ├── ContextExtensions.kt
│   │   └── ComposeExtensions.kt
│   ├── constants/                                 # 常量定义
│   │   ├── AppConstants.kt
│   │   ├── ApiConstants.kt
│   │   └── DatabaseConstants.kt
│   └── result/                                    # 结果封装
│       ├── Result.kt
│       ├── ApiResponse.kt
│       └── ErrorHandler.kt
└── build.gradle.kts
```

#### core/network - 网络层核心
```
core/network/
├── src/main/java/com/cadence/core/network/
│   ├── di/
│   │   └── NetworkModule.kt
│   ├── interceptors/                              # 网络拦截器
│   │   ├── AuthInterceptor.kt
│   │   ├── LoggingInterceptor.kt
│   │   └── ErrorInterceptor.kt
│   ├── security/                                  # 网络安全
│   │   ├── CertificatePinner.kt
│   │   └── ApiKeyManager.kt
│   └── client/                                    # 网络客户端
│       ├── NetworkClient.kt
│       └── RetrofitBuilder.kt
└── build.gradle.kts
```

#### core/database - 数据库核心
```
core/database/
├── src/main/java/com/cadence/core/database/
│   ├── di/
│   │   └── DatabaseModule.kt
│   ├── converters/                                # 数据转换器
│   │   ├── DateConverter.kt
│   │   ├── ListConverter.kt
│   │   └── JsonConverter.kt
│   ├── migrations/                                # 数据库迁移
│   │   ├── Migration1to2.kt
│   │   └── MigrationHelper.kt
│   └── AppDatabase.kt
└── build.gradle.kts
```

#### core/ui - UI核心组件
```
core/ui/
├── src/main/java/com/cadence/core/ui/
│   ├── components/                                # 通用UI组件
│   │   ├── buttons/
│   │   │   ├── PrimaryButton.kt
│   │   │   ├── SecondaryButton.kt
│   │   │   └── IconButton.kt
│   │   ├── cards/
│   │   │   ├── BaseCard.kt
│   │   │   └── ExpandableCard.kt
│   │   ├── inputs/
│   │   │   ├── TextInputField.kt
│   │   │   ├── SearchField.kt
│   │   │   └── DropdownSelector.kt
│   │   └── dialogs/
│   │       ├── ConfirmDialog.kt
│   │       ├── InfoDialog.kt
│   │       └── LoadingDialog.kt
│   ├── theme/                                     # UI主题
│   │   ├── AppTheme.kt
│   │   ├── Colors.kt
│   │   ├── Typography.kt
│   │   └── Dimensions.kt
│   └── utils/                                     # UI工具
│       ├── PreviewUtils.kt
│       ├── ComposeUtils.kt
│       └── AnimationUtils.kt
└── build.gradle.kts
```

### 3. domain模块 - 领域层

```
domain/
├── src/main/java/com/cadence/domain/
│   ├── models/                                    # 领域模型
│   │   ├── Language.kt
│   │   ├── RegionalStyle.kt
│   │   ├── TranslationResult.kt
│   │   ├── TranslationContext.kt
│   │   ├── UserPreferences.kt
│   │   └── CulturalExplanation.kt
│   ├── repositories/                              # 仓库接口
│   │   ├── TranslationRepository.kt
│   │   ├── HistoryRepository.kt
│   │   ├── PreferencesRepository.kt
│   │   ├── LanguageRepository.kt
│   │   └── CacheRepository.kt
│   ├── usecases/                                  # 用例
│   │   ├── translation/
│   │   │   ├── TranslateTextUseCase.kt
│   │   │   ├── DetectLanguageUseCase.kt
│   │   │   ├── GetSupportedLanguagesUseCase.kt
│   │   │   └── ValidateInputUseCase.kt
│   │   ├── history/
│   │   │   ├── GetTranslationHistoryUseCase.kt
│   │   │   ├── SaveTranslationUseCase.kt
│   │   │   ├── DeleteTranslationUseCase.kt
│   │   │   └── SearchHistoryUseCase.kt
│   │   ├── favorites/
│   │   │   ├── AddToFavoritesUseCase.kt
│   │   │   ├── RemoveFromFavoritesUseCase.kt
│   │   │   └── GetFavoritesUseCase.kt
│   │   ├── voice/
│   │   │   ├── SpeechToTextUseCase.kt
│   │   │   ├── TextToSpeechUseCase.kt
│   │   │   └── VoiceRecognitionUseCase.kt
│   │   ├── offline/
│   │   │   ├── DownloadLanguagePackUseCase.kt
│   │   │   ├── OfflineTranslateUseCase.kt
│   │   │   └── ManageOfflineDataUseCase.kt
│   │   └── preferences/
│   │       ├── GetUserPreferencesUseCase.kt
│   │       ├── UpdatePreferencesUseCase.kt
│   │       └── ResetPreferencesUseCase.kt
│   └── exceptions/                                # 领域异常
│       ├── TranslationException.kt
│       ├── NetworkException.kt
│       ├── ValidationException.kt
│       └── OfflineException.kt
└── build.gradle.kts
```

### 4. data模块 - 数据层

```
data/
├── src/main/java/com/cadence/data/
│   ├── local/                                     # 本地数据源
│   │   ├── database/
│   │   │   ├── entities/                          # 数据库实体
│   │   │   │   ├── TranslationEntity.kt
│   │   │   │   ├── RegionalStyleEntity.kt
│   │   │   │   ├── LanguageEntity.kt
│   │   │   │   └── FavoriteEntity.kt
│   │   │   ├── dao/                               # 数据访问对象
│   │   │   │   ├── TranslationDao.kt
│   │   │   │   ├── RegionalStyleDao.kt
│   │   │   │   ├── LanguageDao.kt
│   │   │   │   └── FavoriteDao.kt
│   │   │   └── AppDatabase.kt
│   │   ├── preferences/                           # 偏好设置
│   │   │   ├── PreferencesDataSource.kt
│   │   │   └── PreferencesKeys.kt
│   │   ├── cache/                                 # 缓存管理
│   │   │   ├── TranslationCache.kt
│   │   │   ├── MemoryCache.kt
│   │   │   └── DiskCache.kt
│   │   └── files/                                 # 文件管理
│   │       ├── FileManager.kt
│   │       ├── OfflineDataManager.kt
│   │       └── ExportManager.kt
│   ├── remote/                                    # 远程数据源
│   │   ├── api/                                   # API服务
│   │   │   ├── GeminiApiService.kt
│   │   │   ├── VoiceApiService.kt
│   │   │   ├── OcrApiService.kt
│   │   │   └── UpdateApiService.kt
│   │   ├── dto/                                   # 数据传输对象
│   │   │   ├── TranslationRequestDto.kt
│   │   │   ├── TranslationResponseDto.kt
│   │   │   ├── LanguageDetectionDto.kt
│   │   │   └── VoiceRequestDto.kt
│   │   ├── engines/                               # 翻译引擎
│   │   │   ├── GeminiTranslationEngine.kt
│   │   │   ├── RegionalPromptGenerator.kt
│   │   │   ├── OfflineTranslationEngine.kt
│   │   │   └── CulturalAnalyzer.kt
│   │   └── interceptors/                          # 网络拦截器
│   │       ├── ApiKeyInterceptor.kt
│   │       ├── RateLimitInterceptor.kt
│   │       └── ErrorHandlingInterceptor.kt
│   ├── repositories/                              # 仓库实现
│   │   ├── TranslationRepositoryImpl.kt
│   │   ├── HistoryRepositoryImpl.kt
│   │   ├── PreferencesRepositoryImpl.kt
│   │   ├── LanguageRepositoryImpl.kt
│   │   └── CacheRepositoryImpl.kt
│   ├── mappers/                                   # 数据映射器
│   │   ├── TranslationMapper.kt
│   │   ├── LanguageMapper.kt
│   │   ├── RegionalStyleMapper.kt
│   │   └── PreferencesMapper.kt
│   └── di/                                        # 数据层依赖注入
│       ├── DataModule.kt
│       ├── DatabaseModule.kt
│       ├── ApiModule.kt
│       └── RepositoryModule.kt
└── build.gradle.kts
```

### 5. feature模块 - 功能模块

#### feature/translation - 翻译功能
```
feature/translation/
├── src/main/java/com/cadence/feature/translation/
│   ├── presentation/
│   │   ├── screens/                               # 页面
│   │   │   ├── MainTranslationScreen.kt
│   │   │   ├── BatchTranslationScreen.kt
│   │   │   └── DialectTranslationScreen.kt
│   │   ├── components/                            # 组件
│   │   │   ├── LanguageSelectionRow.kt
│   │   │   ├── RegionalStyleSelector.kt
│   │   │   ├── TranslationInputCard.kt
│   │   │   ├── TranslationResultCard.kt
│   │   │   ├── VoiceInputButton.kt
│   │   │   ├── CameraInputButton.kt
│   │   │   └── TranslateButton.kt
│   │   ├── viewmodels/                            # 视图模型
│   │   │   ├── TranslationViewModel.kt
│   │   │   ├── BatchTranslationViewModel.kt
│   │   │   └── DialectTranslationViewModel.kt
│   │   └── navigation/                            # 导航
│   │       ├── TranslationNavigation.kt
│   │       └── TranslationDestinations.kt
│   └── di/
│       └── TranslationModule.kt
└── build.gradle.kts
```

#### feature/history - 历史记录功能
```
feature/history/
├── src/main/java/com/cadence/feature/history/
│   ├── presentation/
│   │   ├── screens/
│   │   │   ├── HistoryScreen.kt
│   │   │   ├── HistoryDetailScreen.kt
│   │   │   └── SearchHistoryScreen.kt
│   │   ├── components/
│   │   │   ├── HistoryListItem.kt
│   │   │   ├── HistorySearchBar.kt
│   │   │   ├── HistoryFilterChips.kt
│   │   │   └── HistoryEmptyState.kt
│   │   ├── viewmodels/
│   │   │   ├── HistoryViewModel.kt
│   │   │   └── HistoryDetailViewModel.kt
│   │   └── navigation/
│   │       └── HistoryNavigation.kt
│   └── di/
│       └── HistoryModule.kt
└── build.gradle.kts
```

#### feature/favorites - 收藏功能
```
feature/favorites/
├── src/main/java/com/cadence/feature/favorites/
│   ├── presentation/
│   │   ├── screens/
│   │   │   ├── FavoritesScreen.kt
│   │   │   └── FavoriteDetailScreen.kt
│   │   ├── components/
│   │   │   ├── FavoriteListItem.kt
│   │   │   ├── FavoriteTagChips.kt
│   │   │   └── FavoriteActions.kt
│   │   ├── viewmodels/
│   │   │   └── FavoritesViewModel.kt
│   │   └── navigation/
│   │       └── FavoritesNavigation.kt
│   └── di/
│       └── FavoritesModule.kt
└── build.gradle.kts
```

#### feature/settings - 设置功能
```
feature/settings/
├── src/main/java/com/cadence/feature/settings/
│   ├── presentation/
│   │   ├── screens/
│   │   │   ├── SettingsScreen.kt
│   │   │   ├── LanguageSettingsScreen.kt
│   │   │   ├── ThemeSettingsScreen.kt
│   │   │   ├── PrivacySettingsScreen.kt
│   │   │   └── AboutScreen.kt
│   │   ├── components/
│   │   │   ├── SettingsItem.kt
│   │   │   ├── SettingsSwitch.kt
│   │   │   ├── SettingsSlider.kt
│   │   │   └── SettingsDropdown.kt
│   │   ├── viewmodels/
│   │   │   └── SettingsViewModel.kt
│   │   └── navigation/
│   │       └── SettingsNavigation.kt
│   └── di/
│       └── SettingsModule.kt
└── build.gradle.kts
```

#### feature/learning - 语言学习功能
```
feature/learning/
├── src/main/java/com/cadence/feature/learning/
│   ├── presentation/
│   │   ├── screens/
│   │   │   ├── LearningModeScreen.kt
│   │   │   ├── LearningProgressScreen.kt
│   │   │   └── LearningExerciseScreen.kt
│   │   ├── components/
│   │   │   ├── LearningCard.kt
│   │   │   ├── ProgressIndicator.kt
│   │   │   └── ExerciseQuestion.kt
│   │   ├── viewmodels/
│   │   │   └── LearningViewModel.kt
│   │   └── navigation/
│   │       └── LearningNavigation.kt
│   └── di/
│       └── LearningModule.kt
└── build.gradle.kts
```

#### feature/cultural - 文化解释功能
```
feature/cultural/
├── src/main/java/com/cadence/feature/cultural/
│   ├── presentation/
│   │   ├── screens/
│   │   │   ├── CulturalExplanationScreen.kt
│   │   │   └── CulturalKnowledgeScreen.kt
│   │   ├── components/
│   │   │   ├── CulturalCard.kt
│   │   │   ├── CulturalTimeline.kt
│   │   │   └── CulturalReference.kt
│   │   ├── viewmodels/
│   │   │   └── CulturalViewModel.kt
│   │   └── navigation/
│   │       └── CulturalNavigation.kt
│   └── di/
│       └── CulturalModule.kt
└── build.gradle.kts
```

### 6. buildSrc模块 - 构建配置

```
buildSrc/
├── src/main/kotlin/
│   ├── Dependencies.kt                            # 依赖版本管理
│   ├── Versions.kt                                # 版本号定义
│   ├── BuildConfig.kt                             # 构建配置
│   └── Plugins.kt                                 # 插件配置
└── build.gradle.kts
```

### 7. 其他支持目录

```
docs/                                              # 项目文档
├── api/                                           # API文档
├── architecture/                                  # 架构文档
├── design/                                        # 设计文档
└── user-guide/                                    # 用户指南

scripts/                                           # 脚本文件
├── build.sh                                       # 构建脚本
├── test.sh                                        # 测试脚本
├── deploy.sh                                      # 部署脚本
└── setup.sh                                       # 环境设置脚本

gradle/                                            # Gradle配置
├── wrapper/                                       # Gradle Wrapper
└── libs.versions.toml                             # 版本目录
```

## 模块依赖关系

```
app
├── feature:translation
├── feature:history
├── feature:favorites
├── feature:settings
├── feature:learning
├── feature:cultural
├── core:ui
├── core:common
└── domain

feature:* (各功能模块)
├── core:ui
├── core:common
├── domain
└── data

data
├── core:network
├── core:database
├── core:common
└── domain

domain
└── core:common

core:ui
└── core:common

core:network
└── core:common

core:database
└── core:common
```

## 设计原则

### 1. 单一职责原则
每个模块都有明确的职责边界，避免功能重叠。

### 2. 依赖倒置原则
高层模块不依赖低层模块，都依赖于抽象接口。

### 3. 开闭原则
对扩展开放，对修改关闭，便于添加新功能。

### 4. 接口隔离原则
使用小而专一的接口，避免臃肿的大接口。

### 5. 模块化原则
每个功能模块可以独立开发、测试和维护。

这种目录结构设计确保了代码的可维护性、可测试性和可扩展性，为团队协作开发提供了清晰的指导。