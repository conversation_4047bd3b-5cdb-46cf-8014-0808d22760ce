package com.cadence.domain.model

import kotlinx.serialization.Serializable

/**
 * 用户偏好设置领域模型
 */
@Serializable
data class UserPreference(
    val id: String = "default_user",
    val defaultSourceLanguage: Language,
    val defaultTargetLanguage: Language,
    val autoDetectLanguage: Boolean = true,
    val saveTranslationHistory: Boolean = true,
    val enableCulturalContext: Boolean = true,
    val themeMode: ThemeMode = ThemeMode.SYSTEM,
    val fontSize: FontSize = FontSize.MEDIUM,
    val enableVoiceInput: Boolean = true,
    val enableVoiceOutput: Boolean = true,
    val enableOcr: Boolean = true,
    val createdAt: Long,
    val updatedAt: Long
)

/**
 * 主题模式枚举
 */
@Serializable
enum class ThemeMode {
    LIGHT,   // 浅色主题
    DARK,    // 深色主题
    SYSTEM   // 跟随系统
}

/**
 * 字体大小枚举
 */
@Serializable
enum class FontSize {
    SMALL,   // 小字体
    MEDIUM,  // 中等字体
    LARGE    // 大字体
}

/**
 * 语言偏好设置
 */
@Serializable
data class LanguagePreference(
    val language: Language,
    val isPreferred: Boolean = true,
    val usageCount: Int = 0,
    val lastUsedAt: Long? = null
)

/**
 * 翻译偏好设置
 */
@Serializable
data class TranslationPreference(
    val enableAutoTranslation: Boolean = false,
    val enableRealTimeTranslation: Boolean = false,
    val maxHistoryCount: Int = 1000,
    val cacheExpirationDays: Int = 30,
    val enableOfflineMode: Boolean = false
)

/**
 * 隐私设置
 */
@Serializable
data class PrivacySettings(
    val enableDataCollection: Boolean = true,
    val enableCrashReporting: Boolean = true,
    val enableAnalytics: Boolean = true,
    val autoDeleteHistoryDays: Int? = null // null表示不自动删除
)

/**
 * 通知设置
 */
@Serializable
data class NotificationSettings(
    val enableTranslationComplete: Boolean = true,
    val enableDailyReminder: Boolean = false,
    val enableWeeklyStats: Boolean = false,
    val reminderTime: String = "09:00" // HH:mm格式
)

/**
 * 完整的用户设置模型
 */
@Serializable
data class UserSettings(
    val userPreference: UserPreference,
    val languagePreferences: List<LanguagePreference> = emptyList(),
    val translationPreference: TranslationPreference = TranslationPreference(),
    val privacySettings: PrivacySettings = PrivacySettings(),
    val notificationSettings: NotificationSettings = NotificationSettings()
)
