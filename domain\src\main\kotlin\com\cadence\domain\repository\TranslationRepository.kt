package com.cadence.domain.repository

import com.cadence.domain.model.*
import kotlinx.coroutines.flow.Flow

/**
 * 翻译数据仓库接口
 * 定义翻译相关的数据操作契约
 */
interface TranslationRepository {
    
    /**
     * 执行翻译
     * @param request 翻译请求
     * @return 翻译结果
     */
    suspend fun translate(request: TranslationRequest): Result<TranslationResult>
    
    /**
     * 检测语言
     * @param text 待检测文本
     * @return 语言检测结果
     */
    suspend fun detectLanguage(text: String): Result<LanguageDetection>
    
    /**
     * 获取文化背景解释
     * @param translation 翻译对象
     * @return 文化背景信息
     */
    suspend fun getCulturalContext(translation: Translation): Result<String>
    
    /**
     * 保存翻译记录
     * @param translation 翻译对象
     */
    suspend fun saveTranslation(translation: Translation): Result<Unit>
    
    /**
     * 更新翻译记录
     * @param translation 翻译对象
     */
    suspend fun updateTranslation(translation: Translation): Result<Unit>
    
    /**
     * 删除翻译记录
     * @param translationId 翻译ID
     */
    suspend fun deleteTranslation(translationId: String): Result<Unit>
    
    /**
     * 根据ID获取翻译记录
     * @param translationId 翻译ID
     * @return 翻译对象
     */
    suspend fun getTranslationById(translationId: String): Result<Translation?>
    
    /**
     * 获取翻译历史记录
     * @param query 查询参数
     * @return 翻译记录流
     */
    fun getTranslationHistory(query: TranslationHistoryQuery = TranslationHistoryQuery()): Flow<List<Translation>>
    
    /**
     * 获取收藏的翻译记录
     * @return 收藏翻译记录流
     */
    fun getFavoriteTranslations(): Flow<List<Translation>>
    
    /**
     * 搜索翻译记录
     * @param searchQuery 搜索关键词
     * @return 搜索结果流
     */
    fun searchTranslations(searchQuery: String): Flow<List<Translation>>
    
    /**
     * 更新收藏状态
     * @param translationId 翻译ID
     * @param isFavorite 是否收藏
     */
    suspend fun updateFavoriteStatus(translationId: String, isFavorite: Boolean): Result<Unit>
    
    /**
     * 获取翻译统计信息
     * @return 统计信息
     */
    suspend fun getTranslationStatistics(): Result<TranslationStatistics>
    
    /**
     * 清空翻译历史
     */
    suspend fun clearTranslationHistory(): Result<Unit>
    
    /**
     * 删除指定时间之前的翻译记录
     * @param timestamp 时间戳
     */
    suspend fun deleteTranslationsBefore(timestamp: Long): Result<Unit>
    
    /**
     * 导出翻译历史
     * @param format 导出格式
     * @return 导出文件路径
     */
    suspend fun exportTranslationHistory(format: ExportFormat): Result<String>
    
    /**
     * 导入翻译历史
     * @param filePath 文件路径
     * @return 导入结果
     */
    suspend fun importTranslationHistory(filePath: String): Result<ImportResult>
}

/**
 * 导出格式枚举
 */
enum class ExportFormat {
    JSON,
    CSV,
    TXT
}

/**
 * 导入结果
 */
data class ImportResult(
    val totalCount: Int,
    val successCount: Int,
    val failureCount: Int,
    val errors: List<String> = emptyList()
)
