package com.cadence.cadence

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import com.cadence.cadence.navigation.CadenceNavigation
import com.cadence.cadence.ui.theme.CadenceTheme
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

/**
 * Cadence应用程序主Activity
 * 
 * 负责：
 * - 设置应用主题和UI
 * - 配置导航系统
 * - 处理系统UI（状态栏、导航栏）
 * - 启动画面管理
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        // 安装启动画面
        val splashScreen = installSplashScreen()
        
        super.onCreate(savedInstanceState)
        
        // 启用边到边显示
        enableEdgeToEdge()
        
        Timber.d("MainActivity创建完成")
        
        setContent {
            CadenceTheme {
                CadenceApp()
            }
        }
    }
}

/**
 * Cadence应用程序主Composable
 */
@Composable
private fun CadenceApp() {
    val systemUiController = rememberSystemUiController()
    
    // 配置系统UI颜色
    systemUiController.setSystemBarsColor(
        color = MaterialTheme.colorScheme.background,
        darkIcons = !isSystemInDarkTheme()
    )
    
    Surface(
        modifier = Modifier.fillMaxSize(),
        color = MaterialTheme.colorScheme.background
    ) {
        CadenceNavigation()
    }
}

/**
 * 检查系统是否处于深色主题模式
 */
@Composable
private fun isSystemInDarkTheme(): Boolean {
    return androidx.compose.foundation.isSystemInDarkTheme()
}