# Gemini API 集成文档

## 概述

本文档描述了地方特色翻译应用与Google Gemini API的集成方案，包括API调用、提示词策略、错误处理和性能优化。

## API配置

### 1. 基础配置
```kotlin
// core/network/client/GeminiClient.kt
@Singleton
class GeminiClient @Inject constructor(
    private val httpClient: OkHttpClient,
    private val apiKeyManager: ApiKeyManager
) {
    companion object {
        private const val BASE_URL = "https://generativelanguage.googleapis.com/"
        private const val API_VERSION = "v1beta"
        private const val MODEL_NAME = "gemini-1.5-pro"
    }
    
    private val retrofit = Retrofit.Builder()
        .baseUrl(BASE_URL)
        .client(httpClient)
        .addConverterFactory(KotlinxSerializationConverterFactory.create())
        .build()
    
    val apiService: GeminiApiService = retrofit.create()
}
```

### 2. API服务接口
```kotlin
// data/remote/api/GeminiApiService.kt
interface GeminiApiService {
    @POST("v1beta/models/{model}:generateContent")
    suspend fun generateContent(
        @Path("model") model: String = "gemini-1.5-pro",
        @Query("key") apiKey: String,
        @Body request: GenerateContentRequest
    ): GenerateContentResponse
    
    @POST("v1beta/models/{model}:countTokens")
    suspend fun countTokens(
        @Path("model") model: String = "gemini-1.5-pro",
        @Query("key") apiKey: String,
        @Body request: CountTokensRequest
    ): CountTokensResponse
}
```

### 3. 数据传输对象
```kotlin
// data/remote/dto/GeminiDto.kt
@Serializable
data class GenerateContentRequest(
    val contents: List<Content>,
    val generationConfig: GenerationConfig? = null,
    val safetySettings: List<SafetySetting>? = null
)

@Serializable
data class Content(
    val parts: List<Part>,
    val role: String = "user"
)

@Serializable
data class Part(
    val text: String
)

@Serializable
data class GenerationConfig(
    val temperature: Float = 0.7f,
    val topK: Int = 40,
    val topP: Float = 0.95f,
    val maxOutputTokens: Int = 2048,
    val stopSequences: List<String>? = null
)

@Serializable
data class SafetySetting(
    val category: String,
    val threshold: String = "BLOCK_MEDIUM_AND_ABOVE"
)

@Serializable
data class GenerateContentResponse(
    val candidates: List<Candidate>,
    val promptFeedback: PromptFeedback? = null,
    val usageMetadata: UsageMetadata? = null
)

@Serializable
data class Candidate(
    val content: Content,
    val finishReason: String? = null,
    val index: Int = 0,
    val safetyRatings: List<SafetyRating>? = null
)

@Serializable
data class UsageMetadata(
    val promptTokenCount: Int,
    val candidatesTokenCount: Int,
    val totalTokenCount: Int
)
```

## 地方特色提示词策略

### 1. 提示词生成器
```kotlin
// data/remote/engines/RegionalPromptGenerator.kt
@Singleton
class RegionalPromptGenerator @Inject constructor() {
    
    fun generateTranslationPrompt(
        text: String,
        sourceLanguage: Language,
        targetLanguage: Language,
        regionalStyle: RegionalStyle,
        context: TranslationContext
    ): String {
        return buildString {
            appendLine("你是一位精通多种语言和地方方言的翻译专家。")
            appendLine()
            
            // 基础翻译要求
            appendLine("请将以下${sourceLanguage.displayName}文本翻译为${targetLanguage.displayName}：")
            appendLine("原文：$text")
            appendLine()
            
            // 地方特色要求
            appendRegionalStyleInstructions(targetLanguage, regionalStyle)
            
            // 上下文要求
            appendContextInstructions(context)
            
            // 输出格式要求
            appendOutputFormatInstructions()
        }
    }
    
    private fun StringBuilder.appendRegionalStyleInstructions(
        language: Language,
        style: RegionalStyle
    ) {
        appendLine("地方特色要求：")
        when (language.code) {
            "zh" -> appendChineseStyleInstructions(style)
            "en" -> appendEnglishStyleInstructions(style)
            "ja" -> appendJapaneseStyleInstructions(style)
            "ko" -> appendKoreanStyleInstructions(style)
        }
        appendLine()
    }
    
    private fun StringBuilder.appendChineseStyleInstructions(style: RegionalStyle) {
        when (style.id) {
            "beijing" -> {
                appendLine("- 使用北京话的特色表达方式")
                appendLine("- 适当使用儿化音（如：哪儿、这儿）")
                appendLine("- 体现北京话的亲切和幽默感")
                appendLine("- 使用北京话特有的语气词（如：嘛、呗、咧）")
            }
            "shanghai" -> {
                appendLine("- 体现上海话的精致和委婉")
                appendLine("- 使用上海地区的特色词汇")
                appendLine("- 保持上海话的优雅表达风格")
            }
            "cantonese" -> {
                appendLine("- 使用粤语的表达习惯")
                appendLine("- 体现广东话的生动和直接")
                appendLine("- 适当使用粤语特色词汇")
            }
            "taiwanese" -> {
                appendLine("- 使用台湾地区的用词习惯")
                appendLine("- 体现台湾腔的温和礼貌")
                appendLine("- 使用台湾特有的表达方式")
            }
            "northeastern" -> {
                appendLine("- 使用东北话的豪爽表达")
                appendLine("- 体现东北话的幽默和直率")
                appendLine("- 适当使用东北方言词汇")
            }
        }
    }
    
    private fun StringBuilder.appendEnglishStyleInstructions(style: RegionalStyle) {
        when (style.id) {
            "american_ny" -> {
                appendLine("- 使用纽约地区的美式英语表达")
                appendLine("- 体现纽约人的直接和快节奏风格")
                appendLine("- 使用美式拼写和词汇")
            }
            "british_london" -> {
                appendLine("- 使用伦敦地区的英式英语")
                appendLine("- 体现英式英语的正式和礼貌")
                appendLine("- 使用英式拼写和词汇")
            }
            "australian" -> {
                appendLine("- 使用澳式英语的表达方式")
                appendLine("- 体现澳洲英语的轻松和友好")
                appendLine("- 适当使用澳式俚语")
            }
        }
    }
    
    private fun StringBuilder.appendContextInstructions(context: TranslationContext) {
        appendLine("使用场景：${context.scenario}")
        when (context.scenario) {
            "formal" -> appendLine("- 使用正式、礼貌的表达方式")
            "casual" -> appendLine("- 使用轻松、日常的表达方式")
            "business" -> appendLine("- 使用商务、专业的表达方式")
            "travel" -> appendLine("- 使用旅行场景的实用表达")
        }
        appendLine()
    }
    
    private fun StringBuilder.appendOutputFormatInstructions() {
        appendLine("输出格式要求：")
        appendLine("1. 提供翻译结果")
        appendLine("2. 如果涉及文化背景，请提供简要解释")
        appendLine("3. 标注翻译的置信度（1-10分）")
        appendLine("4. 如有发音指导，请提供拼音或音标")
        appendLine()
        appendLine("请按以下JSON格式输出：")
        appendLine("""
        {
          "translation": "翻译结果",
          "cultural_explanation": "文化背景解释（如适用）",
          "confidence": 9,
          "pronunciation": "发音指导（如适用）",
          "style_notes": "地方特色说明"
        }
        """.trimIndent())
    }
}
```

### 2. 翻译引擎实现
```kotlin
// data/remote/engines/GeminiTranslationEngine.kt
@Singleton
class GeminiTranslationEngine @Inject constructor(
    private val apiService: GeminiApiService,
    private val promptGenerator: RegionalPromptGenerator,
    private val apiKeyManager: ApiKeyManager,
    private val responseParser: GeminiResponseParser
) : TranslationEngine {
    
    override suspend fun translate(
        text: String,
        sourceLanguage: Language,
        targetLanguage: Language,
        regionalStyle: RegionalStyle,
        context: TranslationContext
    ): TranslationResult {
        try {
            // 生成提示词
            val prompt = promptGenerator.generateTranslationPrompt(
                text, sourceLanguage, targetLanguage, regionalStyle, context
            )
            
            // 构建请求
            val request = GenerateContentRequest(
                contents = listOf(
                    Content(
                        parts = listOf(Part(prompt))
                    )
                ),
                generationConfig = GenerationConfig(
                    temperature = 0.3f, // 较低的温度确保一致性
                    maxOutputTokens = 1024
                ),
                safetySettings = createSafetySettings()
            )
            
            // 调用API
            val response = apiService.generateContent(
                apiKey = apiKeyManager.getApiKey(),
                request = request
            )
            
            // 解析响应
            return responseParser.parseTranslationResponse(
                response, text, sourceLanguage, targetLanguage, regionalStyle
            )
            
        } catch (e: Exception) {
            throw mapToTranslationException(e)
        }
    }
    
    override suspend fun detectLanguage(text: String): Language {
        val prompt = """
            请检测以下文本的语言，并返回语言代码：
            文本：$text
            
            请只返回语言代码（zh/en/ja/ko等），不要其他内容。
        """.trimIndent()
        
        val request = GenerateContentRequest(
            contents = listOf(Content(parts = listOf(Part(prompt)))),
            generationConfig = GenerationConfig(temperature = 0.1f, maxOutputTokens = 10)
        )
        
        val response = apiService.generateContent(
            apiKey = apiKeyManager.getApiKey(),
            request = request
        )
        
        val languageCode = response.candidates.firstOrNull()
            ?.content?.parts?.firstOrNull()?.text?.trim() ?: "unknown"
            
        return Language.fromCode(languageCode)
    }
    
    private fun createSafetySettings(): List<SafetySetting> {
        return listOf(
            SafetySetting("HARM_CATEGORY_HARASSMENT", "BLOCK_MEDIUM_AND_ABOVE"),
            SafetySetting("HARM_CATEGORY_HATE_SPEECH", "BLOCK_MEDIUM_AND_ABOVE"),
            SafetySetting("HARM_CATEGORY_SEXUALLY_EXPLICIT", "BLOCK_MEDIUM_AND_ABOVE"),
            SafetySetting("HARM_CATEGORY_DANGEROUS_CONTENT", "BLOCK_MEDIUM_AND_ABOVE")
        )
    }
    
    private fun mapToTranslationException(exception: Exception): TranslationException {
        return when (exception) {
            is HttpException -> when (exception.code()) {
                400 -> TranslationException.InvalidInput
                401 -> TranslationException.ApiKeyInvalid
                403 -> TranslationException.QuotaExceeded
                429 -> TranslationException.RateLimitExceeded
                else -> TranslationException.ApiError(exception.code(), exception.message())
            }
            is SocketTimeoutException -> TranslationException.NetworkTimeout
            is UnknownHostException -> TranslationException.NetworkUnavailable
            else -> TranslationException.UnknownError(exception.message ?: "Unknown error")
        }
    }
}
```

### 3. 响应解析器
```kotlin
// data/remote/engines/GeminiResponseParser.kt
@Singleton
class GeminiResponseParser @Inject constructor(
    private val json: Json
) {
    
    @Serializable
    private data class GeminiTranslationResponse(
        val translation: String,
        val cultural_explanation: String? = null,
        val confidence: Int = 8,
        val pronunciation: String? = null,
        val style_notes: String? = null
    )
    
    fun parseTranslationResponse(
        response: GenerateContentResponse,
        originalText: String,
        sourceLanguage: Language,
        targetLanguage: Language,
        regionalStyle: RegionalStyle
    ): TranslationResult {
        val candidate = response.candidates.firstOrNull()
            ?: throw TranslationException.EmptyResponse
            
        val responseText = candidate.content.parts.firstOrNull()?.text
            ?: throw TranslationException.EmptyResponse
            
        return try {
            // 尝试解析JSON格式的响应
            val parsedResponse = json.decodeFromString<GeminiTranslationResponse>(
                extractJsonFromResponse(responseText)
            )
            
            TranslationResult(
                originalText = originalText,
                translatedText = parsedResponse.translation,
                sourceLanguage = sourceLanguage,
                targetLanguage = targetLanguage,
                regionalStyle = regionalStyle,
                confidence = parsedResponse.confidence / 10.0f,
                culturalExplanation = parsedResponse.cultural_explanation,
                pronunciationGuide = parsedResponse.pronunciation,
                styleNotes = parsedResponse.style_notes,
                timestamp = System.currentTimeMillis(),
                tokenUsage = response.usageMetadata?.totalTokenCount ?: 0
            )
        } catch (e: Exception) {
            // 如果JSON解析失败，使用简单文本解析
            parseSimpleTextResponse(
                responseText, originalText, sourceLanguage, targetLanguage, regionalStyle
            )
        }
    }
    
    private fun extractJsonFromResponse(responseText: String): String {
        // 提取JSON部分，处理可能的markdown格式
        val jsonStart = responseText.indexOf('{')
        val jsonEnd = responseText.lastIndexOf('}')
        
        if (jsonStart != -1 && jsonEnd != -1 && jsonEnd > jsonStart) {
            return responseText.substring(jsonStart, jsonEnd + 1)
        }
        
        throw TranslationException.InvalidResponseFormat
    }
    
    private fun parseSimpleTextResponse(
        responseText: String,
        originalText: String,
        sourceLanguage: Language,
        targetLanguage: Language,
        regionalStyle: RegionalStyle
    ): TranslationResult {
        // 简单文本解析逻辑
        val lines = responseText.lines().filter { it.isNotBlank() }
        val translation = lines.firstOrNull() ?: responseText.trim()
        
        return TranslationResult(
            originalText = originalText,
            translatedText = translation,
            sourceLanguage = sourceLanguage,
            targetLanguage = targetLanguage,
            regionalStyle = regionalStyle,
            confidence = 0.7f, // 默认置信度
            culturalExplanation = null,
            pronunciationGuide = null,
            styleNotes = null,
            timestamp = System.currentTimeMillis(),
            tokenUsage = 0
        )
    }
}
```

## 错误处理和重试机制

### 1. 异常定义
```kotlin
// domain/exceptions/TranslationException.kt
sealed class TranslationException : Exception() {
    object InvalidInput : TranslationException()
    object ApiKeyInvalid : TranslationException()
    object QuotaExceeded : TranslationException()
    object RateLimitExceeded : TranslationException()
    object NetworkTimeout : TranslationException()
    object NetworkUnavailable : TranslationException()
    object EmptyResponse : TranslationException()
    object InvalidResponseFormat : TranslationException()
    data class ApiError(val code: Int, val message: String) : TranslationException()
    data class UnknownError(val message: String) : TranslationException()
}
```

### 2. 重试策略
```kotlin
// data/remote/engines/RetryStrategy.kt
@Singleton
class RetryStrategy @Inject constructor() {
    
    suspend fun <T> executeWithRetry(
        maxRetries: Int = 3,
        initialDelay: Long = 1000L,
        maxDelay: Long = 10000L,
        factor: Double = 2.0,
        operation: suspend () -> T
    ): T {
        var currentDelay = initialDelay
        repeat(maxRetries) { attempt ->
            try {
                return operation()
            } catch (e: Exception) {
                if (attempt == maxRetries - 1 || !shouldRetry(e)) {
                    throw e
                }
                
                delay(currentDelay)
                currentDelay = (currentDelay * factor).toLong().coerceAtMost(maxDelay)
            }
        }
        throw IllegalStateException("Retry logic error")
    }
    
    private fun shouldRetry(exception: Exception): Boolean {
        return when (exception) {
            is TranslationException.NetworkTimeout -> true
            is TranslationException.NetworkUnavailable -> true
            is TranslationException.RateLimitExceeded -> true
            is TranslationException.ApiError -> exception.code >= 500
            else -> false
        }
    }
}
```

## 性能优化

### 1. 请求优化
```kotlin
// data/remote/engines/RequestOptimizer.kt
@Singleton
class RequestOptimizer @Inject constructor() {
    
    fun optimizePrompt(prompt: String): String {
        // 移除不必要的空白字符
        return prompt.lines()
            .map { it.trim() }
            .filter { it.isNotEmpty() }
            .joinToString("\n")
    }
    
    fun calculateOptimalTokenLimit(textLength: Int): Int {
        // 根据输入文本长度动态调整输出token限制
        return when {
            textLength < 100 -> 512
            textLength < 500 -> 1024
            textLength < 1000 -> 1536
            else -> 2048
        }
    }
    
    fun shouldUseCache(text: String): Boolean {
        // 判断是否应该使用缓存
        return text.length > 10 && text.length < 1000
    }
}
```

### 2. 批量处理
```kotlin
// data/remote/engines/BatchTranslationEngine.kt
@Singleton
class BatchTranslationEngine @Inject constructor(
    private val geminiEngine: GeminiTranslationEngine,
    private val coroutineScope: CoroutineScope
) {
    
    suspend fun translateBatch(
        texts: List<String>,
        sourceLanguage: Language,
        targetLanguage: Language,
        regionalStyle: RegionalStyle,
        context: TranslationContext,
        onProgress: (Int, Int) -> Unit = { _, _ -> }
    ): List<TranslationResult> {
        return texts.mapIndexed { index, text ->
            async {
                try {
                    val result = geminiEngine.translate(
                        text, sourceLanguage, targetLanguage, regionalStyle, context
                    )
                    onProgress(index + 1, texts.size)
                    result
                } catch (e: Exception) {
                    // 创建错误结果
                    TranslationResult.error(text, e)
                }
            }
        }.awaitAll()
    }
}
```

## 配额和限制管理

### 1. 配额监控
```kotlin
// data/remote/monitoring/QuotaMonitor.kt
@Singleton
class QuotaMonitor @Inject constructor(
    private val preferences: SharedPreferences
) {
    private val dailyRequestCount = AtomicInteger(0)
    private val monthlyTokenCount = AtomicLong(0)
    
    fun recordRequest(tokenCount: Int) {
        dailyRequestCount.incrementAndGet()
        monthlyTokenCount.addAndGet(tokenCount.toLong())
        
        // 保存到本地存储
        preferences.edit()
            .putInt("daily_requests", dailyRequestCount.get())
            .putLong("monthly_tokens", monthlyTokenCount.get())
            .apply()
    }
    
    fun checkQuotaAvailable(): Boolean {
        val dailyLimit = 1000 // 每日请求限制
        val monthlyTokenLimit = 1000000L // 每月token限制
        
        return dailyRequestCount.get() < dailyLimit && 
               monthlyTokenCount.get() < monthlyTokenLimit
    }
    
    fun getRemainingQuota(): QuotaInfo {
        return QuotaInfo(
            dailyRequestsRemaining = maxOf(0, 1000 - dailyRequestCount.get()),
            monthlyTokensRemaining = maxOf(0, 1000000L - monthlyTokenCount.get())
        )
    }
}

data class QuotaInfo(
    val dailyRequestsRemaining: Int,
    val monthlyTokensRemaining: Long
)
```

### 2. 降级策略
```kotlin
// data/remote/engines/FallbackEngine.kt
@Singleton
class FallbackEngine @Inject constructor(
    private val offlineEngine: OfflineTranslationEngine,
    private val cacheRepository: CacheRepository
) {
    
    suspend fun translateWithFallback(
        text: String,
        sourceLanguage: Language,
        targetLanguage: Language,
        regionalStyle: RegionalStyle,
        context: TranslationContext
    ): TranslationResult {
        // 1. 尝试从缓存获取
        val cached = cacheRepository.getTranslation(text, sourceLanguage, targetLanguage, regionalStyle)
        if (cached != null) {
            return cached
        }
        
        // 2. 尝试离线翻译
        return try {
            offlineEngine.translate(text, sourceLanguage, targetLanguage, regionalStyle, context)
        } catch (e: Exception) {
            // 3. 返回基础翻译结果
            TranslationResult.basic(text, "翻译服务暂时不可用")
        }
    }
}
```

这个Gemini API集成方案提供了完整的翻译服务，包括地方特色提示词、错误处理、性能优化和配额管理，确保应用的稳定性和用户体验。