package com.cadence.feature.translation.presentation.component

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.cadence.core.speech.TextToSpeechService

/**
 * 语音播放按钮组件
 * 提供文字转语音播放功能
 */
@Composable
fun SpeechPlayButton(
    text: String,
    language: String,
    isPlaying: Boolean,
    isAvailable: Boolean,
    onPlay: (String, String) -> Unit,
    onStop: () -> Unit,
    modifier: Modifier = Modifier,
    ttsResult: TextToSpeechService.TtsResult? = null,
    size: ButtonSize = ButtonSize.Medium
) {
    // 动画状态
    val scale by animateFloatAsState(
        targetValue = if (isPlaying) 1.1f else 1.0f,
        animationSpec = tween(200),
        label = "scale"
    )
    
    val infiniteTransition = rememberInfiniteTransition(label = "infinite")
    val rotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "rotation"
    )
    
    when (size) {
        ButtonSize.Small -> {
            IconButton(
                onClick = {
                    if (isPlaying) {
                        onStop()
                    } else {
                        onPlay(text, language)
                    }
                },
                enabled = isAvailable && text.isNotBlank(),
                modifier = modifier.scale(scale)
            ) {
                Icon(
                    imageVector = when {
                        !isAvailable -> Icons.Default.VolumeOff
                        isPlaying -> Icons.Default.Stop
                        else -> Icons.Default.VolumeUp
                    },
                    contentDescription = when {
                        !isAvailable -> "语音播放不可用"
                        isPlaying -> "停止播放"
                        else -> "播放语音"
                    },
                    tint = when {
                        !isAvailable -> MaterialTheme.colorScheme.outline
                        isPlaying -> MaterialTheme.colorScheme.error
                        else -> MaterialTheme.colorScheme.primary
                    }
                )
            }
        }
        
        ButtonSize.Medium -> {
            OutlinedButton(
                onClick = {
                    if (isPlaying) {
                        onStop()
                    } else {
                        onPlay(text, language)
                    }
                },
                enabled = isAvailable && text.isNotBlank(),
                modifier = modifier.scale(scale),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = when {
                        !isAvailable -> MaterialTheme.colorScheme.outline
                        isPlaying -> MaterialTheme.colorScheme.error
                        else -> MaterialTheme.colorScheme.primary
                    }
                )
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = when {
                            !isAvailable -> Icons.Default.VolumeOff
                            isPlaying -> Icons.Default.Stop
                            else -> Icons.Default.VolumeUp
                        },
                        contentDescription = null,
                        modifier = if (isPlaying && ttsResult is TextToSpeechService.TtsResult.Started) {
                            Modifier.graphicsLayer { rotationZ = rotation }
                        } else {
                            Modifier
                        }
                    )
                    
                    Text(
                        text = when {
                            !isAvailable -> "不可用"
                            isPlaying -> "停止"
                            else -> "播放"
                        }
                    )
                }
            }
        }
        
        ButtonSize.Large -> {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(8.dp),
                modifier = modifier
            ) {
                FloatingActionButton(
                    onClick = {
                        if (isPlaying) {
                            onStop()
                        } else {
                            onPlay(text, language)
                        }
                    },
                    modifier = Modifier
                        .size(56.dp)
                        .scale(scale),
                    containerColor = when {
                        !isAvailable -> MaterialTheme.colorScheme.outline
                        isPlaying -> MaterialTheme.colorScheme.error
                        else -> MaterialTheme.colorScheme.primary
                    }
                ) {
                    Icon(
                        imageVector = when {
                            !isAvailable -> Icons.Default.VolumeOff
                            isPlaying -> Icons.Default.Stop
                            else -> Icons.Default.VolumeUp
                        },
                        contentDescription = when {
                            !isAvailable -> "语音播放不可用"
                            isPlaying -> "停止播放"
                            else -> "播放语音"
                        },
                        tint = Color.White,
                        modifier = if (isPlaying && ttsResult is TextToSpeechService.TtsResult.Started) {
                            Modifier.graphicsLayer { rotationZ = rotation }
                        } else {
                            Modifier
                        }
                    )
                }
                
                // 状态文本
                AnimatedContent(
                    targetState = getStatusText(isPlaying, isAvailable, ttsResult),
                    transitionSpec = {
                        fadeIn(animationSpec = tween(300)) togetherWith 
                        fadeOut(animationSpec = tween(300))
                    },
                    label = "status_text"
                ) { statusText ->
                    Text(
                        text = statusText,
                        style = MaterialTheme.typography.labelMedium,
                        color = when {
                            !isAvailable -> MaterialTheme.colorScheme.outline
                            isPlaying -> MaterialTheme.colorScheme.primary
                            else -> MaterialTheme.colorScheme.onSurfaceVariant
                        },
                        fontWeight = if (isPlaying) FontWeight.Medium else FontWeight.Normal
                    )
                }
            }
        }
    }
}

/**
 * 按钮尺寸枚举
 */
enum class ButtonSize {
    Small,   // 仅图标
    Medium,  // 图标+文字按钮
    Large    // 大型FAB+状态文字
}

/**
 * 获取播放状态文本
 */
private fun getStatusText(
    isPlaying: Boolean,
    isAvailable: Boolean,
    ttsResult: TextToSpeechService.TtsResult?
): String {
    return when {
        !isAvailable -> "播放不可用"
        isPlaying -> {
            when (ttsResult) {
                is TextToSpeechService.TtsResult.Started -> "正在播放..."
                is TextToSpeechService.TtsResult.Progress -> "播放中..."
                else -> "正在播放..."
            }
        }
        else -> "点击播放语音"
    }
}

/**
 * 语音播放进度指示器
 */
@Composable
fun SpeechPlayProgress(
    isPlaying: Boolean,
    progress: TextToSpeechService.TtsResult.Progress?,
    modifier: Modifier = Modifier
) {
    AnimatedVisibility(
        visible = isPlaying && progress != null,
        enter = fadeIn() + expandVertically(),
        exit = fadeOut() + shrinkVertically(),
        modifier = modifier
    ) {
        progress?.let { progressData ->
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.5f)
                )
            ) {
                Column(
                    modifier = Modifier.padding(12.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "播放进度",
                            style = MaterialTheme.typography.labelMedium,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                        
                        Text(
                            text = "${progressData.start}-${progressData.end}",
                            style = MaterialTheme.typography.labelSmall,
                            color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                        )
                    }
                    
                    LinearProgressIndicator(
                        progress = if (progressData.end > progressData.start) {
                            (progressData.frame - progressData.start).toFloat() / 
                            (progressData.end - progressData.start).toFloat()
                        } else {
                            0f
                        },
                        modifier = Modifier.fillMaxWidth(),
                        color = MaterialTheme.colorScheme.primary,
                        trackColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.3f)
                    )
                }
            }
        }
    }
}

/**
 * TTS错误提示
 */
@Composable
fun TtsErrorSnackbar(
    error: TextToSpeechService.TtsResult.Error?,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    error?.let {
        Card(
            modifier = modifier
                .fillMaxWidth()
                .padding(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.errorContainer
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = Icons.Default.Error,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onErrorContainer
                    )
                    Text(
                        text = "语音播放失败: ${error.message}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
                
                IconButton(onClick = onDismiss) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "关闭",
                        tint = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
        }
    }
}
