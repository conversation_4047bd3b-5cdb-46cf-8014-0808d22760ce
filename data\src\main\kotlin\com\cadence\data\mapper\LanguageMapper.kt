package com.cadence.data.mapper

import com.cadence.core.database.entity.LanguageRegionEntity
import com.cadence.domain.model.Language
import com.cadence.domain.model.Region

/**
 * 语言数据映射器
 * 负责语言相关数据库实体与领域模型之间的转换
 */
object LanguageMapper {
    
    /**
     * 将数据库实体转换为语言领域模型
     */
    fun LanguageRegionEntity.toLanguage(): Language {
        return Language(
            code = languageCode,
            name = languageName,
            region = if (regionCode != null) {
                Region(
                    code = regionCode,
                    name = regionName ?: "",
                    dialectName = dialectName,
                    culturalInfo = culturalInfo
                )
            } else null
        )
    }
    
    /**
     * 将语言领域模型转换为数据库实体
     */
    fun Language.toEntity(): LanguageRegionEntity {
        return LanguageRegionEntity(
            id = "${code}_${region?.code ?: "default"}",
            languageCode = code,
            languageName = name,
            regionCode = region?.code,
            regionName = region?.name,
            dialectName = region?.dialectName,
            culturalInfo = region?.culturalInfo,
            isSupported = true,
            isPopular = false,
            usageCount = 0,
            lastUsedAt = null,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis()
        )
    }
    
    /**
     * 将数据库实体转换为地区领域模型
     */
    fun LanguageRegionEntity.toRegion(): Region? {
        return if (regionCode != null) {
            Region(
                code = regionCode,
                name = regionName ?: "",
                dialectName = dialectName,
                culturalInfo = culturalInfo
            )
        } else null
    }
    
    /**
     * 批量转换数据库实体列表为语言领域模型列表
     */
    fun List<LanguageRegionEntity>.toLanguageList(): List<Language> {
        return map { it.toLanguage() }
    }
    
    /**
     * 批量转换语言领域模型列表为数据库实体列表
     */
    fun List<Language>.toEntityList(): List<LanguageRegionEntity> {
        return map { it.toEntity() }
    }
    
    /**
     * 获取不重复的语言列表（去除地区重复）
     */
    fun List<LanguageRegionEntity>.toUniqueLanguageList(): List<Language> {
        return groupBy { it.languageCode }
            .map { (_, entities) ->
                val mainEntity = entities.first()
                Language(
                    code = mainEntity.languageCode,
                    name = mainEntity.languageName,
                    region = null // 不包含地区信息的基础语言
                )
            }
    }
    
    /**
     * 根据语言代码获取所有地区
     */
    fun List<LanguageRegionEntity>.getRegionsByLanguageCode(languageCode: String): List<Region> {
        return filter { it.languageCode == languageCode && it.regionCode != null }
            .mapNotNull { it.toRegion() }
    }
    
    /**
     * 创建默认语言实体
     */
    fun createDefaultLanguageEntity(
        languageCode: String,
        languageName: String,
        regionCode: String? = null,
        regionName: String? = null,
        dialectName: String? = null,
        isPopular: Boolean = false
    ): LanguageRegionEntity {
        return LanguageRegionEntity(
            id = "${languageCode}_${regionCode ?: "default"}",
            languageCode = languageCode,
            languageName = languageName,
            regionCode = regionCode,
            regionName = regionName,
            dialectName = dialectName,
            culturalInfo = null,
            isSupported = true,
            isPopular = isPopular,
            usageCount = 0,
            lastUsedAt = null,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis()
        )
    }
}
