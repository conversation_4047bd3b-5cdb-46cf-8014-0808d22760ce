package com.cadence.domain.usecase

import com.cadence.core.speech.TextToSpeechService
import com.cadence.domain.model.Language
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

/**
 * 文字转语音用例
 * 处理文字转语音相关的业务逻辑
 */
class TextToSpeechUseCase @Inject constructor(
    private val textToSpeechService: TextToSpeechService
) {
    
    /**
     * TTS播放结果
     */
    sealed class TtsResult {
        object Started : TtsResult()
        object Completed : TtsResult()
        object Stopped : TtsResult()
        data class Progress(val start: Int, val end: Int, val frame: Int) : TtsResult()
        data class Error(val message: String, val errorCode: Int) : TtsResult()
    }
    
    /**
     * TTS配置
     */
    data class TtsConfig(
        val language: Language,
        val speechRate: Float = 1.0f,
        val pitch: Float = 1.0f,
        val volume: Float = 1.0f
    )
    
    /**
     * 初始化TTS引擎
     */
    suspend fun initialize(): Boolean {
        return textToSpeechService.initialize()
    }
    
    /**
     * 检查TTS是否可用
     */
    fun isAvailable(): Boolean {
        return textToSpeechService.isAvailable()
    }
    
    /**
     * 获取支持的语言列表
     */
    fun getSupportedLanguages(): List<Language> {
        val supportedCodes = textToSpeechService.getSupportedLanguages()
        return supportedCodes.map { code ->
            Language(
                code = code,
                name = textToSpeechService.getLanguageDisplayName(code)
            )
        }
    }
    
    /**
     * 朗读文本
     * @param text 要朗读的文本
     * @param config TTS配置
     * @return 播放状态流
     */
    fun speak(text: String, config: TtsConfig): Flow<TtsResult> {
        val ttsConfig = TextToSpeechService.TtsConfig(
            language = textToSpeechService.mapTranslationLanguageToTts(config.language.code),
            speechRate = config.speechRate,
            pitch = config.pitch,
            volume = config.volume
        )
        
        return textToSpeechService.speak(text, ttsConfig).map { result ->
            when (result) {
                is TextToSpeechService.TtsResult.Started -> TtsResult.Started
                is TextToSpeechService.TtsResult.Completed -> TtsResult.Completed
                is TextToSpeechService.TtsResult.Stopped -> TtsResult.Stopped
                is TextToSpeechService.TtsResult.Progress -> 
                    TtsResult.Progress(result.start, result.end, result.frame)
                is TextToSpeechService.TtsResult.Error -> 
                    TtsResult.Error(result.message, result.errorCode)
            }
        }
    }
    
    /**
     * 停止播放
     */
    fun stop() {
        textToSpeechService.stop()
    }
    
    /**
     * 检查是否正在播放
     */
    fun isSpeaking(): Boolean {
        return textToSpeechService.isSpeaking()
    }
    
    /**
     * 根据语言获取推荐的TTS配置
     * @param language 语言
     * @return 推荐配置
     */
    fun getRecommendedConfig(language: Language): TtsConfig {
        val serviceConfig = textToSpeechService.getRecommendedConfig(
            textToSpeechService.mapTranslationLanguageToTts(language.code)
        )
        
        return TtsConfig(
            language = language,
            speechRate = serviceConfig.speechRate,
            pitch = serviceConfig.pitch,
            volume = serviceConfig.volume
        )
    }
    
    /**
     * 验证TTS配置
     * @param config 配置
     * @return 验证结果和错误信息
     */
    fun validateConfig(config: TtsConfig): Pair<Boolean, String?> {
        // 检查语言是否支持
        val supportedLanguages = getSupportedLanguages()
        if (!supportedLanguages.any { it.code == config.language.code }) {
            return false to "不支持的语言: ${config.language.name}"
        }
        
        // 检查语速
        if (config.speechRate < 0.1f || config.speechRate > 3.0f) {
            return false to "语速必须在0.1到3.0之间"
        }
        
        // 检查音调
        if (config.pitch < 0.5f || config.pitch > 2.0f) {
            return false to "音调必须在0.5到2.0之间"
        }
        
        // 检查音量
        if (config.volume < 0.0f || config.volume > 1.0f) {
            return false to "音量必须在0.0到1.0之间"
        }
        
        return true to null
    }
    
    /**
     * 预处理文本以优化TTS效果
     * @param text 原始文本
     * @param language 语言
     * @return 处理后的文本
     */
    fun preprocessText(text: String, language: Language): String {
        var processedText = text.trim()
        
        when (language.code.lowercase()) {
            "zh-cn", "zh-tw" -> {
                // 中文文本处理
                processedText = processedText
                    .replace(Regex("[。！？]"), "$0 ") // 在句号后添加停顿
                    .replace(Regex("[，；]"), "$0") // 保持逗号分号
                    .replace(Regex("\\s+"), " ") // 规范化空格
            }
            "en-us", "en-gb" -> {
                // 英文文本处理
                processedText = processedText
                    .replace(Regex("[.!?]"), "$0 ") // 在句号后添加停顿
                    .replace(Regex("[,;]"), "$0 ") // 在逗号分号后添加短停顿
                    .replace(Regex("\\s+"), " ") // 规范化空格
            }
            "ja-jp" -> {
                // 日文文本处理
                processedText = processedText
                    .replace(Regex("[。！？]"), "$0 ") // 在句号后添加停顿
                    .replace(Regex("[、，]"), "$0") // 保持逗号
                    .replace(Regex("\\s+"), " ") // 规范化空格
            }
            "ko-kr" -> {
                // 韩文文本处理
                processedText = processedText
                    .replace(Regex("[.!?。！？]"), "$0 ") // 在句号后添加停顿
                    .replace(Regex("[,，]"), "$0") // 保持逗号
                    .replace(Regex("\\s+"), " ") // 规范化空格
            }
        }
        
        return processedText
    }
    
    /**
     * 估算播放时长
     * @param text 文本
     * @param config TTS配置
     * @return 估算时长（毫秒）
     */
    fun estimatePlaybackDuration(text: String, config: TtsConfig): Long {
        val baseWordsPerMinute = when (config.language.code.lowercase()) {
            "zh-cn", "zh-tw" -> 200 // 中文每分钟字数
            "en-us", "en-gb" -> 150 // 英文每分钟单词数
            "ja-jp" -> 180 // 日文每分钟字数
            "ko-kr" -> 170 // 韩文每分钟字数
            else -> 150
        }
        
        val adjustedWpm = baseWordsPerMinute * config.speechRate
        val wordCount = text.length // 简化计算，实际应该按语言特点计算
        
        return ((wordCount / adjustedWpm) * 60 * 1000).toLong()
    }
    
    /**
     * 获取TTS播放建议
     * @param result 播放结果
     * @return 改进建议
     */
    fun getPlaybackSuggestions(result: TtsResult): List<String> {
        return when (result) {
            is TtsResult.Error -> {
                when (result.errorCode) {
                    -1 -> listOf("TTS引擎未初始化", "请重启应用", "检查TTS服务")
                    -2 -> listOf("不支持的语言", "请选择其他语言", "更新TTS引擎")
                    -3 -> listOf("TTS服务不可用", "请检查系统TTS设置", "安装TTS引擎")
                    -4 -> listOf("音频播放失败", "请检查音频设置", "调整音量")
                    -5 -> listOf("文本过长", "请分段播放", "缩短文本长度")
                    else -> listOf("播放失败", "请重试", "检查TTS设置")
                }
            }
            else -> emptyList()
        }
    }
    
    /**
     * 释放资源
     */
    fun shutdown() {
        textToSpeechService.shutdown()
    }
}
