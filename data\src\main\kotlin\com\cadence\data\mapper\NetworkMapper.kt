package com.cadence.data.mapper

import com.cadence.core.network.NetworkResult
import com.cadence.core.network.TranslationNetworkService
import com.cadence.domain.model.*
import java.util.*

/**
 * 网络数据映射器
 * 负责网络层数据与领域模型之间的转换
 */
object NetworkMapper {
    
    /**
     * 将网络翻译结果转换为领域模型
     */
    fun com.cadence.core.network.dto.TranslationResult.toDomain(
        sourceText: String,
        sourceLanguage: Language,
        targetLanguage: Language
    ): Translation {
        val currentTime = System.currentTimeMillis()
        
        return Translation(
            id = UUID.randomUUID().toString(),
            sourceText = sourceText,
            translatedText = translatedText,
            sourceLanguage = sourceLanguage,
            targetLanguage = targetLanguage,
            confidenceScore = confidence,
            isFavorite = false,
            createdAt = currentTime,
            updatedAt = currentTime,
            translationType = TranslationType.TEXT,
            culturalContext = null
        )
    }
    
    /**
     * 将网络语言检测结果转换为领域模型
     */
    fun com.cadence.core.network.dto.LanguageDetectionResult.toDomain(): LanguageDetection {
        return LanguageDetection(
            detectedLanguage = Language(
                code = detectedLanguage,
                name = getLanguageName(detectedLanguage),
                region = null
            ),
            confidence = confidence,
            isReliable = isReliable,
            alternatives = emptyList() // 网络层暂不支持备选项
        )
    }
    
    /**
     * 将翻译请求转换为网络层参数
     */
    fun TranslationRequest.toNetworkParams(): Map<String, Any> {
        return mapOf(
            "sourceText" to sourceText,
            "sourceLanguage" to sourceLanguage.code,
            "targetLanguage" to targetLanguage.code,
            "sourceRegion" to (sourceLanguage.region?.code ?: ""),
            "targetRegion" to (targetLanguage.region?.code ?: ""),
            "includeCulturalContext" to includeCulturalContext
        )
    }
    
    /**
     * 创建翻译结果包装器
     */
    fun createTranslationResult(
        translation: Translation,
        isFromCache: Boolean = false,
        processingTimeMs: Long = 0L
    ): TranslationResult {
        return TranslationResult(
            translation = translation,
            isFromCache = isFromCache,
            processingTimeMs = processingTimeMs
        )
    }
    
    /**
     * 处理网络结果错误
     */
    fun <T> NetworkResult<T>.toDomainResult(): Result<T> {
        return when (this) {
            is NetworkResult.Success -> Result.success(data)
            is NetworkResult.Error -> {
                val exception = when {
                    exception != null -> exception
                    message != null -> Exception(message)
                    else -> Exception("未知网络错误")
                }
                Result.failure(exception)
            }
        }
    }
    
    /**
     * 根据语言代码获取语言名称
     * 这里提供一些常用语言的映射，实际项目中应该从数据库或配置文件获取
     */
    private fun getLanguageName(languageCode: String): String {
        return when (languageCode.lowercase()) {
            "zh" -> "中文"
            "en" -> "English"
            "ja" -> "日本語"
            "ko" -> "한국어"
            "fr" -> "Français"
            "de" -> "Deutsch"
            "es" -> "Español"
            "it" -> "Italiano"
            "pt" -> "Português"
            "ru" -> "Русский"
            "ar" -> "العربية"
            "hi" -> "हिन्दी"
            "th" -> "ไทย"
            "vi" -> "Tiếng Việt"
            "id" -> "Bahasa Indonesia"
            "ms" -> "Bahasa Melayu"
            "tr" -> "Türkçe"
            "pl" -> "Polski"
            "nl" -> "Nederlands"
            "sv" -> "Svenska"
            "da" -> "Dansk"
            "no" -> "Norsk"
            "fi" -> "Suomi"
            "cs" -> "Čeština"
            "sk" -> "Slovenčina"
            "hu" -> "Magyar"
            "ro" -> "Română"
            "bg" -> "Български"
            "hr" -> "Hrvatski"
            "sr" -> "Српски"
            "sl" -> "Slovenščina"
            "et" -> "Eesti"
            "lv" -> "Latviešu"
            "lt" -> "Lietuvių"
            "mt" -> "Malti"
            "ga" -> "Gaeilge"
            "cy" -> "Cymraeg"
            "eu" -> "Euskera"
            "ca" -> "Català"
            "gl" -> "Galego"
            "is" -> "Íslenska"
            "mk" -> "Македонски"
            "sq" -> "Shqip"
            "he" -> "עברית"
            "fa" -> "فارسی"
            "ur" -> "اردو"
            "bn" -> "বাংলা"
            "ta" -> "தமிழ்"
            "te" -> "తెలుగు"
            "ml" -> "മലയാളം"
            "kn" -> "ಕನ್ನಡ"
            "gu" -> "ગુજરાતી"
            "pa" -> "ਪੰਜਾਬੀ"
            "or" -> "ଓଡ଼ିଆ"
            "as" -> "অসমীয়া"
            "ne" -> "नेपाली"
            "si" -> "සිංහල"
            "my" -> "မြန်မာ"
            "km" -> "ខ្មែរ"
            "lo" -> "ລາວ"
            "ka" -> "ქართული"
            "am" -> "አማርኛ"
            "sw" -> "Kiswahili"
            "zu" -> "isiZulu"
            "af" -> "Afrikaans"
            "xh" -> "isiXhosa"
            else -> languageCode.uppercase()
        }
    }
}
