package com.cadence.core.offline

import com.cadence.core.database.dao.OfflineDictionaryDao
import com.cadence.core.database.entity.OfflineDictionaryEntity
import com.cadence.core.database.entity.WordUsageHistoryEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 离线词典服务
 * 提供离线词汇查询、管理和统计功能
 */
@Singleton
class OfflineDictionaryService @Inject constructor(
    private val offlineDictionaryDao: OfflineDictionaryDao
) {
    
    companion object {
        private const val MAX_SEARCH_RESULTS = 20
        private const val MIN_WORD_LENGTH = 1
        private const val MAX_WORD_LENGTH = 100
        private const val USAGE_HISTORY_RETENTION_DAYS = 90
    }
    
    /**
     * 查询单词翻译
     */
    suspend fun getTranslation(
        sourceWord: String,
        sourceLanguage: String,
        targetLanguage: String
    ): DictionaryTranslationResult = withContext(Dispatchers.IO) {
        try {
            // 验证输入参数
            if (!isValidWord(sourceWord)) {
                return@withContext DictionaryTranslationResult.Error("无效的输入词汇")
            }
            
            if (!isValidLanguageCode(sourceLanguage) || !isValidLanguageCode(targetLanguage)) {
                return@withContext DictionaryTranslationResult.Error("无效的语言代码")
            }
            
            // 精确查询
            val exactMatch = offlineDictionaryDao.getTranslation(
                sourceWord.trim().lowercase(),
                sourceLanguage,
                targetLanguage
            )
            
            if (exactMatch != null) {
                // 更新使用统计
                updateWordUsage(exactMatch.id, sourceWord)
                
                return@withContext DictionaryTranslationResult.Success(
                    translations = listOf(exactMatch),
                    isExactMatch = true,
                    searchQuery = sourceWord
                )
            }
            
            // 模糊查询
            val fuzzyMatches = offlineDictionaryDao.searchTranslations(
                sourceWord.trim().lowercase(),
                sourceLanguage,
                targetLanguage,
                MAX_SEARCH_RESULTS
            )
            
            if (fuzzyMatches.isNotEmpty()) {
                return@withContext DictionaryTranslationResult.Success(
                    translations = fuzzyMatches,
                    isExactMatch = false,
                    searchQuery = sourceWord
                )
            }
            
            // 未找到翻译
            DictionaryTranslationResult.NotFound(sourceWord)
            
        } catch (e: Exception) {
            Timber.e(e, "查询词典翻译失败: $sourceWord")
            DictionaryTranslationResult.Error("查询失败: ${e.message}")
        }
    }
    
    /**
     * 批量查询单词翻译
     */
    suspend fun getTranslations(
        sourceWords: List<String>,
        sourceLanguage: String,
        targetLanguage: String
    ): List<OfflineDictionaryEntity> = withContext(Dispatchers.IO) {
        try {
            if (sourceWords.isEmpty()) {
                return@withContext emptyList()
            }
            
            val validWords = sourceWords
                .filter { isValidWord(it) }
                .map { it.trim().lowercase() }
                .distinct()
            
            if (validWords.isEmpty()) {
                return@withContext emptyList()
            }
            
            offlineDictionaryDao.getTranslations(validWords, sourceLanguage, targetLanguage)
            
        } catch (e: Exception) {
            Timber.e(e, "批量查询词典翻译失败")
            emptyList()
        }
    }
    
    /**
     * 获取高频词汇
     */
    suspend fun getHighFrequencyWords(
        sourceLanguage: String,
        targetLanguage: String,
        limit: Int = 100
    ): List<OfflineDictionaryEntity> = withContext(Dispatchers.IO) {
        try {
            offlineDictionaryDao.getHighFrequencyWords(
                sourceLanguage,
                targetLanguage,
                minFrequency = 50,
                limit = limit.coerceAtMost(1000)
            )
        } catch (e: Exception) {
            Timber.e(e, "获取高频词汇失败")
            emptyList()
        }
    }
    
    /**
     * 获取最近使用的词汇
     */
    suspend fun getRecentlyUsedWords(
        sourceLanguage: String,
        targetLanguage: String,
        limit: Int = 50
    ): List<OfflineDictionaryEntity> = withContext(Dispatchers.IO) {
        try {
            offlineDictionaryDao.getRecentlyUsedWords(
                sourceLanguage,
                targetLanguage,
                limit.coerceAtMost(100)
            )
        } catch (e: Exception) {
            Timber.e(e, "获取最近使用词汇失败")
            emptyList()
        }
    }
    
    /**
     * 添加或更新词典条目
     */
    suspend fun addOrUpdateDictionaryEntry(
        sourceWord: String,
        targetWord: String,
        sourceLanguage: String,
        targetLanguage: String,
        wordType: String? = null,
        wordFrequency: Int = 1,
        pronunciation: String? = null,
        exampleSentence: String? = null,
        exampleTranslation: String? = null,
        confidenceScore: Float = 0.8f
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            if (!isValidWord(sourceWord) || !isValidWord(targetWord)) {
                Timber.w("无效的词汇输入")
                return@withContext false
            }
            
            val entry = OfflineDictionaryEntity(
                sourceWord = sourceWord.trim().lowercase(),
                targetWord = targetWord.trim(),
                sourceLanguage = sourceLanguage,
                targetLanguage = targetLanguage,
                wordType = wordType,
                wordFrequency = wordFrequency,
                pronunciation = pronunciation,
                exampleSentence = exampleSentence,
                exampleTranslation = exampleTranslation,
                confidenceScore = confidenceScore.coerceIn(0.0f, 1.0f),
                isVerified = false
            )
            
            val id = offlineDictionaryDao.insertOrUpdateDictionaryEntry(entry)
            
            // 更新统计信息
            offlineDictionaryDao.updateDictionaryStats(sourceLanguage, targetLanguage)
            
            Timber.d("词典条目添加成功: $sourceWord -> $targetWord (ID: $id)")
            true
            
        } catch (e: Exception) {
            Timber.e(e, "添加词典条目失败: $sourceWord -> $targetWord")
            false
        }
    }
    
    /**
     * 批量导入词典条目
     */
    suspend fun importDictionaryEntries(
        entries: List<OfflineDictionaryEntity>
    ): ImportResult = withContext(Dispatchers.IO) {
        try {
            if (entries.isEmpty()) {
                return@withContext ImportResult(0, 0, "没有要导入的条目")
            }
            
            val validEntries = entries.filter { entry ->
                isValidWord(entry.sourceWord) && 
                isValidWord(entry.targetWord) &&
                isValidLanguageCode(entry.sourceLanguage) &&
                isValidLanguageCode(entry.targetLanguage)
            }
            
            if (validEntries.isEmpty()) {
                return@withContext ImportResult(0, entries.size, "所有条目都无效")
            }
            
            // 批量插入
            offlineDictionaryDao.insertDictionaryEntries(validEntries)
            
            // 更新统计信息
            val languagePairs = validEntries.map { "${it.sourceLanguage}-${it.targetLanguage}" }.distinct()
            languagePairs.forEach { pair ->
                val (source, target) = pair.split("-")
                offlineDictionaryDao.updateDictionaryStats(source, target)
            }
            
            val successCount = validEntries.size
            val failureCount = entries.size - successCount
            
            Timber.d("词典批量导入完成: 成功 $successCount, 失败 $failureCount")
            ImportResult(successCount, failureCount, "导入完成")
            
        } catch (e: Exception) {
            Timber.e(e, "批量导入词典条目失败")
            ImportResult(0, entries.size, "导入失败: ${e.message}")
        }
    }
    
    /**
     * 获取词典统计信息
     */
    suspend fun getDictionaryStats(
        sourceLanguage: String,
        targetLanguage: String
    ): DictionaryStats? = withContext(Dispatchers.IO) {
        try {
            val languagePair = "$sourceLanguage-$targetLanguage"
            val statsEntity = offlineDictionaryDao.getDictionaryStats(languagePair)
            
            statsEntity?.let {
                DictionaryStats(
                    languagePair = it.languagePair,
                    totalWords = it.totalWords,
                    verifiedWords = it.verifiedWords,
                    lastUpdated = it.lastUpdated,
                    version = it.version
                )
            }
        } catch (e: Exception) {
            Timber.e(e, "获取词典统计信息失败")
            null
        }
    }
    
    /**
     * 清理旧的使用历史记录
     */
    suspend fun cleanupOldUsageHistory(): Boolean = withContext(Dispatchers.IO) {
        try {
            val cutoffTime = System.currentTimeMillis() - (USAGE_HISTORY_RETENTION_DAYS * 24 * 60 * 60 * 1000L)
            offlineDictionaryDao.cleanupOldUsageHistory(cutoffTime)
            Timber.d("清理旧使用历史记录完成")
            true
        } catch (e: Exception) {
            Timber.e(e, "清理使用历史记录失败")
            false
        }
    }
    
    /**
     * 更新词汇使用统计
     */
    private suspend fun updateWordUsage(dictionaryId: Long, contextText: String?) {
        try {
            val currentTime = System.currentTimeMillis()
            
            // 更新词典条目的使用统计
            offlineDictionaryDao.updateWordUsage(dictionaryId, currentTime)
            
            // 记录使用历史
            val usageHistory = WordUsageHistoryEntity(
                dictionaryId = dictionaryId,
                contextText = contextText?.take(200), // 限制上下文长度
                usedAt = currentTime
            )
            offlineDictionaryDao.insertWordUsageHistory(usageHistory)
            
        } catch (e: Exception) {
            Timber.e(e, "更新词汇使用统计失败")
        }
    }
    
    /**
     * 验证词汇是否有效
     */
    private fun isValidWord(word: String): Boolean {
        return word.isNotBlank() && 
               word.length >= MIN_WORD_LENGTH && 
               word.length <= MAX_WORD_LENGTH
    }
    
    /**
     * 验证语言代码是否有效
     */
    private fun isValidLanguageCode(languageCode: String): Boolean {
        val supportedLanguages = setOf("zh", "en", "ja", "ko")
        return supportedLanguages.contains(languageCode)
    }
}

/**
 * 词典翻译结果封装类
 */
sealed class DictionaryTranslationResult {
    data class Success(
        val translations: List<OfflineDictionaryEntity>,
        val isExactMatch: Boolean,
        val searchQuery: String
    ) : DictionaryTranslationResult()
    
    data class NotFound(
        val searchQuery: String
    ) : DictionaryTranslationResult()
    
    data class Error(
        val message: String
    ) : DictionaryTranslationResult()
}

/**
 * 导入结果数据类
 */
data class ImportResult(
    val successCount: Int,
    val failureCount: Int,
    val message: String
)

/**
 * 词典统计信息数据类
 */
data class DictionaryStats(
    val languagePair: String,
    val totalWords: Int,
    val verifiedWords: Int,
    val lastUpdated: Long,
    val version: String
)
