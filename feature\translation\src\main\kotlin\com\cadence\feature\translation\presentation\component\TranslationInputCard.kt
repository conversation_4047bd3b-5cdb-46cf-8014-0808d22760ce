package com.cadence.feature.translation.presentation.component

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import com.cadence.core.speech.SpeechRecognitionService
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.cadence.domain.model.Language

/**
 * 翻译输入卡片组件
 * 包含文本输入框、语言选择和相关操作按钮
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TranslationInputCard(
    sourceText: String,
    sourceLanguage: Language,
    targetLanguage: Language,
    supportedLanguages: List<Language>,
    isDetectingLanguage: Boolean,
    autoDetectLanguage: Boolean,
    onSourceTextChange: (String) -> Unit,
    onSourceLanguageChange: (Language) -> Unit,
    onTargetLanguageChange: (Language) -> Unit,
    onSwapLanguages: () -> Unit,
    onDetectLanguage: () -> Unit,
    onTranslate: () -> Unit,
    onClearText: () -> Unit,
    // 语音相关参数
    isListening: Boolean = false,
    speechResult: SpeechRecognitionService.SpeechResult? = null,
    hasSpeechPermission: Boolean = false,
    isSpeechAvailable: Boolean = false,
    onStartSpeechRecognition: () -> Unit = {},
    onStopSpeechRecognition: () -> Unit = {},
    onRequestSpeechPermission: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    val focusRequester = remember { FocusRequester() }
    
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 语言选择栏
            LanguageSelectionRow(
                sourceLanguage = sourceLanguage,
                targetLanguage = targetLanguage,
                supportedLanguages = supportedLanguages,
                autoDetectLanguage = autoDetectLanguage,
                isDetectingLanguage = isDetectingLanguage,
                onSourceLanguageChange = onSourceLanguageChange,
                onTargetLanguageChange = onTargetLanguageChange,
                onSwapLanguages = onSwapLanguages,
                onDetectLanguage = onDetectLanguage
            )
            
            // 文本输入框
            OutlinedTextField(
                value = sourceText,
                onValueChange = onSourceTextChange,
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(min = 120.dp, max = 200.dp)
                    .focusRequester(focusRequester),
                placeholder = {
                    Text(
                        text = "请输入要翻译的文本...",
                        style = MaterialTheme.typography.bodyLarge
                    )
                },
                keyboardOptions = KeyboardOptions(
                    capitalization = KeyboardCapitalization.Sentences,
                    keyboardType = KeyboardType.Text,
                    imeAction = ImeAction.Done
                ),
                keyboardActions = KeyboardActions(
                    onDone = {
                        keyboardController?.hide()
                        onTranslate()
                    }
                ),
                trailingIcon = {
                    if (sourceText.isNotEmpty()) {
                        IconButton(onClick = onClearText) {
                            Icon(
                                imageVector = Icons.Default.Clear,
                                contentDescription = "清空文本"
                            )
                        }
                    }
                },
                maxLines = 8,
                singleLine = false
            )
            
            // 操作按钮栏
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 翻译按钮
                Button(
                    onClick = onTranslate,
                    modifier = Modifier.weight(1f),
                    enabled = sourceText.isNotBlank()
                ) {
                    Icon(
                        imageVector = Icons.Default.Translate,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("翻译")
                }
                
                // 语音输入按钮
                SpeechInputButton(
                    isListening = isListening,
                    isAvailable = isSpeechAvailable,
                    hasPermission = hasSpeechPermission,
                    onStartListening = onStartSpeechRecognition,
                    onStopListening = onStopSpeechRecognition,
                    onRequestPermission = onRequestSpeechPermission,
                    speechResult = speechResult,
                    modifier = Modifier.size(48.dp)
                )
                
                // OCR按钮
                OutlinedButton(
                    onClick = { /* TODO: 实现OCR功能 */ }
                ) {
                    Icon(
                        imageVector = Icons.Default.CameraAlt,
                        contentDescription = "拍照翻译"
                    )
                }
            }
        }
    }
}

/**
 * 语言选择行组件
 */
@Composable
private fun LanguageSelectionRow(
    sourceLanguage: Language,
    targetLanguage: Language,
    supportedLanguages: List<Language>,
    autoDetectLanguage: Boolean,
    isDetectingLanguage: Boolean,
    onSourceLanguageChange: (Language) -> Unit,
    onTargetLanguageChange: (Language) -> Unit,
    onSwapLanguages: () -> Unit,
    onDetectLanguage: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 源语言选择
        LanguageDropdown(
            selectedLanguage = sourceLanguage,
            supportedLanguages = supportedLanguages,
            onLanguageChange = onSourceLanguageChange,
            modifier = Modifier.weight(1f),
            showAutoDetect = true,
            autoDetectEnabled = autoDetectLanguage,
            isDetecting = isDetectingLanguage,
            onDetectLanguage = onDetectLanguage
        )
        
        // 交换语言按钮
        IconButton(
            onClick = onSwapLanguages,
            modifier = Modifier.size(40.dp)
        ) {
            Icon(
                imageVector = Icons.Default.SwapHoriz,
                contentDescription = "交换语言",
                tint = MaterialTheme.colorScheme.primary
            )
        }
        
        // 目标语言选择
        LanguageDropdown(
            selectedLanguage = targetLanguage,
            supportedLanguages = supportedLanguages,
            onLanguageChange = onTargetLanguageChange,
            modifier = Modifier.weight(1f)
        )
    }
}

/**
 * 语言下拉选择组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun LanguageDropdown(
    selectedLanguage: Language,
    supportedLanguages: List<Language>,
    onLanguageChange: (Language) -> Unit,
    modifier: Modifier = Modifier,
    showAutoDetect: Boolean = false,
    autoDetectEnabled: Boolean = false,
    isDetecting: Boolean = false,
    onDetectLanguage: (() -> Unit)? = null
) {
    var expanded by remember { mutableStateOf(false) }
    
    ExposedDropdownMenuBox(
        expanded = expanded,
        onExpandedChange = { expanded = !expanded },
        modifier = modifier
    ) {
        OutlinedTextField(
            value = if (showAutoDetect && autoDetectEnabled) {
                if (isDetecting) "检测中..." else "自动检测"
            } else {
                selectedLanguage.name
            },
            onValueChange = { },
            readOnly = true,
            modifier = Modifier
                .fillMaxWidth()
                .menuAnchor(),
            trailingIcon = {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    if (showAutoDetect && isDetecting) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                    }
                    
                    if (showAutoDetect && !autoDetectEnabled && onDetectLanguage != null) {
                        IconButton(
                            onClick = onDetectLanguage,
                            modifier = Modifier.size(24.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Psychology,
                                contentDescription = "检测语言",
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }
                    
                    ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)
                }
            },
            colors = ExposedDropdownMenuDefaults.outlinedTextFieldColors()
        )
        
        ExposedDropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false }
        ) {
            if (showAutoDetect) {
                DropdownMenuItem(
                    text = { Text("自动检测") },
                    onClick = {
                        // TODO: 启用自动检测
                        expanded = false
                    },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.Psychology,
                            contentDescription = null
                        )
                    }
                )
                HorizontalDivider()
            }
            
            supportedLanguages.forEach { language ->
                DropdownMenuItem(
                    text = { Text(language.name) },
                    onClick = {
                        onLanguageChange(language)
                        expanded = false
                    },
                    leadingIcon = {
                        // TODO: 添加语言标志图标
                        Text(
                            text = language.code.uppercase(),
                            style = MaterialTheme.typography.labelSmall
                        )
                    }
                )
            }
        }
    }
}
