package com.cadence.cadence.navigation

/**
 * Cadence应用程序导航目标定义
 * 
 * 集中管理所有页面的路由常量，
 * 便于导航管理和深度链接配置
 */
object CadenceDestinations {
    
    // 主要功能页面路由
    const val TRANSLATION_ROUTE = "translation"
    const val HISTORY_ROUTE = "history"
    const val FAVORITES_ROUTE = "favorites"
    const val LEARNING_ROUTE = "learning"
    const val SETTINGS_ROUTE = "settings"
    const val CULTURAL_ROUTE = "cultural"
    
    // 翻译功能子页面路由
    const val TRANSLATION_DETAIL_ROUTE = "translation_detail"
    const val BATCH_TRANSLATION_ROUTE = "batch_translation"
    const val VOICE_TRANSLATION_ROUTE = "voice_translation"
    const val OCR_TRANSLATION_ROUTE = "ocr_translation"
    
    // 历史记录子页面路由
    const val HISTORY_DETAIL_ROUTE = "history_detail"
    const val HISTORY_SEARCH_ROUTE = "history_search"
    
    // 收藏子页面路由
    const val FAVORITES_DETAIL_ROUTE = "favorites_detail"
    const val FAVORITES_MANAGE_ROUTE = "favorites_manage"
    
    // 学习模式子页面路由
    const val LEARNING_PRACTICE_ROUTE = "learning_practice"
    const val LEARNING_PROGRESS_ROUTE = "learning_progress"
    const val LEARNING_QUIZ_ROUTE = "learning_quiz"
    
    // 设置子页面路由
    const val SETTINGS_LANGUAGE_ROUTE = "settings_language"
    const val SETTINGS_THEME_ROUTE = "settings_theme"
    const val SETTINGS_ABOUT_ROUTE = "settings_about"
    const val SETTINGS_PRIVACY_ROUTE = "settings_privacy"
    
    // 文化背景子页面路由
    const val CULTURAL_DETAIL_ROUTE = "cultural_detail"
    const val CULTURAL_SEARCH_ROUTE = "cultural_search"
    
    // 带参数的路由模板
    const val TRANSLATION_DETAIL_WITH_ID = "translation_detail/{translationId}"
    const val HISTORY_DETAIL_WITH_ID = "history_detail/{historyId}"
    const val FAVORITES_DETAIL_WITH_ID = "favorites_detail/{favoriteId}"
    const val CULTURAL_DETAIL_WITH_ID = "cultural_detail/{culturalId}"
    
    // 参数键名
    const val TRANSLATION_ID_KEY = "translationId"
    const val HISTORY_ID_KEY = "historyId"
    const val FAVORITE_ID_KEY = "favoriteId"
    const val CULTURAL_ID_KEY = "culturalId"
    
    // 深度链接前缀
    const val DEEP_LINK_PREFIX = "cadence://"
    
    // 完整深度链接
    const val TRANSLATION_DEEP_LINK = "${DEEP_LINK_PREFIX}translation"
    const val HISTORY_DEEP_LINK = "${DEEP_LINK_PREFIX}history"
    const val FAVORITES_DEEP_LINK = "${DEEP_LINK_PREFIX}favorites"
    const val LEARNING_DEEP_LINK = "${DEEP_LINK_PREFIX}learning"
    const val SETTINGS_DEEP_LINK = "${DEEP_LINK_PREFIX}settings"
    const val CULTURAL_DEEP_LINK = "${DEEP_LINK_PREFIX}cultural"
}

/**
 * 导航参数构建器
 */
object CadenceNavigationArgs {
    
    /**
     * 构建翻译详情页面路由
     */
    fun translationDetail(translationId: String): String {
        return "translation_detail/$translationId"
    }
    
    /**
     * 构建历史记录详情页面路由
     */
    fun historyDetail(historyId: String): String {
        return "history_detail/$historyId"
    }
    
    /**
     * 构建收藏详情页面路由
     */
    fun favoritesDetail(favoriteId: String): String {
        return "favorites_detail/$favoriteId"
    }
    
    /**
     * 构建文化背景详情页面路由
     */
    fun culturalDetail(culturalId: String): String {
        return "cultural_detail/$culturalId"
    }
}