package com.cadence.core.network

import com.cadence.core.network.api.GeminiApiService
import com.cadence.core.network.dto.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 翻译网络服务实现
 * 封装Gemini API调用，提供翻译、语言检测等功能
 */
@Singleton
class TranslationNetworkService @Inject constructor(
    private val geminiApiService: GeminiApiService
) {
    
    /**
     * 执行翻译
     */
    suspend fun translate(
        text: String,
        sourceLanguage: String,
        targetLanguage: String,
        sourceRegion: String? = null,
        targetRegion: String? = null,
        includeCulturalContext: Boolean = false
    ): NetworkResult<TranslationResult> = withContext(Dispatchers.IO) {
        try {
            val prompt = buildTranslationPrompt(
                text, sourceLanguage, targetLanguage, 
                sourceRegion, targetRegion, includeCulturalContext
            )
            
            val request = GeminiTranslationRequest(
                contents = listOf(
                    Content(
                        parts = listOf(Part(text = prompt))
                    )
                ),
                generationConfig = GenerationConfig(
                    temperature = 0.1f,
                    topK = 1,
                    topP = 0.8f,
                    maxOutputTokens = 2048
                )
            )
            
            val response = geminiApiService.generateContent(
                model = "gemini-pro",
                request = request
            )
            
            if (response.isSuccessful) {
                val geminiResponse = response.body()
                if (geminiResponse?.error != null) {
                    NetworkResult.Error(
                        exception = Exception(geminiResponse.error.message),
                        code = geminiResponse.error.code
                    )
                } else {
                    val translatedText = extractTranslationFromResponse(geminiResponse)
                    if (translatedText.isNotBlank()) {
                        NetworkResult.Success(
                            TranslationResult(
                                translatedText = translatedText,
                                sourceLanguage = sourceLanguage,
                                targetLanguage = targetLanguage,
                                confidence = 0.9f // Gemini通常有较高的置信度
                            )
                        )
                    } else {
                        NetworkResult.Error(
                            exception = Exception("翻译结果为空"),
                            message = "API返回了空的翻译结果"
                        )
                    }
                }
            } else {
                handleHttpError(response.code(), response.message())
            }
        } catch (e: Exception) {
            handleException(e)
        }
    }
    
    /**
     * 检测语言
     */
    suspend fun detectLanguage(text: String): NetworkResult<LanguageDetectionResult> = 
        withContext(Dispatchers.IO) {
            try {
                val prompt = "请检测以下文本的语言，只返回语言代码（如zh、en、ja等）：\n\n$text"
                
                val request = GeminiTranslationRequest(
                    contents = listOf(
                        Content(
                            parts = listOf(Part(text = prompt))
                        )
                    ),
                    generationConfig = GenerationConfig(
                        temperature = 0.0f,
                        maxOutputTokens = 10
                    )
                )
                
                val response = geminiApiService.generateContent(request = request)
                
                if (response.isSuccessful) {
                    val detectedLanguage = extractLanguageFromResponse(response.body())
                    if (detectedLanguage.isNotBlank()) {
                        NetworkResult.Success(
                            LanguageDetectionResult(
                                detectedLanguage = detectedLanguage,
                                confidence = 0.85f,
                                isReliable = true
                            )
                        )
                    } else {
                        NetworkResult.Error(
                            exception = Exception("语言检测失败"),
                            message = "无法检测文本语言"
                        )
                    }
                } else {
                    handleHttpError(response.code(), response.message())
                }
            } catch (e: Exception) {
                handleException(e)
            }
        }
    
    /**
     * 获取文化背景解释
     */
    suspend fun getCulturalContext(
        text: String,
        sourceLanguage: String,
        targetLanguage: String
    ): NetworkResult<String> = withContext(Dispatchers.IO) {
        try {
            val prompt = """
                请解释以下文本在从${sourceLanguage}翻译到${targetLanguage}时的文化背景和含义差异：
                
                原文：$text
                
                请提供：
                1. 文化背景解释
                2. 使用场景说明
                3. 可能的理解差异
                
                请用中文回答，保持简洁明了。
            """.trimIndent()
            
            val request = CulturalContextRequest(
                contents = listOf(
                    Content(
                        parts = listOf(Part(text = prompt))
                    )
                )
            )
            
            val response = geminiApiService.generateContent(request = request)
            
            if (response.isSuccessful) {
                val context = extractTextFromResponse(response.body())
                if (context.isNotBlank()) {
                    NetworkResult.Success(context)
                } else {
                    NetworkResult.Error(
                        exception = Exception("文化背景解释为空"),
                        message = "无法获取文化背景信息"
                    )
                }
            } else {
                handleHttpError(response.code(), response.message())
            }
        } catch (e: Exception) {
            handleException(e)
        }
    }
    
    /**
     * 构建翻译提示词
     */
    private fun buildTranslationPrompt(
        text: String,
        sourceLanguage: String,
        targetLanguage: String,
        sourceRegion: String?,
        targetRegion: String?,
        includeCulturalContext: Boolean
    ): String {
        val regionInfo = buildString {
            if (sourceRegion != null) append("（${sourceRegion}地区）")
            if (targetRegion != null) append("翻译为${targetLanguage}（${targetRegion}地区）")
        }
        
        return buildString {
            append("请将以下${sourceLanguage}${regionInfo}文本翻译为${targetLanguage}：\n\n")
            append(text)
            append("\n\n要求：")
            append("1. 保持原文的语气和风格")
            append("2. 考虑地区语言特色")
            if (includeCulturalContext) {
                append("3. 如有必要，请在翻译后添加文化背景说明")
            }
            append("\n\n请只返回翻译结果，不要包含其他解释。")
        }
    }
    
    /**
     * 从响应中提取翻译结果
     */
    private fun extractTranslationFromResponse(response: GeminiResponse?): String {
        return response?.candidates?.firstOrNull()?.content?.parts?.firstOrNull()?.text?.trim() ?: ""
    }
    
    /**
     * 从响应中提取语言代码
     */
    private fun extractLanguageFromResponse(response: GeminiResponse?): String {
        val text = response?.candidates?.firstOrNull()?.content?.parts?.firstOrNull()?.text?.trim() ?: ""
        // 提取语言代码（通常是2-3个字符）
        return text.lowercase().take(3)
    }
    
    /**
     * 从响应中提取文本内容
     */
    private fun extractTextFromResponse(response: GeminiResponse?): String {
        return response?.candidates?.firstOrNull()?.content?.parts?.firstOrNull()?.text?.trim() ?: ""
    }
    
    /**
     * 处理HTTP错误
     */
    private fun handleHttpError(code: Int, message: String): NetworkResult.Error {
        Timber.e("HTTP错误: $code - $message")
        return when (code) {
            401 -> NetworkResult.Error(NetworkError.ApiKeyError)
            429 -> NetworkResult.Error(NetworkError.RateLimitError)
            in 500..599 -> NetworkResult.Error(NetworkError.ServerError(code, message))
            else -> NetworkResult.Error(NetworkError.UnknownError("HTTP错误: $code"))
        }
    }
    
    /**
     * 处理异常
     */
    private fun handleException(exception: Exception): NetworkResult.Error {
        Timber.e(exception, "网络请求异常")
        return when (exception) {
            is UnknownHostException -> NetworkResult.Error(NetworkError.NetworkConnectionError)
            is SocketTimeoutException -> NetworkResult.Error(NetworkError.TimeoutError)
            is IOException -> NetworkResult.Error(NetworkError.NetworkConnectionError)
            else -> NetworkResult.Error(NetworkError.UnknownError(exception.message ?: "未知错误"))
        }
    }
}
