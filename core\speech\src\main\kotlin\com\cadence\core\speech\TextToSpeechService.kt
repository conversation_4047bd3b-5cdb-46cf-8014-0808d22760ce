package com.cadence.core.speech

import android.content.Context
import android.speech.tts.TextToSpeech
import android.speech.tts.UtteranceProgressListener
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.suspendCancellableCoroutine
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume

/**
 * 文字转语音服务
 * 提供多语言文字朗读功能
 */
@Singleton
class TextToSpeechService @Inject constructor(
    private val context: Context
) {
    
    private var tts: TextToSpeech? = null
    private var isInitialized = false
    
    /**
     * TTS播放结果
     */
    sealed class TtsResult {
        object Started : TtsResult()
        object Completed : TtsResult()
        object Stopped : TtsResult()
        data class Error(val errorCode: Int, val message: String) : TtsResult()
        data class Progress(val start: Int, val end: Int, val frame: Int) : TtsResult()
    }
    
    /**
     * TTS配置
     */
    data class TtsConfig(
        val language: String = "zh-CN",
        val speechRate: Float = 1.0f,
        val pitch: Float = 1.0f,
        val volume: Float = 1.0f
    )
    
    /**
     * 初始化TTS引擎
     */
    suspend fun initialize(): Boolean = suspendCancellableCoroutine { continuation ->
        if (isInitialized) {
            continuation.resume(true)
            return@suspendCancellableCoroutine
        }
        
        tts = TextToSpeech(context) { status ->
            isInitialized = status == TextToSpeech.SUCCESS
            continuation.resume(isInitialized)
        }
        
        continuation.invokeOnCancellation {
            if (!isInitialized) {
                tts?.shutdown()
                tts = null
            }
        }
    }
    
    /**
     * 检查TTS是否可用
     */
    fun isAvailable(): Boolean {
        return isInitialized && tts != null
    }
    
    /**
     * 获取支持的语言列表
     */
    fun getSupportedLanguages(): List<String> {
        if (!isAvailable()) return emptyList()
        
        val supportedLanguages = mutableListOf<String>()
        val languagesToCheck = listOf(
            "zh-CN", "zh-TW", "en-US", "en-GB", "ja-JP", "ko-KR",
            "fr-FR", "de-DE", "es-ES", "it-IT", "pt-BR", "ru-RU",
            "ar-SA", "hi-IN", "th-TH", "vi-VN", "id-ID", "ms-MY",
            "tl-PH", "tr-TR", "pl-PL", "nl-NL", "sv-SE", "da-DK", "no-NO"
        )
        
        languagesToCheck.forEach { languageCode ->
            val locale = Locale.forLanguageTag(languageCode)
            val result = tts?.isLanguageAvailable(locale)
            if (result == TextToSpeech.LANG_AVAILABLE || 
                result == TextToSpeech.LANG_COUNTRY_AVAILABLE ||
                result == TextToSpeech.LANG_COUNTRY_VAR_AVAILABLE) {
                supportedLanguages.add(languageCode)
            }
        }
        
        return supportedLanguages
    }
    
    /**
     * 设置语言
     */
    fun setLanguage(languageCode: String): Boolean {
        if (!isAvailable()) return false
        
        val locale = Locale.forLanguageTag(languageCode)
        val result = tts?.setLanguage(locale)
        
        return result == TextToSpeech.LANG_AVAILABLE || 
               result == TextToSpeech.LANG_COUNTRY_AVAILABLE ||
               result == TextToSpeech.LANG_COUNTRY_VAR_AVAILABLE
    }
    
    /**
     * 朗读文本
     * @param text 要朗读的文本
     * @param config TTS配置
     * @return 播放状态流
     */
    fun speak(text: String, config: TtsConfig = TtsConfig()): Flow<TtsResult> = callbackFlow {
        if (!isAvailable()) {
            trySend(TtsResult.Error(-1, "TTS引擎未初始化"))
            close()
            return@callbackFlow
        }
        
        if (text.isBlank()) {
            trySend(TtsResult.Error(-1, "文本内容为空"))
            close()
            return@callbackFlow
        }
        
        // 设置语言
        if (!setLanguage(config.language)) {
            trySend(TtsResult.Error(-1, "不支持的语言: ${config.language}"))
            close()
            return@callbackFlow
        }
        
        // 设置语音参数
        tts?.setSpeechRate(config.speechRate)
        tts?.setPitch(config.pitch)
        
        val utteranceId = "utterance_${System.currentTimeMillis()}"
        
        val progressListener = object : UtteranceProgressListener() {
            override fun onStart(utteranceId: String?) {
                trySend(TtsResult.Started)
            }
            
            override fun onDone(utteranceId: String?) {
                trySend(TtsResult.Completed)
                close()
            }
            
            override fun onError(utteranceId: String?) {
                trySend(TtsResult.Error(-1, "TTS播放错误"))
                close()
            }
            
            override fun onStop(utteranceId: String?, interrupted: Boolean) {
                trySend(TtsResult.Stopped)
                close()
            }
            
            override fun onRangeStart(utteranceId: String?, start: Int, end: Int, frame: Int) {
                trySend(TtsResult.Progress(start, end, frame))
            }
        }
        
        tts?.setOnUtteranceProgressListener(progressListener)
        
        val params = Bundle().apply {
            putString(TextToSpeech.Engine.KEY_PARAM_UTTERANCE_ID, utteranceId)
            putFloat(TextToSpeech.Engine.KEY_PARAM_VOLUME, config.volume)
        }
        
        val result = tts?.speak(text, TextToSpeech.QUEUE_FLUSH, params, utteranceId)
        
        if (result != TextToSpeech.SUCCESS) {
            trySend(TtsResult.Error(result ?: -1, "TTS播放失败"))
            close()
        }
        
        awaitClose {
            tts?.stop()
        }
    }
    
    /**
     * 停止播放
     */
    fun stop() {
        tts?.stop()
    }
    
    /**
     * 检查是否正在播放
     */
    fun isSpeaking(): Boolean {
        return tts?.isSpeaking == true
    }
    
    /**
     * 获取语言显示名称
     */
    fun getLanguageDisplayName(languageCode: String): String {
        return when (languageCode) {
            "zh-CN" -> "中文（简体）"
            "zh-TW" -> "中文（繁体）"
            "en-US" -> "English (US)"
            "en-GB" -> "English (UK)"
            "ja-JP" -> "日本語"
            "ko-KR" -> "한국어"
            "fr-FR" -> "Français"
            "de-DE" -> "Deutsch"
            "es-ES" -> "Español"
            "it-IT" -> "Italiano"
            "pt-BR" -> "Português (Brasil)"
            "ru-RU" -> "Русский"
            "ar-SA" -> "العربية"
            "hi-IN" -> "हिन्दी"
            "th-TH" -> "ไทย"
            "vi-VN" -> "Tiếng Việt"
            "id-ID" -> "Bahasa Indonesia"
            "ms-MY" -> "Bahasa Melayu"
            "tl-PH" -> "Filipino"
            "tr-TR" -> "Türkçe"
            "pl-PL" -> "Polski"
            "nl-NL" -> "Nederlands"
            "sv-SE" -> "Svenska"
            "da-DK" -> "Dansk"
            "no-NO" -> "Norsk"
            else -> languageCode
        }
    }
    
    /**
     * 根据翻译语言代码获取TTS语言代码
     */
    fun mapTranslationLanguageToTts(translationLanguage: String): String {
        return when (translationLanguage.lowercase()) {
            "chinese", "zh", "中文" -> "zh-CN"
            "english", "en", "英文" -> "en-US"
            "japanese", "ja", "日文" -> "ja-JP"
            "korean", "ko", "韩文" -> "ko-KR"
            "french", "fr", "法文" -> "fr-FR"
            "german", "de", "德文" -> "de-DE"
            "spanish", "es", "西班牙文" -> "es-ES"
            "italian", "it", "意大利文" -> "it-IT"
            "portuguese", "pt", "葡萄牙文" -> "pt-BR"
            "russian", "ru", "俄文" -> "ru-RU"
            "arabic", "ar", "阿拉伯文" -> "ar-SA"
            "hindi", "hi", "印地文" -> "hi-IN"
            "thai", "th", "泰文" -> "th-TH"
            "vietnamese", "vi", "越南文" -> "vi-VN"
            "indonesian", "id", "印尼文" -> "id-ID"
            "malay", "ms", "马来文" -> "ms-MY"
            "filipino", "tl", "菲律宾文" -> "tl-PH"
            "turkish", "tr", "土耳其文" -> "tr-TR"
            "polish", "pl", "波兰文" -> "pl-PL"
            "dutch", "nl", "荷兰文" -> "nl-NL"
            "swedish", "sv", "瑞典文" -> "sv-SE"
            "danish", "da", "丹麦文" -> "da-DK"
            "norwegian", "no", "挪威文" -> "no-NO"
            else -> "zh-CN" // 默认中文
        }
    }
    
    /**
     * 获取推荐的语音参数
     */
    fun getRecommendedConfig(languageCode: String): TtsConfig {
        return when (languageCode) {
            "zh-CN", "zh-TW" -> TtsConfig(
                language = languageCode,
                speechRate = 0.9f,
                pitch = 1.0f,
                volume = 1.0f
            )
            "en-US", "en-GB" -> TtsConfig(
                language = languageCode,
                speechRate = 1.0f,
                pitch = 1.0f,
                volume = 1.0f
            )
            "ja-JP" -> TtsConfig(
                language = languageCode,
                speechRate = 0.8f,
                pitch = 1.1f,
                volume = 1.0f
            )
            "ko-KR" -> TtsConfig(
                language = languageCode,
                speechRate = 0.9f,
                pitch = 1.0f,
                volume = 1.0f
            )
            else -> TtsConfig(
                language = languageCode,
                speechRate = 1.0f,
                pitch = 1.0f,
                volume = 1.0f
            )
        }
    }
    
    /**
     * 释放资源
     */
    fun shutdown() {
        tts?.stop()
        tts?.shutdown()
        tts = null
        isInitialized = false
    }
}
