package com.cadence.core.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.serialization.Serializable

/**
 * 翻译缓存数据实体
 * 缓存翻译结果以提高性能和减少API调用
 */
@Entity(tableName = "translation_cache")
@Serializable
data class TranslationCacheEntity(
    @PrimaryKey
    @ColumnInfo(name = "cache_key")
    val cacheKey: String, // 基于源文本+语言对生成的唯一键
    
    @ColumnInfo(name = "source_text")
    val sourceText: String,
    
    @ColumnInfo(name = "translated_text")
    val translatedText: String,
    
    @ColumnInfo(name = "source_language")
    val sourceLanguage: String,
    
    @ColumnInfo(name = "target_language")
    val targetLanguage: String,
    
    @ColumnInfo(name = "source_region")
    val sourceRegion: String? = null,
    
    @ColumnInfo(name = "target_region")
    val targetRegion: String? = null,
    
    @ColumnInfo(name = "confidence_score")
    val confidenceScore: Float? = null,
    
    @ColumnInfo(name = "cultural_context")
    val culturalContext: String? = null,
    
    @ColumnInfo(name = "access_count")
    val accessCount: Int = 1,
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long,
    
    @ColumnInfo(name = "last_accessed_at")
    val lastAccessedAt: Long,
    
    @ColumnInfo(name = "expires_at")
    val expiresAt: Long // 缓存过期时间
)
