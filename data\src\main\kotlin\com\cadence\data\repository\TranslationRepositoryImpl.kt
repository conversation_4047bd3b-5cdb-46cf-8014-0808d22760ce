package com.cadence.data.repository

import com.cadence.core.database.dao.TranslationDao
import com.cadence.core.database.dao.TranslationCacheDao
import com.cadence.core.network.TranslationNetworkService
import com.cadence.core.offline.*
import com.cadence.data.mapper.TranslationMapper
import com.cadence.data.mapper.TranslationMapper.toDomain
import com.cadence.data.mapper.TranslationMapper.toEntity
import com.cadence.data.mapper.TranslationMapper.toDomainList
import com.cadence.data.mapper.NetworkMapper
import com.cadence.data.mapper.NetworkMapper.toDomainResult
import com.cadence.core.database.entity.TranslationCacheEntity
import com.cadence.domain.model.*
import com.cadence.domain.repository.TranslationRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 翻译数据仓库实现
 * 整合网络服务、数据库缓存和业务逻辑
 */
@Singleton
class TranslationRepositoryImpl @Inject constructor(
    private val translationNetworkService: TranslationNetworkService,
    private val translationDao: TranslationDao,
    private val translationCacheDao: TranslationCacheDao,
    private val offlineTranslationService: OfflineTranslationService,
    private val offlineDictionaryService: OfflineDictionaryService,
    private val translationModeManager: TranslationModeManager
) : TranslationRepository {
    
    override suspend fun translate(request: TranslationRequest): Result<TranslationResult> {
        return withContext(Dispatchers.IO) {
            try {
                val startTime = System.currentTimeMillis()

                // 首先检查缓存
                val cacheKey = generateCacheKey(request)
                val cachedResult = translationCacheDao.getCachedTranslation(cacheKey)

                if (cachedResult != null && !isCacheExpired(cachedResult.createdAt)) {
                    Timber.d("使用缓存翻译结果: $cacheKey")
                    val translation = cachedResult.toTranslation()
                    return@withContext Result.success(
                        NetworkMapper.createTranslationResult(
                            translation = translation,
                            isFromCache = true,
                            processingTimeMs = System.currentTimeMillis() - startTime
                        )
                    )
                }

                // 获取推荐的翻译模式
                val modeRecommendation = translationModeManager.getRecommendedTranslationMode(
                    request.sourceLanguage.code,
                    request.targetLanguage.code
                )

                Timber.d("推荐翻译模式: ${modeRecommendation.mode}, 原因: ${modeRecommendation.reason}")

                // 根据模式选择翻译策略
                val translationResult = when (modeRecommendation.mode) {
                    TranslationMode.OFFLINE_ONLY -> {
                        performOfflineTranslation(request, startTime)
                    }
                    TranslationMode.OFFLINE_PREFERRED -> {
                        performOfflineTranslationWithFallback(request, startTime)
                    }
                    TranslationMode.ONLINE_ONLY, TranslationMode.ONLINE_PREFERRED -> {
                        performOnlineTranslation(request, startTime)
                    }
                    TranslationMode.HYBRID -> {
                        performHybridTranslation(request, startTime)
                    }
                }

                // 保存到历史记录和缓存
                if (translationResult.isSuccess) {
                    val result = translationResult.getOrThrow()
                    if (request.saveToHistory) {
                        saveTranslation(result.translation)
                    }
                    saveToCacheAsync(cacheKey, result.translation)
                }

                translationResult

                
            } catch (e: Exception) {
                Timber.e(e, "翻译过程中发生异常")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun detectLanguage(text: String): Result<LanguageDetection> {
        return withContext(Dispatchers.IO) {
            try {
                val networkResult = translationNetworkService.detectLanguage(text)
                val domainResult = networkResult.toDomainResult()
                
                if (domainResult.isSuccess) {
                    val detection = domainResult.getOrThrow().toDomain()
                    Result.success(detection)
                } else {
                    domainResult
                }
            } catch (e: Exception) {
                Timber.e(e, "语言检测过程中发生异常")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun getCulturalContext(translation: Translation): Result<String> {
        return withContext(Dispatchers.IO) {
            try {
                val networkResult = translationNetworkService.getCulturalContext(
                    text = translation.sourceText,
                    sourceLanguage = translation.sourceLanguage.code,
                    targetLanguage = translation.targetLanguage.code
                )
                
                networkResult.toDomainResult()
            } catch (e: Exception) {
                Timber.e(e, "获取文化背景过程中发生异常")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun saveTranslation(translation: Translation): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                translationDao.insertTranslation(translation.toEntity())
                Timber.d("保存翻译记录成功: ${translation.id}")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "保存翻译记录失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun updateTranslation(translation: Translation): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val updatedTranslation = translation.copy(updatedAt = System.currentTimeMillis())
                translationDao.updateTranslation(updatedTranslation.toEntity())
                Timber.d("更新翻译记录成功: ${translation.id}")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "更新翻译记录失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun deleteTranslation(translationId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                translationDao.deleteTranslation(translationId)
                Timber.d("删除翻译记录成功: $translationId")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "删除翻译记录失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun getTranslationById(translationId: String): Result<Translation?> {
        return withContext(Dispatchers.IO) {
            try {
                val entity = translationDao.getTranslationById(translationId)
                val translation = entity?.toDomain()
                Result.success(translation)
            } catch (e: Exception) {
                Timber.e(e, "获取翻译记录失败")
                Result.failure(e)
            }
        }
    }
    
    override fun getTranslationHistory(query: TranslationHistoryQuery): Flow<List<Translation>> {
        return translationDao.getTranslationHistory(
            sourceLanguageCode = query.languagePair?.first?.code,
            targetLanguageCode = query.languagePair?.second?.code,
            searchQuery = query.searchQuery,
            favoritesOnly = query.favoritesOnly,
            translationType = query.translationType?.name,
            limit = query.limit,
            offset = query.offset
        ).map { it.toDomainList() }
    }
    
    override fun getFavoriteTranslations(): Flow<List<Translation>> {
        return translationDao.getFavoriteTranslations().map { it.toDomainList() }
    }
    
    override fun searchTranslations(searchQuery: String): Flow<List<Translation>> {
        return translationDao.searchTranslations(searchQuery).map { it.toDomainList() }
    }
    
    override suspend fun updateFavoriteStatus(translationId: String, isFavorite: Boolean): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                translationDao.updateFavoriteStatus(translationId, isFavorite)
                Timber.d("更新收藏状态成功: $translationId -> $isFavorite")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "更新收藏状态失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun getTranslationStatistics(): Result<TranslationStatistics> {
        return withContext(Dispatchers.IO) {
            try {
                val totalCount = translationDao.getTotalTranslationCount()
                val favoriteCount = translationDao.getFavoriteTranslationCount()
                val languagePairs = translationDao.getMostUsedLanguagePairs(10)
                val typeStats = translationDao.getTranslationCountByType()
                val avgConfidence = translationDao.getAverageConfidenceScore() ?: 0f
                
                val statistics = TranslationMapper.createTranslationStatistics(
                    totalTranslations = totalCount,
                    favoriteCount = favoriteCount,
                    languagePairUsages = languagePairs,
                    translationsByType = typeStats,
                    averageConfidenceScore = avgConfidence
                )
                
                Result.success(statistics)
            } catch (e: Exception) {
                Timber.e(e, "获取翻译统计信息失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun clearTranslationHistory(): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                translationDao.clearAllTranslations()
                Timber.d("清空翻译历史成功")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "清空翻译历史失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun deleteTranslationsBefore(timestamp: Long): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                translationDao.deleteTranslationsBefore(timestamp)
                Timber.d("删除历史翻译记录成功: 时间戳 $timestamp")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "删除历史翻译记录失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun exportTranslationHistory(format: ExportFormat): Result<String> {
        // TODO: 实现导出功能
        return Result.failure(UnsupportedOperationException("导出功能尚未实现"))
    }
    
    override suspend fun importTranslationHistory(filePath: String): Result<ImportResult> {
        // TODO: 实现导入功能
        return Result.failure(UnsupportedOperationException("导入功能尚未实现"))
    }
    
    /**
     * 生成缓存键
     */
    private fun generateCacheKey(request: TranslationRequest): String {
        return "${request.sourceText.hashCode()}_${request.sourceLanguage.code}_${request.targetLanguage.code}"
    }
    
    /**
     * 检查缓存是否过期
     */
    private fun isCacheExpired(cacheTime: Long): Boolean {
        val expirationTime = 24 * 60 * 60 * 1000L // 24小时
        return System.currentTimeMillis() - cacheTime > expirationTime
    }
    
    /**
     * 异步保存到缓存
     */
    private suspend fun saveToCacheAsync(cacheKey: String, translation: Translation) {
        try {
            // TODO: 实现缓存保存逻辑
            Timber.d("保存翻译缓存: $cacheKey")
        } catch (e: Exception) {
            Timber.w(e, "保存翻译缓存失败")
        }
    }

    /**
     * 执行在线翻译
     */
    private suspend fun performOnlineTranslation(
        request: TranslationRequest,
        startTime: Long
    ): Result<TranslationResult> {
        return try {
            val networkResult = translationNetworkService.translate(
                text = request.sourceText,
                sourceLanguage = request.sourceLanguage.code,
                targetLanguage = request.targetLanguage.code,
                sourceRegion = request.sourceLanguage.region?.code,
                targetRegion = request.targetLanguage.region?.code,
                includeCulturalContext = request.includeCulturalContext
            )

            val domainResult = networkResult.toDomainResult()

            if (domainResult.isSuccess) {
                val networkTranslationResult = domainResult.getOrThrow()
                val translation = NetworkMapper.createTranslationResult(
                    translation = networkTranslationResult.toDomain(
                        sourceText = request.sourceText,
                        sourceLanguage = request.sourceLanguage,
                        targetLanguage = request.targetLanguage
                    ),
                    isFromCache = false,
                    processingTimeMs = System.currentTimeMillis() - startTime
                )
                Result.success(translation)
            } else {
                domainResult
            }
        } catch (e: Exception) {
            Timber.e(e, "在线翻译失败")
            Result.failure(e)
        }
    }

    /**
     * 执行离线翻译
     */
    private suspend fun performOfflineTranslation(
        request: TranslationRequest,
        startTime: Long
    ): Result<TranslationResult> {
        return try {
            val offlineResult = offlineTranslationService.translate(
                text = request.sourceText,
                sourceLanguage = request.sourceLanguage.code,
                targetLanguage = request.targetLanguage.code
            )

            when (offlineResult) {
                is OfflineTranslationResult.Success -> {
                    val translation = createTranslationFromOfflineResult(
                        offlineResult,
                        request,
                        System.currentTimeMillis() - startTime
                    )
                    Result.success(translation)
                }
                is OfflineTranslationResult.Error -> {
                    Timber.w("离线翻译失败: ${offlineResult.message}")
                    Result.failure(Exception("离线翻译失败: ${offlineResult.message}"))
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "离线翻译异常")
            Result.failure(e)
        }
    }

    /**
     * 执行离线翻译，失败时回退到在线翻译
     */
    private suspend fun performOfflineTranslationWithFallback(
        request: TranslationRequest,
        startTime: Long
    ): Result<TranslationResult> {
        // 首先尝试离线翻译
        val offlineResult = performOfflineTranslation(request, startTime)

        return if (offlineResult.isSuccess) {
            Timber.d("离线翻译成功")
            offlineResult
        } else {
            Timber.d("离线翻译失败，回退到在线翻译")
            performOnlineTranslation(request, startTime)
        }
    }

    /**
     * 执行混合翻译（结合在线和离线结果）
     */
    private suspend fun performHybridTranslation(
        request: TranslationRequest,
        startTime: Long
    ): Result<TranslationResult> {
        return try {
            // 并行执行在线和离线翻译
            val onlineDeferred = kotlinx.coroutines.async { performOnlineTranslation(request, startTime) }
            val offlineDeferred = kotlinx.coroutines.async { performOfflineTranslation(request, startTime) }

            // 等待两个结果
            val onlineResult = try { onlineDeferred.await() } catch (e: Exception) { Result.failure(e) }
            val offlineResult = try { offlineDeferred.await() } catch (e: Exception) { Result.failure(e) }

            // 选择最佳结果
            when {
                onlineResult.isSuccess && offlineResult.isSuccess -> {
                    // 两个都成功，选择置信度更高的
                    val onlineTranslation = onlineResult.getOrThrow()
                    val offlineTranslation = offlineResult.getOrThrow()

                    if (onlineTranslation.translation.confidenceScore >= offlineTranslation.translation.confidenceScore) {
                        Timber.d("混合翻译：选择在线结果（置信度更高）")
                        onlineResult
                    } else {
                        Timber.d("混合翻译：选择离线结果（置信度更高）")
                        offlineResult
                    }
                }
                onlineResult.isSuccess -> {
                    Timber.d("混合翻译：仅在线翻译成功")
                    onlineResult
                }
                offlineResult.isSuccess -> {
                    Timber.d("混合翻译：仅离线翻译成功")
                    offlineResult
                }
                else -> {
                    Timber.w("混合翻译：在线和离线翻译都失败")
                    onlineResult // 返回在线翻译的错误
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "混合翻译异常")
            Result.failure(e)
        }
    }

    /**
     * 从离线翻译结果创建TranslationResult
     */
    private fun createTranslationFromOfflineResult(
        offlineResult: OfflineTranslationResult.Success,
        request: TranslationRequest,
        processingTimeMs: Long
    ): TranslationResult {
        val currentTime = System.currentTimeMillis()

        val translation = Translation(
            id = UUID.randomUUID().toString(),
            sourceText = request.sourceText,
            translatedText = offlineResult.translatedText,
            sourceLanguage = request.sourceLanguage,
            targetLanguage = request.targetLanguage,
            confidenceScore = offlineResult.confidence,
            isFavorite = false,
            createdAt = currentTime,
            updatedAt = currentTime,
            translationType = TranslationType.TEXT,
            culturalContext = null
        )

        return TranslationResult(
            translation = translation,
            isFromCache = false,
            processingTimeMs = processingTimeMs,
            translationSource = "offline"
        )
    }
}

/**
 * 扩展函数：将缓存实体转换为翻译领域模型
 */
private fun TranslationCacheEntity.toTranslation(): Translation {
    return Translation(
        id = translationId,
        sourceText = sourceText,
        translatedText = translatedText,
        sourceLanguage = Language(
            code = sourceLanguageCode,
            name = sourceLanguageName
        ),
        targetLanguage = Language(
            code = targetLanguageCode,
            name = targetLanguageName
        ),
        confidenceScore = confidenceScore,
        isFavorite = false,
        createdAt = createdAt,
        updatedAt = updatedAt,
        translationType = TranslationType.TEXT,
        culturalContext = null
    )
}

/**
 * 扩展函数：将网络翻译结果转换为领域模型
 */
private fun com.cadence.core.network.dto.TranslationResult.toDomain(
    sourceText: String,
    sourceLanguage: Language,
    targetLanguage: Language
): Translation {
    val currentTime = System.currentTimeMillis()

    return Translation(
        id = UUID.randomUUID().toString(),
        sourceText = sourceText,
        translatedText = translatedText,
        sourceLanguage = sourceLanguage,
        targetLanguage = targetLanguage,
        confidenceScore = confidence,
        isFavorite = false,
        createdAt = currentTime,
        updatedAt = currentTime,
        translationType = TranslationType.TEXT,
        culturalContext = null
    )
}

/**
 * 扩展函数：将网络语言检测结果转换为领域模型
 */
private fun com.cadence.core.network.dto.LanguageDetectionResult.toDomain(): LanguageDetection {
    return LanguageDetection(
        detectedLanguage = Language(
            code = detectedLanguage,
            name = getLanguageName(detectedLanguage)
        ),
        confidence = confidence,
        isReliable = isReliable,
        alternatives = emptyList()
    )
}

/**
 * 根据语言代码获取语言名称
 */
private fun getLanguageName(languageCode: String): String {
    return when (languageCode.lowercase()) {
        "zh" -> "中文"
        "en" -> "English"
        "ja" -> "日本語"
        "ko" -> "한국어"
        "fr" -> "Français"
        "de" -> "Deutsch"
        "es" -> "Español"
        else -> languageCode.uppercase()
    }
}
