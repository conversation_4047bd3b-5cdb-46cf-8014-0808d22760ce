package com.cadence.core.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.serialization.Serializable

/**
 * 语言区域数据实体
 * 存储支持的语言和方言信息
 */
@Entity(tableName = "language_regions")
@Serializable
data class LanguageRegionEntity(
    @PrimaryKey
    @ColumnInfo(name = "id")
    val id: String,
    
    @ColumnInfo(name = "language_code")
    val languageCode: String, // zh, en, ja, ko 等
    
    @ColumnInfo(name = "language_name")
    val languageName: String, // 中文, English, 日本語 等
    
    @ColumnInfo(name = "region_code")
    val regionCode: String, // CN, TW, HK, US, UK 等
    
    @ColumnInfo(name = "region_name")
    val regionName: String, // 大陆, 台湾, 香港, 美国, 英国 等
    
    @ColumnInfo(name = "dialect_name")
    val dialectName: String? = null, // 粤语, 闽南语, 客家话 等
    
    @ColumnInfo(name = "is_supported")
    val isSupported: Boolean = true,
    
    @ColumnInfo(name = "display_order")
    val displayOrder: Int = 0,
    
    @ColumnInfo(name = "cultural_info")
    val culturalInfo: String? = null, // 文化背景信息
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long,
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long
)
