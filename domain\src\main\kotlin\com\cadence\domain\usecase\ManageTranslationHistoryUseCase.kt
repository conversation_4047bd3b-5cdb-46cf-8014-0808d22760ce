package com.cadence.domain.usecase

import com.cadence.domain.model.*
import com.cadence.domain.repository.TranslationRepository
import kotlinx.coroutines.flow.Flow
import timber.log.Timber
import javax.inject.Inject

/**
 * 翻译历史管理用例
 * 处理翻译历史记录的增删改查操作
 */
class ManageTranslationHistoryUseCase @Inject constructor(
    private val translationRepository: TranslationRepository
) {
    
    /**
     * 获取翻译历史记录
     * @param query 查询参数
     * @return 翻译记录流
     */
    fun getTranslationHistory(
        query: TranslationHistoryQuery = TranslationHistoryQuery()
    ): Flow<List<Translation>> {
        return translationRepository.getTranslationHistory(query)
    }
    
    /**
     * 获取收藏的翻译记录
     * @return 收藏翻译记录流
     */
    fun getFavoriteTranslations(): Flow<List<Translation>> {
        return translationRepository.getFavoriteTranslations()
    }
    
    /**
     * 搜索翻译记录
     * @param searchQuery 搜索关键词
     * @return 搜索结果流
     */
    fun searchTranslations(searchQuery: String): Flow<List<Translation>> {
        return if (searchQuery.isBlank()) {
            getTranslationHistory()
        } else {
            translationRepository.searchTranslations(searchQuery.trim())
        }
    }
    
    /**
     * 根据ID获取翻译记录
     * @param translationId 翻译ID
     * @return 翻译对象
     */
    suspend fun getTranslationById(translationId: String): Result<Translation?> {
        return try {
            if (translationId.isBlank()) {
                return Result.failure(IllegalArgumentException("翻译ID不能为空"))
            }
            
            translationRepository.getTranslationById(translationId)
        } catch (e: Exception) {
            Timber.e(e, "获取翻译记录失败: $translationId")
            Result.failure(e)
        }
    }
    
    /**
     * 更新收藏状态
     * @param translationId 翻译ID
     * @param isFavorite 是否收藏
     * @return 操作结果
     */
    suspend fun updateFavoriteStatus(
        translationId: String,
        isFavorite: Boolean
    ): Result<Unit> {
        return try {
            if (translationId.isBlank()) {
                return Result.failure(IllegalArgumentException("翻译ID不能为空"))
            }
            
            val result = translationRepository.updateFavoriteStatus(translationId, isFavorite)
            
            if (result.isSuccess) {
                Timber.d("更新收藏状态成功: $translationId -> $isFavorite")
            } else {
                Timber.e("更新收藏状态失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "更新收藏状态过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 删除翻译记录
     * @param translationId 翻译ID
     * @return 操作结果
     */
    suspend fun deleteTranslation(translationId: String): Result<Unit> {
        return try {
            if (translationId.isBlank()) {
                return Result.failure(IllegalArgumentException("翻译ID不能为空"))
            }
            
            val result = translationRepository.deleteTranslation(translationId)
            
            if (result.isSuccess) {
                Timber.d("删除翻译记录成功: $translationId")
            } else {
                Timber.e("删除翻译记录失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "删除翻译记录过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 批量删除翻译记录
     * @param translationIds 翻译ID列表
     * @return 操作结果
     */
    suspend fun deleteTranslations(translationIds: List<String>): Result<Unit> {
        return try {
            if (translationIds.isEmpty()) {
                return Result.success(Unit)
            }
            
            var successCount = 0
            var failureCount = 0
            
            for (id in translationIds) {
                val result = translationRepository.deleteTranslation(id)
                if (result.isSuccess) {
                    successCount++
                } else {
                    failureCount++
                    Timber.w("删除翻译记录失败: $id")
                }
            }
            
            Timber.d("批量删除完成: 成功 $successCount, 失败 $failureCount")
            
            if (failureCount > 0) {
                Result.failure(Exception("部分翻译记录删除失败: $failureCount/$${translationIds.size}"))
            } else {
                Result.success(Unit)
            }
        } catch (e: Exception) {
            Timber.e(e, "批量删除翻译记录过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 清空翻译历史
     * @return 操作结果
     */
    suspend fun clearTranslationHistory(): Result<Unit> {
        return try {
            val result = translationRepository.clearTranslationHistory()
            
            if (result.isSuccess) {
                Timber.d("清空翻译历史成功")
            } else {
                Timber.e("清空翻译历史失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "清空翻译历史过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 删除指定时间之前的翻译记录
     * @param timestamp 时间戳
     * @return 操作结果
     */
    suspend fun deleteTranslationsBefore(timestamp: Long): Result<Unit> {
        return try {
            if (timestamp <= 0) {
                return Result.failure(IllegalArgumentException("时间戳无效"))
            }
            
            val result = translationRepository.deleteTranslationsBefore(timestamp)
            
            if (result.isSuccess) {
                Timber.d("删除历史翻译记录成功: 时间戳 $timestamp")
            } else {
                Timber.e("删除历史翻译记录失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "删除历史翻译记录过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 获取翻译统计信息
     * @return 统计信息
     */
    suspend fun getTranslationStatistics(): Result<TranslationStatistics> {
        return try {
            val result = translationRepository.getTranslationStatistics()
            
            if (result.isSuccess) {
                Timber.d("获取翻译统计信息成功")
            } else {
                Timber.e("获取翻译统计信息失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "获取翻译统计信息过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 导出翻译历史
     * @param format 导出格式
     * @return 导出文件路径
     */
    suspend fun exportTranslationHistory(format: ExportFormat): Result<String> {
        return try {
            val result = translationRepository.exportTranslationHistory(format)
            
            if (result.isSuccess) {
                Timber.d("导出翻译历史成功: ${result.getOrNull()}")
            } else {
                Timber.e("导出翻译历史失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "导出翻译历史过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 导入翻译历史
     * @param filePath 文件路径
     * @return 导入结果
     */
    suspend fun importTranslationHistory(filePath: String): Result<ImportResult> {
        return try {
            if (filePath.isBlank()) {
                return Result.failure(IllegalArgumentException("文件路径不能为空"))
            }
            
            val result = translationRepository.importTranslationHistory(filePath)
            
            if (result.isSuccess) {
                val importResult = result.getOrThrow()
                Timber.d("导入翻译历史完成: 总数 ${importResult.totalCount}, 成功 ${importResult.successCount}, 失败 ${importResult.failureCount}")
            } else {
                Timber.e("导入翻译历史失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "导入翻译历史过程中发生异常")
            Result.failure(e)
        }
    }
}
