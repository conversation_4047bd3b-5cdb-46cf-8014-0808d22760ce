package com.cadence.core.network.dto

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Gemini API响应数据传输对象
 */
@Serializable
data class GeminiResponse(
    @SerialName("candidates")
    val candidates: List<Candidate>? = null,
    
    @SerialName("promptFeedback")
    val promptFeedback: PromptFeedback? = null,
    
    @SerialName("error")
    val error: ApiError? = null
)

@Serializable
data class Candidate(
    @SerialName("content")
    val content: ResponseContent? = null,
    
    @SerialName("finishReason")
    val finishReason: String? = null,
    
    @SerialName("index")
    val index: Int? = null,
    
    @SerialName("safetyRatings")
    val safetyRatings: List<SafetyRating>? = null
)

@Serializable
data class ResponseContent(
    @SerialName("parts")
    val parts: List<ResponsePart>? = null,
    
    @SerialName("role")
    val role: String? = null
)

@Serializable
data class ResponsePart(
    @SerialName("text")
    val text: String? = null
)

@Serializable
data class PromptFeedback(
    @SerialName("safetyRatings")
    val safetyRatings: List<SafetyRating>? = null,
    
    @SerialName("blockReason")
    val blockReason: String? = null
)

@Serializable
data class SafetyRating(
    @SerialName("category")
    val category: String,
    
    @SerialName("probability")
    val probability: String,
    
    @SerialName("blocked")
    val blocked: Boolean? = null
)

@Serializable
data class ApiError(
    @SerialName("code")
    val code: Int,
    
    @SerialName("message")
    val message: String,
    
    @SerialName("status")
    val status: String? = null,
    
    @SerialName("details")
    val details: List<ErrorDetail>? = null
)

@Serializable
data class ErrorDetail(
    @SerialName("@type")
    val type: String? = null,
    
    @SerialName("reason")
    val reason: String? = null,
    
    @SerialName("domain")
    val domain: String? = null,
    
    @SerialName("metadata")
    val metadata: Map<String, String>? = null
)

/**
 * 翻译结果数据类
 */
@Serializable
data class TranslationResult(
    @SerialName("translatedText")
    val translatedText: String,
    
    @SerialName("sourceLanguage")
    val sourceLanguage: String? = null,
    
    @SerialName("targetLanguage")
    val targetLanguage: String? = null,
    
    @SerialName("confidence")
    val confidence: Float? = null,
    
    @SerialName("culturalContext")
    val culturalContext: String? = null
)

/**
 * 语言检测结果
 */
@Serializable
data class LanguageDetectionResult(
    @SerialName("detectedLanguage")
    val detectedLanguage: String,
    
    @SerialName("confidence")
    val confidence: Float,
    
    @SerialName("isReliable")
    val isReliable: Boolean = true
)
