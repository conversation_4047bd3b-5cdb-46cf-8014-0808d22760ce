# 1-Tasks 文件夹 - 任务清单管理

## 📋 文件夹说明

本文件夹专门用于管理Cadence项目的所有开发任务清单，包括功能开发、技术实现、测试验证等各类任务的详细规划和跟踪。

## 📝 文档命名规则

### 命名格式
```
1-[序号]-任务清单-[模块名称]-[版本号].md
```

### 命名示例
- `1-01-任务清单-项目初始化-v1.md`
- `1-02-任务清单-核心功能开发-v1.md`
- `1-03-任务清单-UI界面实现-v1.md`
- `1-04-任务清单-测试验证-v1.md`

### 序号规则
- 使用两位数字格式：01, 02, 03, ..., 99
- 按照开发阶段和优先级排序
- 预留序号空间便于插入新任务

### 版本号规则
- v1: 初始版本
- v2: 重大修订版本
- v3: 后续迭代版本

## 📊 任务清单模板

### 基础信息模板
```markdown
# 1-[序号]-任务清单-[模块名称]-[版本号]

## 📋 基本信息
- **创建时间**: [调用Time Server MCP获取]
- **最后更新**: [调用Time Server MCP获取]
- **负责人**: [开发人员姓名]
- **开发阶段**: [初始化/核心开发/功能实现/测试优化/发布准备]
- **优先级**: [🔥高/🔶中/🔷低]
- **预计工期**: [X天]
- **当前状态**: [📝规划中/🔄进行中/✅已完成/⏸️暂停/❌取消]

## 🎯 任务概述
[详细描述本任务清单的目标和范围]

## 📝 任务列表
### 🔥 高优先级任务
- [ ] **任务1**: [任务描述] - 预计[X]天
  - [ ] 子任务1.1: [详细描述]
  - [ ] 子任务1.2: [详细描述]
- [ ] **任务2**: [任务描述] - 预计[X]天

### 🔶 中优先级任务
- [ ] **任务3**: [任务描述] - 预计[X]天
- [ ] **任务4**: [任务描述] - 预计[X]天

### 🔷 低优先级任务
- [ ] **任务5**: [任务描述] - 预计[X]天

## 🔗 依赖关系
- 任务A 依赖于 [其他任务/模块]
- 任务B 需要等待 [外部条件]

## ✅ 验收标准
1. [功能完整性检查标准]
2. [代码质量审查标准]
3. [性能指标要求]
4. [用户体验验证]

## ⚠️ 风险评估
### 高风险
- **风险1**: [描述] - 应对措施: [方案]

### 中风险
- **风险2**: [描述] - 应对措施: [方案]

## 📈 进度跟踪
- **总任务数**: [X]个
- **已完成**: [X]个 ([X]%)
- **进行中**: [X]个
- **待开始**: [X]个

## 📎 相关资源
- [相关文档链接]
- [技术参考资料]
- [设计稿和原型]
```

## 🕐 时间戳规则

### Time Server MCP调用规范
所有文档的时间戳必须通过Time Server MCP获取，确保时间的准确性和一致性。

#### 调用方式
```
调用 Time Server MCP
时区: Asia/Shanghai
格式: YYYY-MM-DD HH:mm:ss
```

#### 使用场景
1. **文档创建时间**: 文档首次创建时调用
2. **最后更新时间**: 每次修改文档时更新
3. **任务开始时间**: 任务状态变更为"进行中"时记录
4. **任务完成时间**: 任务状态变更为"已完成"时记录
5. **里程碑时间**: 重要节点达成时记录

#### 时间戳格式示例
```markdown
- **创建时间**: 2025-07-28 00:49:42
- **最后更新**: 2025-07-28 00:49:42
- **任务开始**: 2025-07-28 09:00:00
- **预计完成**: 2025-08-05 18:00:00
```

## 📋 任务状态管理

### 状态定义
- **📝 规划中**: 任务正在规划和设计阶段
- **🔄 进行中**: 任务正在开发实施中
- **✅ 已完成**: 任务已完成并通过验收
- **⏸️ 暂停**: 任务因某种原因暂时停止
- **❌ 取消**: 任务被取消不再执行

### 状态变更记录
每次状态变更都需要记录：
```markdown
## 📊 状态变更历史
| 时间 | 状态变更 | 操作人 | 备注 |
|------|----------|--------|------|
| 2025-07-28 00:49:42 | 📝规划中 → 🔄进行中 | [姓名] | 开始开发 |
| 2025-08-05 18:00:00 | 🔄进行中 → ✅已完成 | [姓名] | 通过验收 |
```

## 🔄 任务更新流程

### 日常更新
1. **每日更新**: 更新任务进度和完成情况
2. **状态同步**: 及时更新任务状态
3. **问题记录**: 记录遇到的问题和解决方案
4. **时间更新**: 调用Time Server MCP更新时间戳

### 周期性审查
1. **周审查**: 每周审查任务进度和计划调整
2. **月总结**: 每月总结任务完成情况和经验教训
3. **版本更新**: 重大变更时创建新版本文档

## 📊 质量标准

### 文档质量要求
1. **完整性**: 所有必填字段都必须填写
2. **准确性**: 信息必须准确无误
3. **及时性**: 信息必须及时更新
4. **可读性**: 格式规范，易于理解

### 任务描述要求
1. **具体明确**: 任务描述要具体明确，避免模糊表达
2. **可衡量**: 任务结果要可衡量和验证
3. **可实现**: 任务要在技术和资源范围内可实现
4. **有时限**: 任务要有明确的时间限制

## 🔗 关联文档

### 相关文件夹
- `2-progress/`: 项目进度文档
- `3-requirements/`: 需求规格文档
- `4-design/`: 设计方案文档
- `5-reports/`: 各类报告文档

### 文档关联规则
- 任务清单应引用相关的需求文档
- 完成的任务应在进度文档中体现
- 重要任务应有对应的设计文档
- 问题和风险应在报告文档中记录

---

**维护说明**: 本文档规范了Tasks文件夹的使用方式，所有团队成员都应严格遵循这些规则，确保任务管理的规范性和有效性。

*文档版本: 1.0*  
*创建时间: 2025-07-28 00:49:42*  
*维护人: Cadence开发团队*