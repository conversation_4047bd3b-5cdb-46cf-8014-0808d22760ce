package com.cadence.core.network

import kotlinx.serialization.Serializable

/**
 * 网络请求结果封装类
 * 统一处理成功、错误和加载状态
 */
sealed class NetworkResult<out T> {
    
    /**
     * 请求成功
     */
    data class Success<T>(val data: T) : NetworkResult<T>()
    
    /**
     * 请求失败
     */
    data class Error(
        val exception: Throwable,
        val message: String = exception.message ?: "未知错误",
        val code: Int? = null
    ) : NetworkResult<Nothing>()
    
    /**
     * 加载中
     */
    data object Loading : NetworkResult<Nothing>()
    
    /**
     * 空结果
     */
    data object Empty : NetworkResult<Nothing>()
}

/**
 * 网络错误类型
 */
sealed class NetworkError : Exception() {
    
    /**
     * 网络连接错误
     */
    data object NetworkConnectionError : NetworkError() {
        override val message: String = "网络连接失败，请检查网络设置"
    }
    
    /**
     * 超时错误
     */
    data object TimeoutError : NetworkError() {
        override val message: String = "请求超时，请稍后重试"
    }
    
    /**
     * API密钥错误
     */
    data object ApiKeyError : NetworkError() {
        override val message: String = "API密钥无效或已过期"
    }
    
    /**
     * 请求频率限制
     */
    data object RateLimitError : NetworkError() {
        override val message: String = "请求过于频繁，请稍后重试"
    }
    
    /**
     * 服务器错误
     */
    data class ServerError(val code: Int, override val message: String) : NetworkError()
    
    /**
     * 解析错误
     */
    data class ParseError(override val message: String) : NetworkError()
    
    /**
     * 未知错误
     */
    data class UnknownError(override val message: String) : NetworkError()
}

/**
 * 扩展函数：检查是否成功
 */
fun <T> NetworkResult<T>.isSuccess(): Boolean = this is NetworkResult.Success

/**
 * 扩展函数：检查是否失败
 */
fun <T> NetworkResult<T>.isError(): Boolean = this is NetworkResult.Error

/**
 * 扩展函数：检查是否加载中
 */
fun <T> NetworkResult<T>.isLoading(): Boolean = this is NetworkResult.Loading

/**
 * 扩展函数：获取数据
 */
fun <T> NetworkResult<T>.getDataOrNull(): T? = when (this) {
    is NetworkResult.Success -> data
    else -> null
}

/**
 * 扩展函数：获取错误信息
 */
fun <T> NetworkResult<T>.getErrorOrNull(): NetworkResult.Error? = when (this) {
    is NetworkResult.Error -> this
    else -> null
}

/**
 * 扩展函数：映射数据
 */
inline fun <T, R> NetworkResult<T>.map(transform: (T) -> R): NetworkResult<R> = when (this) {
    is NetworkResult.Success -> NetworkResult.Success(transform(data))
    is NetworkResult.Error -> this
    is NetworkResult.Loading -> this
    is NetworkResult.Empty -> this
}

/**
 * 扩展函数：处理结果
 */
inline fun <T> NetworkResult<T>.onSuccess(action: (T) -> Unit): NetworkResult<T> {
    if (this is NetworkResult.Success) {
        action(data)
    }
    return this
}

/**
 * 扩展函数：处理错误
 */
inline fun <T> NetworkResult<T>.onError(action: (NetworkResult.Error) -> Unit): NetworkResult<T> {
    if (this is NetworkResult.Error) {
        action(this)
    }
    return this
}

/**
 * 扩展函数：处理加载状态
 */
inline fun <T> NetworkResult<T>.onLoading(action: () -> Unit): NetworkResult<T> {
    if (this is NetworkResult.Loading) {
        action()
    }
    return this
}
