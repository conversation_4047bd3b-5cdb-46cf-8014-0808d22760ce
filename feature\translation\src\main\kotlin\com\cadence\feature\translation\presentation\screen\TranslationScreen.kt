package com.cadence.feature.translation.presentation.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cadence.feature.translation.presentation.component.TranslationInputCard
import com.cadence.feature.translation.presentation.component.TranslationResultCard
import com.cadence.feature.translation.presentation.component.TranslationQualityIndicator
import com.cadence.feature.translation.presentation.component.SpeechPermissionDialog
import com.cadence.feature.translation.presentation.component.SpeechErrorSnackbar
import com.cadence.feature.translation.presentation.component.TtsErrorSnackbar
import com.cadence.feature.translation.presentation.viewmodel.TranslationViewModel
import com.cadence.core.speech.SpeechRecognitionService
import com.cadence.core.speech.TextToSpeechService

/**
 * 翻译主界面
 * 包含翻译输入、结果显示和相关功能
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TranslationScreen(
    onNavigateToHistory: () -> Unit,
    onNavigateToSettings: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: TranslationViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val supportedLanguages by viewModel.supportedLanguages.collectAsStateWithLifecycle()
    val userPreference by viewModel.userPreference.collectAsStateWithLifecycle()

    // 语音权限对话框状态
    var showPermissionDialog by remember { mutableStateOf(false) }

    // 错误提示
    LaunchedEffect(uiState.error) {
        uiState.error?.let { error ->
            // TODO: 显示错误提示
            viewModel.clearError()
        }
    }
    
    Scaffold(
        topBar = {
            TranslationTopBar(
                onNavigateToHistory = onNavigateToHistory,
                onNavigateToSettings = onNavigateToSettings,
                onToggleAutoDetect = viewModel::toggleAutoDetectLanguage,
                onToggleCulturalContext = viewModel::toggleCulturalContext,
                autoDetectEnabled = uiState.autoDetectLanguage,
                culturalContextEnabled = uiState.enableCulturalContext
            )
        },
        floatingActionButton = {
            if (uiState.translatedText.isNotEmpty()) {
                FloatingActionButton(
                    onClick = viewModel::clearTranslation,
                    containerColor = MaterialTheme.colorScheme.secondaryContainer
                ) {
                    Icon(
                        imageVector = Icons.Default.Clear,
                        contentDescription = "清空翻译"
                    )
                }
            }
        }
    ) { paddingValues ->
        Column(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Spacer(modifier = Modifier.height(8.dp))
            
            // 翻译输入卡片
            TranslationInputCard(
                sourceText = uiState.sourceText,
                sourceLanguage = uiState.sourceLanguage,
                targetLanguage = uiState.targetLanguage,
                supportedLanguages = supportedLanguages,
                isDetectingLanguage = uiState.isDetectingLanguage,
                autoDetectLanguage = uiState.autoDetectLanguage,
                onSourceTextChange = viewModel::updateSourceText,
                onSourceLanguageChange = viewModel::updateSourceLanguage,
                onTargetLanguageChange = viewModel::updateTargetLanguage,
                onSwapLanguages = viewModel::swapLanguages,
                onDetectLanguage = { viewModel.detectLanguage() },
                onTranslate = viewModel::translate,
                onClearText = { viewModel.updateSourceText("") },
                // 语音相关参数
                isListening = uiState.isListening,
                speechResult = uiState.speechResult,
                hasSpeechPermission = uiState.hasSpeechPermission,
                isSpeechAvailable = uiState.isSpeechAvailable,
                onStartSpeechRecognition = viewModel::startSpeechRecognition,
                onStopSpeechRecognition = viewModel::stopSpeechRecognition,
                onRequestSpeechPermission = { showPermissionDialog = true }
            )
            
            // 翻译结果卡片
            TranslationResultCard(
                translatedText = uiState.translatedText,
                isLoading = uiState.isLoading,
                isFromCache = uiState.isFromCache,
                processingTime = uiState.processingTime,
                culturalContext = uiState.culturalContext,
                currentTranslation = uiState.currentTranslation,
                onCopyTranslation = viewModel::copyTranslation,
                onShareTranslation = viewModel::shareTranslation,
                onSpeakTranslation = { /* TODO: 实现语音播放 */ },
                // 语音播放相关参数
                isPlayingTarget = uiState.isPlayingTarget,
                ttsResult = uiState.ttsResult,
                isTtsAvailable = uiState.isTtsAvailable,
                onPlayTargetText = viewModel::playTargetText,
                onStopSpeechPlayback = viewModel::stopSpeechPlayback
            )
            
            // 翻译质量指示器
            uiState.currentTranslation?.let { translation ->
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "翻译质量",
                            style = MaterialTheme.typography.labelMedium
                        )
                        
                        TranslationQualityIndicator(
                            confidenceScore = translation.confidenceScore
                        )
                    }
                }
            }
            
            // 语言检测置信度
            uiState.detectedLanguageConfidence?.let { confidence ->
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.tertiaryContainer
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "语言检测置信度",
                            style = MaterialTheme.typography.labelMedium,
                            color = MaterialTheme.colorScheme.onTertiaryContainer
                        )
                        
                        Text(
                            text = "${(confidence * 100).toInt()}%",
                            style = MaterialTheme.typography.labelMedium,
                            color = MaterialTheme.colorScheme.onTertiaryContainer
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(80.dp)) // FAB空间
        }
    }
}

/**
 * 翻译界面顶部应用栏
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun TranslationTopBar(
    onNavigateToHistory: () -> Unit,
    onNavigateToSettings: () -> Unit,
    onToggleAutoDetect: () -> Unit,
    onToggleCulturalContext: () -> Unit,
    autoDetectEnabled: Boolean,
    culturalContextEnabled: Boolean,
    modifier: Modifier = Modifier
) {
    var showMenu by remember { mutableStateOf(false) }
    
    TopAppBar(
        title = {
            Text(
                text = "Cadence 翻译",
                style = MaterialTheme.typography.titleLarge
            )
        },
        actions = {
            // 历史记录按钮
            IconButton(onClick = onNavigateToHistory) {
                Icon(
                    imageVector = Icons.Default.History,
                    contentDescription = "翻译历史"
                )
            }
            
            // 更多选项菜单
            IconButton(onClick = { showMenu = true }) {
                Icon(
                    imageVector = Icons.Default.MoreVert,
                    contentDescription = "更多选项"
                )
            }
            
            DropdownMenu(
                expanded = showMenu,
                onDismissRequest = { showMenu = false }
            ) {
                DropdownMenuItem(
                    text = { Text("自动检测语言") },
                    onClick = {
                        onToggleAutoDetect()
                        showMenu = false
                    },
                    leadingIcon = {
                        Icon(
                            imageVector = if (autoDetectEnabled) Icons.Default.CheckBox else Icons.Default.CheckBoxOutlineBlank,
                            contentDescription = null
                        )
                    }
                )
                
                DropdownMenuItem(
                    text = { Text("文化背景解释") },
                    onClick = {
                        onToggleCulturalContext()
                        showMenu = false
                    },
                    leadingIcon = {
                        Icon(
                            imageVector = if (culturalContextEnabled) Icons.Default.CheckBox else Icons.Default.CheckBoxOutlineBlank,
                            contentDescription = null
                        )
                    }
                )
                
                HorizontalDivider()
                
                DropdownMenuItem(
                    text = { Text("设置") },
                    onClick = {
                        onNavigateToSettings()
                        showMenu = false
                    },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.Settings,
                            contentDescription = null
                        )
                    }
                )
            }
        },
        modifier = modifier
    )

    // 语音权限请求对话框
    SpeechPermissionDialog(
        showDialog = showPermissionDialog,
        onDismiss = { showPermissionDialog = false },
        onConfirm = {
            showPermissionDialog = false
            // TODO: 请求权限的逻辑需要在Activity中实现
            // 这里只是更新状态，实际权限请求需要通过Activity
        }
    )

    // 语音识别错误提示
    uiState.speechResult?.let { result ->
        if (result is SpeechRecognitionService.SpeechResult.Error) {
            SpeechErrorSnackbar(
                error = result,
                onDismiss = viewModel::clearSpeechError
            )
        }
    }

    // TTS错误提示
    uiState.ttsResult?.let { result ->
        if (result is TextToSpeechService.TtsResult.Error) {
            TtsErrorSnackbar(
                error = result,
                onDismiss = viewModel::clearSpeechError
            )
        }
    }
}
