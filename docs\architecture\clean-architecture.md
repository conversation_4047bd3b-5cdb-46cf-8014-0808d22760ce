# Clean Architecture 设计说明

## 架构概述

本项目采用Clean Architecture（清洁架构）设计原则，结合Android平台的MVVM模式，构建了一个可维护、可测试、可扩展的应用架构。

## 架构层次

### 1. Presentation Layer (表现层)
**位置**: `app/` + `feature/*/presentation/`

**职责**:
- 用户界面展示
- 用户交互处理
- 状态管理
- 导航控制

**主要组件**:
```kotlin
// Compose UI组件
@Composable
fun MainTranslationScreen(
    viewModel: TranslationViewModel
) { /* UI实现 */ }

// ViewModel状态管理
class TranslationViewModel @Inject constructor(
    private val translateUseCase: TranslateTextUseCase
) : ViewModel() { /* 状态管理逻辑 */ }

// 导航定义
object TranslationDestinations {
    const val MAIN_ROUTE = "translation_main"
    const val BATCH_ROUTE = "translation_batch"
}
```

**依赖关系**:
- 依赖 Domain Layer (通过Use Cases)
- 依赖 Core UI 模块
- 不直接依赖 Data Layer

### 2. Domain Layer (领域层)
**位置**: `domain/`

**职责**:
- 业务逻辑封装
- 领域模型定义
- 用例实现
- 仓库接口定义

**主要组件**:
```kotlin
// 领域模型
data class TranslationResult(
    val originalText: String,
    val translatedText: String,
    val sourceLanguage: Language,
    val targetLanguage: Language,
    val regionalStyle: RegionalStyle,
    val confidence: Float,
    val culturalExplanation: String?,
    val timestamp: Long
)

// 用例实现
class TranslateTextUseCase @Inject constructor(
    private val translationRepository: TranslationRepository
) {
    suspend operator fun invoke(
        text: String,
        sourceLanguage: Language,
        targetLanguage: Language,
        regionalStyle: RegionalStyle
    ): Result<TranslationResult> { /* 业务逻辑 */ }
}

// 仓库接口
interface TranslationRepository {
    suspend fun translate(
        text: String,
        sourceLanguage: Language,
        targetLanguage: Language,
        regionalStyle: RegionalStyle
    ): Result<TranslationResult>
}
```

**依赖关系**:
- 不依赖任何外层
- 只依赖 Core Common 模块
- 定义接口供外层实现

### 3. Data Layer (数据层)
**位置**: `data/`

**职责**:
- 数据获取和存储
- 仓库接口实现
- 数据源管理
- 数据映射转换

**主要组件**:
```kotlin
// 仓库实现
@Singleton
class TranslationRepositoryImpl @Inject constructor(
    private val remoteDataSource: TranslationRemoteDataSource,
    private val localDataSource: TranslationLocalDataSource,
    private val cacheDataSource: TranslationCacheDataSource,
    private val mapper: TranslationMapper
) : TranslationRepository {
    override suspend fun translate(/* 参数 */): Result<TranslationResult> {
        // 1. 检查缓存
        // 2. 调用远程API
        // 3. 保存到本地
        // 4. 返回结果
    }
}

// 远程数据源
class TranslationRemoteDataSource @Inject constructor(
    private val apiService: GeminiApiService,
    private val promptGenerator: RegionalPromptGenerator
) {
    suspend fun translate(/* 参数 */): TranslationResponseDto { /* API调用 */ }
}

// 本地数据源
class TranslationLocalDataSource @Inject constructor(
    private val dao: TranslationDao
) {
    suspend fun saveTranslation(entity: TranslationEntity) { /* 数据库操作 */ }
}
```

**依赖关系**:
- 实现 Domain Layer 的接口
- 依赖 Core Network、Core Database 模块
- 不被 Domain Layer 直接依赖

### 4. Core Modules (核心模块)
**位置**: `core/`

**职责**:
- 提供共享功能
- 基础设施支持
- 通用工具和组件

**模块划分**:
```kotlin
// core/common - 通用工具
object StringUtils {
    fun isValidText(text: String): Boolean { /* 验证逻辑 */ }
}

// core/network - 网络基础设施
@Module
object NetworkModule {
    @Provides
    fun provideOkHttpClient(): OkHttpClient { /* 网络配置 */ }
}

// core/database - 数据库基础设施
@Database(
    entities = [TranslationEntity::class],
    version = 1
)
abstract class AppDatabase : RoomDatabase() { /* 数据库定义 */ }

// core/ui - UI通用组件
@Composable
fun PrimaryButton(
    text: String,
    onClick: () -> Unit
) { /* 通用按钮组件 */ }
```

## 数据流向

### 1. 用户操作流
```
用户交互 → Compose UI → ViewModel → Use Case → Repository → Data Source → 外部API/数据库
```

### 2. 数据返回流
```
外部API/数据库 → Data Source → Repository → Use Case → ViewModel → UI State → Compose UI
```

### 3. 具体示例：翻译流程
```kotlin
// 1. 用户点击翻译按钮
@Composable
fun TranslateButton(onClick: () -> Unit) {
    Button(onClick = onClick) { Text("翻译") }
}

// 2. ViewModel处理用户操作
class TranslationViewModel {
    fun translate() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            val result = translateUseCase(
                text = inputText,
                sourceLanguage = sourceLanguage,
                targetLanguage = targetLanguage,
                regionalStyle = selectedStyle
            )
            
            _uiState.value = when (result) {
                is Success -> _uiState.value.copy(
                    isLoading = false,
                    translationResult = result.data
                )
                is Error -> _uiState.value.copy(
                    isLoading = false,
                    error = result.exception.message
                )
            }
        }
    }
}

// 3. Use Case执行业务逻辑
class TranslateTextUseCase {
    suspend operator fun invoke(/* 参数 */): Result<TranslationResult> {
        return try {
            // 验证输入
            validateInput(text)
            
            // 调用仓库
            val result = translationRepository.translate(/* 参数 */)
            
            // 保存历史记录
            historyRepository.saveTranslation(result)
            
            Success(result)
        } catch (e: Exception) {
            Error(e)
        }
    }
}

// 4. Repository协调数据源
class TranslationRepositoryImpl {
    override suspend fun translate(/* 参数 */): Result<TranslationResult> {
        // 检查缓存
        val cached = cacheDataSource.get(cacheKey)
        if (cached != null) return Success(cached)
        
        // 调用远程API
        val response = remoteDataSource.translate(/* 参数 */)
        val result = mapper.mapToModel(response)
        
        // 缓存结果
        cacheDataSource.put(cacheKey, result)
        
        // 保存到本地数据库
        localDataSource.saveTranslation(mapper.mapToEntity(result))
        
        return Success(result)
    }
}
```

## 依赖注入架构

### 模块组织
```kotlin
// app/di/AppModule.kt - 应用级配置
@Module
@InstallIn(SingletonComponent::class)
object AppModule {
    @Provides
    @Singleton
    fun provideContext(@ApplicationContext context: Context): Context = context
}

// core/network/di/NetworkModule.kt - 网络模块
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    @Provides
    @Singleton
    fun provideGeminiApiService(retrofit: Retrofit): GeminiApiService
}

// data/di/RepositoryModule.kt - 仓库模块
@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {
    @Binds
    abstract fun bindTranslationRepository(
        impl: TranslationRepositoryImpl
    ): TranslationRepository
}

// feature/translation/di/TranslationModule.kt - 功能模块
@Module
@InstallIn(ViewModelComponent::class)
object TranslationModule {
    @Provides
    fun provideTranslateUseCase(
        repository: TranslationRepository
    ): TranslateTextUseCase = TranslateTextUseCase(repository)
}
```

## 错误处理策略

### 1. 分层错误处理
```kotlin
// Domain层异常
sealed class TranslationException : Exception() {
    object InvalidInput : TranslationException()
    object UnsupportedLanguage : TranslationException()
    data class ApiError(val code: Int, val message: String) : TranslationException()
}

// Data层错误映射
class ErrorMapper {
    fun mapToTranslationException(throwable: Throwable): TranslationException {
        return when (throwable) {
            is HttpException -> when (throwable.code()) {
                400 -> TranslationException.InvalidInput
                404 -> TranslationException.UnsupportedLanguage
                else -> TranslationException.ApiError(throwable.code(), throwable.message())
            }
            else -> TranslationException.ApiError(-1, throwable.message ?: "Unknown error")
        }
    }
}

// Presentation层错误展示
class TranslationViewModel {
    private fun handleError(exception: TranslationException) {
        val errorMessage = when (exception) {
            is TranslationException.InvalidInput -> "输入内容无效"
            is TranslationException.UnsupportedLanguage -> "不支持的语言"
            is TranslationException.ApiError -> "翻译服务错误: ${exception.message}"
        }
        _uiState.value = _uiState.value.copy(error = errorMessage)
    }
}
```

### 2. 结果封装
```kotlin
// 通用结果类型
sealed class Result<out T> {
    data class Success<T>(val data: T) : Result<T>()
    data class Error(val exception: Throwable) : Result<Nothing>()
    object Loading : Result<Nothing>()
}

// 扩展函数简化使用
inline fun <T> Result<T>.onSuccess(action: (T) -> Unit): Result<T> {
    if (this is Result.Success) action(data)
    return this
}

inline fun <T> Result<T>.onError(action: (Throwable) -> Unit): Result<T> {
    if (this is Result.Error) action(exception)
    return this
}
```

## 测试策略

### 1. 单元测试
```kotlin
// Domain层测试
class TranslateTextUseCaseTest {
    @Test
    fun `translate text successfully`() = runTest {
        // Given
        val mockRepository = mockk<TranslationRepository>()
        val useCase = TranslateTextUseCase(mockRepository)
        
        // When
        val result = useCase("Hello", Language.ENGLISH, Language.CHINESE, RegionalStyle.BEIJING)
        
        // Then
        assertTrue(result is Result.Success)
    }
}

// ViewModel测试
class TranslationViewModelTest {
    @Test
    fun `translate updates ui state correctly`() = runTest {
        // Given
        val mockUseCase = mockk<TranslateTextUseCase>()
        val viewModel = TranslationViewModel(mockUseCase)
        
        // When
        viewModel.translate()
        
        // Then
        assertEquals(false, viewModel.uiState.value.isLoading)
        assertNotNull(viewModel.uiState.value.translationResult)
    }
}
```

### 2. 集成测试
```kotlin
// Repository集成测试
@HiltAndroidTest
class TranslationRepositoryIntegrationTest {
    @get:Rule
    val hiltRule = HiltAndroidRule(this)
    
    @Inject
    lateinit var repository: TranslationRepository
    
    @Test
    fun `repository integrates all data sources correctly`() = runTest {
        // 测试缓存、网络、数据库的集成
    }
}
```

### 3. UI测试
```kotlin
// Compose UI测试
class TranslationScreenTest {
    @get:Rule
    val composeTestRule = createComposeRule()
    
    @Test
    fun `translation screen displays result correctly`() {
        composeTestRule.setContent {
            TranslationScreen(/* 测试状态 */)
        }
        
        composeTestRule.onNodeWithText("翻译结果").assertIsDisplayed()
    }
}
```

## 架构优势

### 1. 可维护性
- 清晰的职责分离
- 低耦合高内聚
- 易于理解和修改

### 2. 可测试性
- 依赖注入便于模拟
- 分层测试策略
- 高测试覆盖率

### 3. 可扩展性
- 新功能易于添加
- 模块化设计
- 插件式架构

### 4. 可重用性
- 核心模块共享
- 通用组件复用
- 业务逻辑独立

这种Clean Architecture设计确保了项目的长期可维护性和团队协作效率。