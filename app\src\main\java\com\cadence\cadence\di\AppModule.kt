package com.cadence.cadence.di

import android.content.Context
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import timber.log.Timber
import javax.inject.Singleton

/**
 * Cadence应用程序依赖注入模块
 * 
 * 提供应用级别的依赖注入配置，包括：
 * - 应用上下文
 * - 全局配置
 * - 单例服务
 */
@Module
@InstallIn(SingletonComponent::class)
object AppModule {
    
    /**
     * 提供应用上下文
     */
    @Provides
    @Singleton
    fun provideApplicationContext(@ApplicationContext context: Context): Context {
        Timber.d("提供应用上下文")
        return context
    }
    
    /**
     * 提供应用配置
     */
    @Provides
    @Singleton
    fun provideAppConfig(): AppConfig {
        return AppConfig(
            isDebugMode = com.cadence.cadence.BuildConfig.DEBUG,
            versionName = com.cadence.cadence.BuildConfig.VERSION_NAME,
            versionCode = com.cadence.cadence.BuildConfig.VERSION_CODE
        )
    }
}

/**
 * 应用配置数据类
 */
data class AppConfig(
    val isDebugMode: Boolean,
    val versionName: String,
    val versionCode: Int
)