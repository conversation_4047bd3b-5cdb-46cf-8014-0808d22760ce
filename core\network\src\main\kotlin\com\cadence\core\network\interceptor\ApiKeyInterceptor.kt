package com.cadence.core.network.interceptor

import okhttp3.Interceptor
import okhttp3.Response
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * API密钥拦截器
 * 自动为请求添加API密钥认证
 */
@Singleton
class ApiKeyInterceptor @Inject constructor() : Interceptor {
    
    companion object {
        private const val API_KEY_HEADER = "x-goog-api-key"
        private const val API_KEY_QUERY_PARAM = "key"
    }
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        val apiKey = getApiKey()
        
        if (apiKey.isBlank()) {
            Timber.w("API密钥未配置，请检查环境变量或配置文件")
            return chain.proceed(originalRequest)
        }
        
        // 为Gemini API添加密钥
        val newRequest = when {
            originalRequest.url.host.contains("googleapis.com") -> {
                // 使用查询参数方式添加API密钥
                val newUrl = originalRequest.url.newBuilder()
                    .addQueryParameter(API_KEY_QUERY_PARAM, apiKey)
                    .build()
                
                originalRequest.newBuilder()
                    .url(newUrl)
                    .build()
            }
            else -> {
                // 使用Header方式添加API密钥
                originalRequest.newBuilder()
                    .addHeader(API_KEY_HEADER, apiKey)
                    .build()
            }
        }
        
        return chain.proceed(newRequest)
    }
    
    /**
     * 获取API密钥
     * 优先级：环境变量 > BuildConfig > 默认值
     */
    private fun getApiKey(): String {
        // 从环境变量获取
        System.getenv("GEMINI_API_KEY")?.let { return it }
        
        // 从系统属性获取
        System.getProperty("gemini.api.key")?.let { return it }
        
        // 开发环境默认值（生产环境应该移除）
        return ""
    }
}
