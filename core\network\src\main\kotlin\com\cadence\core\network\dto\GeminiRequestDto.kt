package com.cadence.core.network.dto

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Gemini API翻译请求数据传输对象
 */
@Serializable
data class GeminiTranslationRequest(
    @SerialName("contents")
    val contents: List<Content>,
    
    @SerialName("generationConfig")
    val generationConfig: GenerationConfig? = null,
    
    @SerialName("safetySettings")
    val safetySettings: List<SafetySetting>? = null
)

@Serializable
data class Content(
    @SerialName("parts")
    val parts: List<Part>
)

@Serializable
data class Part(
    @SerialName("text")
    val text: String
)

@Serializable
data class GenerationConfig(
    @SerialName("temperature")
    val temperature: Float = 0.1f,
    
    @SerialName("topK")
    val topK: Int = 1,
    
    @SerialName("topP")
    val topP: Float = 0.8f,
    
    @SerialName("maxOutputTokens")
    val maxOutputTokens: Int = 2048,
    
    @SerialName("stopSequences")
    val stopSequences: List<String>? = null
)

@Serializable
data class SafetySetting(
    @SerialName("category")
    val category: String,
    
    @SerialName("threshold")
    val threshold: String
)

/**
 * 语言检测请求
 */
@Serializable
data class LanguageDetectionRequest(
    @SerialName("contents")
    val contents: List<Content>
)

/**
 * 文化背景解释请求
 */
@Serializable
data class CulturalContextRequest(
    @SerialName("contents")
    val contents: List<Content>,
    
    @SerialName("generationConfig")
    val generationConfig: GenerationConfig? = GenerationConfig(
        temperature = 0.3f,
        maxOutputTokens = 1024
    )
)
