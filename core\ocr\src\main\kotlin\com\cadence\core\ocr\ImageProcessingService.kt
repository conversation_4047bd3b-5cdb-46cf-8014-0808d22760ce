package com.cadence.core.ocr

import android.content.Context
import android.graphics.*
import android.net.Uri
import androidx.exifinterface.media.ExifInterface
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.*

/**
 * 图片处理服务
 * 提供图片预处理、优化和格式转换功能
 */
@Singleton
class ImageProcessingService @Inject constructor(
    private val context: Context
) {
    
    /**
     * 图片处理结果
     */
    sealed class ProcessingResult {
        data class Success(val processedBitmap: Bitmap) : ProcessingResult()
        data class Error(val message: String, val errorCode: Int) : ProcessingResult()
    }
    
    /**
     * 图片处理配置
     */
    data class ProcessingConfig(
        val maxWidth: Int = 1024,
        val maxHeight: Int = 1024,
        val quality: Int = 85,
        val autoRotate: Boolean = true,
        val enhanceContrast: Boolean = true,
        val sharpen: Boolean = true,
        val denoiseLevel: Float = 0.3f,
        val cropToText: Boolean = false
    )
    
    // 错误代码常量
    companion object {
        const val ERROR_INVALID_URI = 3001
        const val ERROR_FILE_NOT_FOUND = 3002
        const val ERROR_PROCESSING_FAILED = 3003
        const val ERROR_INSUFFICIENT_MEMORY = 3004
        const val ERROR_UNSUPPORTED_FORMAT = 3005
    }
    
    /**
     * 从URI加载并处理图片
     */
    suspend fun processImageFromUri(
        uri: Uri,
        config: ProcessingConfig = ProcessingConfig()
    ): ProcessingResult = withContext(Dispatchers.IO) {
        try {
            // 加载原始图片
            val originalBitmap = loadBitmapFromUri(uri) ?: return@withContext ProcessingResult.Error(
                "无法加载图片",
                ERROR_FILE_NOT_FOUND
            )
            
            // 处理图片
            processImage(originalBitmap, uri, config)
            
        } catch (e: OutOfMemoryError) {
            ProcessingResult.Error("内存不足", ERROR_INSUFFICIENT_MEMORY)
        } catch (e: Exception) {
            ProcessingResult.Error("图片处理失败: ${e.message}", ERROR_PROCESSING_FAILED)
        }
    }
    
    /**
     * 处理Bitmap图片
     */
    suspend fun processImage(
        bitmap: Bitmap,
        uri: Uri? = null,
        config: ProcessingConfig = ProcessingConfig()
    ): ProcessingResult = withContext(Dispatchers.Default) {
        try {
            var processedBitmap = bitmap
            
            // 1. 自动旋转
            if (config.autoRotate && uri != null) {
                processedBitmap = autoRotateImage(processedBitmap, uri)
            }
            
            // 2. 调整尺寸
            processedBitmap = resizeImage(processedBitmap, config.maxWidth, config.maxHeight)
            
            // 3. 增强对比度
            if (config.enhanceContrast) {
                processedBitmap = enhanceContrast(processedBitmap)
            }
            
            // 4. 锐化
            if (config.sharpen) {
                processedBitmap = sharpenImage(processedBitmap)
            }
            
            // 5. 降噪
            if (config.denoiseLevel > 0) {
                processedBitmap = denoiseImage(processedBitmap, config.denoiseLevel)
            }
            
            // 6. 裁剪到文字区域（如果启用）
            if (config.cropToText) {
                processedBitmap = cropToTextRegion(processedBitmap)
            }
            
            ProcessingResult.Success(processedBitmap)
            
        } catch (e: OutOfMemoryError) {
            ProcessingResult.Error("内存不足", ERROR_INSUFFICIENT_MEMORY)
        } catch (e: Exception) {
            ProcessingResult.Error("图片处理失败: ${e.message}", ERROR_PROCESSING_FAILED)
        }
    }
    
    /**
     * 从URI加载Bitmap
     */
    private fun loadBitmapFromUri(uri: Uri): Bitmap? {
        return try {
            context.contentResolver.openInputStream(uri)?.use { inputStream ->
                // 首先获取图片尺寸
                val options = BitmapFactory.Options().apply {
                    inJustDecodeBounds = true
                }
                BitmapFactory.decodeStream(inputStream, null, options)
                
                // 计算采样率
                val sampleSize = calculateInSampleSize(options, 2048, 2048)
                
                // 重新打开流并解码
                context.contentResolver.openInputStream(uri)?.use { stream ->
                    val decodeOptions = BitmapFactory.Options().apply {
                        inSampleSize = sampleSize
                        inPreferredConfig = Bitmap.Config.ARGB_8888
                    }
                    BitmapFactory.decodeStream(stream, null, decodeOptions)
                }
            }
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 计算采样率
     */
    private fun calculateInSampleSize(
        options: BitmapFactory.Options,
        reqWidth: Int,
        reqHeight: Int
    ): Int {
        val height = options.outHeight
        val width = options.outWidth
        var inSampleSize = 1
        
        if (height > reqHeight || width > reqWidth) {
            val halfHeight = height / 2
            val halfWidth = width / 2
            
            while (halfHeight / inSampleSize >= reqHeight && halfWidth / inSampleSize >= reqWidth) {
                inSampleSize *= 2
            }
        }
        
        return inSampleSize
    }
    
    /**
     * 自动旋转图片
     */
    private fun autoRotateImage(bitmap: Bitmap, uri: Uri): Bitmap {
        return try {
            val inputStream = context.contentResolver.openInputStream(uri)
            val exif = ExifInterface(inputStream!!)
            val orientation = exif.getAttributeInt(
                ExifInterface.TAG_ORIENTATION,
                ExifInterface.ORIENTATION_NORMAL
            )
            
            val rotationDegrees = when (orientation) {
                ExifInterface.ORIENTATION_ROTATE_90 -> 90f
                ExifInterface.ORIENTATION_ROTATE_180 -> 180f
                ExifInterface.ORIENTATION_ROTATE_270 -> 270f
                else -> 0f
            }
            
            if (rotationDegrees != 0f) {
                val matrix = Matrix().apply { postRotate(rotationDegrees) }
                Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
            } else {
                bitmap
            }
        } catch (e: Exception) {
            bitmap
        }
    }
    
    /**
     * 调整图片尺寸
     */
    private fun resizeImage(bitmap: Bitmap, maxWidth: Int, maxHeight: Int): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        
        if (width <= maxWidth && height <= maxHeight) {
            return bitmap
        }
        
        val scaleWidth = maxWidth.toFloat() / width
        val scaleHeight = maxHeight.toFloat() / height
        val scale = minOf(scaleWidth, scaleHeight)
        
        val matrix = Matrix().apply {
            postScale(scale, scale)
        }
        
        return Bitmap.createBitmap(bitmap, 0, 0, width, height, matrix, true)
    }
    
    /**
     * 增强对比度
     */
    private fun enhanceContrast(bitmap: Bitmap): Bitmap {
        val colorMatrix = ColorMatrix().apply {
            set(floatArrayOf(
                1.2f, 0f, 0f, 0f, -20f,
                0f, 1.2f, 0f, 0f, -20f,
                0f, 0f, 1.2f, 0f, -20f,
                0f, 0f, 0f, 1f, 0f
            ))
        }
        
        val paint = Paint().apply {
            colorFilter = ColorMatrixColorFilter(colorMatrix)
        }
        
        val result = Bitmap.createBitmap(bitmap.width, bitmap.height, bitmap.config)
        val canvas = Canvas(result)
        canvas.drawBitmap(bitmap, 0f, 0f, paint)
        
        return result
    }
    
    /**
     * 锐化图片
     */
    private fun sharpenImage(bitmap: Bitmap): Bitmap {
        val kernel = floatArrayOf(
            0f, -1f, 0f,
            -1f, 5f, -1f,
            0f, -1f, 0f
        )
        
        return applyConvolution(bitmap, kernel, 3)
    }
    
    /**
     * 降噪处理
     */
    private fun denoiseImage(bitmap: Bitmap, level: Float): Bitmap {
        // 简单的高斯模糊降噪
        val radius = (level * 2).toInt().coerceIn(1, 5)
        return applyGaussianBlur(bitmap, radius)
    }
    
    /**
     * 应用卷积核
     */
    private fun applyConvolution(bitmap: Bitmap, kernel: FloatArray, kernelSize: Int): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        val result = Bitmap.createBitmap(width, height, bitmap.config)
        
        val pixels = IntArray(width * height)
        bitmap.getPixels(pixels, 0, width, 0, 0, width, height)
        
        val newPixels = IntArray(width * height)
        val offset = kernelSize / 2
        
        for (y in offset until height - offset) {
            for (x in offset until width - offset) {
                var r = 0f
                var g = 0f
                var b = 0f
                
                for (ky in 0 until kernelSize) {
                    for (kx in 0 until kernelSize) {
                        val pixelIndex = (y + ky - offset) * width + (x + kx - offset)
                        val pixel = pixels[pixelIndex]
                        val kernelValue = kernel[ky * kernelSize + kx]
                        
                        r += Color.red(pixel) * kernelValue
                        g += Color.green(pixel) * kernelValue
                        b += Color.blue(pixel) * kernelValue
                    }
                }
                
                val newPixel = Color.rgb(
                    r.toInt().coerceIn(0, 255),
                    g.toInt().coerceIn(0, 255),
                    b.toInt().coerceIn(0, 255)
                )
                
                newPixels[y * width + x] = newPixel
            }
        }
        
        result.setPixels(newPixels, 0, width, 0, 0, width, height)
        return result
    }
    
    /**
     * 应用高斯模糊
     */
    private fun applyGaussianBlur(bitmap: Bitmap, radius: Int): Bitmap {
        val renderScript = android.renderscript.RenderScript.create(context)
        val input = android.renderscript.Allocation.createFromBitmap(renderScript, bitmap)
        val output = android.renderscript.Allocation.createTyped(renderScript, input.type)
        
        val script = android.renderscript.ScriptIntrinsicBlur.create(renderScript, android.renderscript.Element.U8_4(renderScript))
        script.setRadius(radius.toFloat())
        script.setInput(input)
        script.forEach(output)
        
        val result = Bitmap.createBitmap(bitmap.width, bitmap.height, bitmap.config)
        output.copyTo(result)
        
        renderScript.destroy()
        return result
    }
    
    /**
     * 裁剪到文字区域
     */
    private fun cropToTextRegion(bitmap: Bitmap): Bitmap {
        // 简单的边缘检测来找到文字区域
        val grayBitmap = convertToGrayscale(bitmap)
        val edges = detectEdges(grayBitmap)
        val bounds = findTextBounds(edges)
        
        return if (bounds != null) {
            Bitmap.createBitmap(bitmap, bounds.left, bounds.top, bounds.width(), bounds.height())
        } else {
            bitmap
        }
    }
    
    /**
     * 转换为灰度图
     */
    private fun convertToGrayscale(bitmap: Bitmap): Bitmap {
        val colorMatrix = ColorMatrix().apply {
            setSaturation(0f)
        }
        
        val paint = Paint().apply {
            colorFilter = ColorMatrixColorFilter(colorMatrix)
        }
        
        val result = Bitmap.createBitmap(bitmap.width, bitmap.height, bitmap.config)
        val canvas = Canvas(result)
        canvas.drawBitmap(bitmap, 0f, 0f, paint)
        
        return result
    }
    
    /**
     * 边缘检测
     */
    private fun detectEdges(bitmap: Bitmap): Bitmap {
        val sobelX = floatArrayOf(
            -1f, 0f, 1f,
            -2f, 0f, 2f,
            -1f, 0f, 1f
        )
        
        return applyConvolution(bitmap, sobelX, 3)
    }
    
    /**
     * 查找文字边界
     */
    private fun findTextBounds(bitmap: Bitmap): Rect? {
        // 简化实现：返回中心区域
        val width = bitmap.width
        val height = bitmap.height
        val margin = minOf(width, height) / 10
        
        return Rect(margin, margin, width - margin, height - margin)
    }
    
    /**
     * 保存处理后的图片
     */
    suspend fun saveBitmap(bitmap: Bitmap, quality: Int = 85): Uri? = withContext(Dispatchers.IO) {
        try {
            val file = File(context.cacheDir, "processed_${System.currentTimeMillis()}.jpg")
            FileOutputStream(file).use { out ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, quality, out)
            }
            Uri.fromFile(file)
        } catch (e: Exception) {
            null
        }
    }
}
