package com.cadence.core.database.dao

import androidx.room.*
import com.cadence.core.database.entity.DictionaryStatsEntity
import com.cadence.core.database.entity.OfflineDictionaryEntity
import com.cadence.core.database.entity.WordUsageHistoryEntity
import kotlinx.coroutines.flow.Flow

/**
 * 离线词典数据访问对象
 * 提供词典数据的CRUD操作
 */
@Dao
interface OfflineDictionaryDao {
    
    /**
     * 查询单词翻译
     */
    @Query("""
        SELECT * FROM offline_dictionary 
        WHERE source_word = :sourceWord 
        AND source_language = :sourceLanguage 
        AND target_language = :targetLanguage
        ORDER BY confidence_score DESC, word_frequency DESC
        LIMIT 1
    """)
    suspend fun getTranslation(
        sourceWord: String,
        sourceLanguage: String,
        targetLanguage: String
    ): OfflineDictionaryEntity?
    
    /**
     * 模糊查询单词翻译
     */
    @Query("""
        SELECT * FROM offline_dictionary 
        WHERE source_word LIKE '%' || :sourceWord || '%'
        AND source_language = :sourceLanguage 
        AND target_language = :targetLanguage
        ORDER BY 
            CASE WHEN source_word = :sourceWord THEN 0 ELSE 1 END,
            confidence_score DESC, 
            word_frequency DESC
        LIMIT :limit
    """)
    suspend fun searchTranslations(
        sourceWord: String,
        sourceLanguage: String,
        targetLanguage: String,
        limit: Int = 10
    ): List<OfflineDictionaryEntity>
    
    /**
     * 批量查询单词翻译
     */
    @Query("""
        SELECT * FROM offline_dictionary 
        WHERE source_word IN (:sourceWords)
        AND source_language = :sourceLanguage 
        AND target_language = :targetLanguage
        ORDER BY confidence_score DESC, word_frequency DESC
    """)
    suspend fun getTranslations(
        sourceWords: List<String>,
        sourceLanguage: String,
        targetLanguage: String
    ): List<OfflineDictionaryEntity>
    
    /**
     * 获取高频词汇
     */
    @Query("""
        SELECT * FROM offline_dictionary 
        WHERE source_language = :sourceLanguage 
        AND target_language = :targetLanguage
        AND word_frequency > :minFrequency
        ORDER BY word_frequency DESC
        LIMIT :limit
    """)
    suspend fun getHighFrequencyWords(
        sourceLanguage: String,
        targetLanguage: String,
        minFrequency: Int = 100,
        limit: Int = 1000
    ): List<OfflineDictionaryEntity>
    
    /**
     * 获取最近使用的词汇
     */
    @Query("""
        SELECT * FROM offline_dictionary 
        WHERE source_language = :sourceLanguage 
        AND target_language = :targetLanguage
        AND last_used_at IS NOT NULL
        ORDER BY last_used_at DESC
        LIMIT :limit
    """)
    suspend fun getRecentlyUsedWords(
        sourceLanguage: String,
        targetLanguage: String,
        limit: Int = 50
    ): List<OfflineDictionaryEntity>
    
    /**
     * 插入或更新词典条目
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrUpdateDictionaryEntry(entry: OfflineDictionaryEntity): Long
    
    /**
     * 批量插入词典条目
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertDictionaryEntries(entries: List<OfflineDictionaryEntity>)
    
    /**
     * 更新词汇使用统计
     */
    @Query("""
        UPDATE offline_dictionary 
        SET usage_count = usage_count + 1,
            last_used_at = :usedAt,
            updated_at = :usedAt
        WHERE id = :dictionaryId
    """)
    suspend fun updateWordUsage(dictionaryId: Long, usedAt: Long = System.currentTimeMillis())
    
    /**
     * 删除词典条目
     */
    @Delete
    suspend fun deleteDictionaryEntry(entry: OfflineDictionaryEntity)
    
    /**
     * 根据语言对删除词典条目
     */
    @Query("""
        DELETE FROM offline_dictionary 
        WHERE source_language = :sourceLanguage 
        AND target_language = :targetLanguage
    """)
    suspend fun deleteDictionaryByLanguagePair(sourceLanguage: String, targetLanguage: String)
    
    /**
     * 获取词典统计信息
     */
    @Query("SELECT * FROM dictionary_stats WHERE language_pair = :languagePair")
    suspend fun getDictionaryStats(languagePair: String): DictionaryStatsEntity?
    
    /**
     * 获取所有词典统计信息
     */
    @Query("SELECT * FROM dictionary_stats ORDER BY language_pair")
    fun getAllDictionaryStats(): Flow<List<DictionaryStatsEntity>>
    
    /**
     * 插入或更新词典统计信息
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrUpdateDictionaryStats(stats: DictionaryStatsEntity)
    
    /**
     * 记录词汇使用历史
     */
    @Insert
    suspend fun insertWordUsageHistory(history: WordUsageHistoryEntity): Long
    
    /**
     * 获取词汇使用历史
     */
    @Query("""
        SELECT * FROM word_usage_history 
        WHERE dictionary_id = :dictionaryId 
        ORDER BY used_at DESC 
        LIMIT :limit
    """)
    suspend fun getWordUsageHistory(dictionaryId: Long, limit: Int = 10): List<WordUsageHistoryEntity>
    
    /**
     * 清理旧的使用历史记录
     */
    @Query("""
        DELETE FROM word_usage_history 
        WHERE used_at < :cutoffTime
    """)
    suspend fun cleanupOldUsageHistory(cutoffTime: Long)
    
    /**
     * 获取词典条目总数
     */
    @Query("""
        SELECT COUNT(*) FROM offline_dictionary 
        WHERE source_language = :sourceLanguage 
        AND target_language = :targetLanguage
    """)
    suspend fun getDictionaryEntryCount(sourceLanguage: String, targetLanguage: String): Int
    
    /**
     * 获取已验证词汇数量
     */
    @Query("""
        SELECT COUNT(*) FROM offline_dictionary 
        WHERE source_language = :sourceLanguage 
        AND target_language = :targetLanguage
        AND is_verified = 1
    """)
    suspend fun getVerifiedWordCount(sourceLanguage: String, targetLanguage: String): Int
    
    /**
     * 更新词典统计信息
     */
    @Transaction
    suspend fun updateDictionaryStats(sourceLanguage: String, targetLanguage: String) {
        val languagePair = "$sourceLanguage-$targetLanguage"
        val totalWords = getDictionaryEntryCount(sourceLanguage, targetLanguage)
        val verifiedWords = getVerifiedWordCount(sourceLanguage, targetLanguage)
        
        val stats = DictionaryStatsEntity(
            languagePair = languagePair,
            totalWords = totalWords,
            verifiedWords = verifiedWords,
            lastUpdated = System.currentTimeMillis()
        )
        
        insertOrUpdateDictionaryStats(stats)
    }
}
