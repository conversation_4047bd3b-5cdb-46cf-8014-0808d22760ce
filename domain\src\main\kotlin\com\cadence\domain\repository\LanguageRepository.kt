package com.cadence.domain.repository

import com.cadence.domain.model.Language
import com.cadence.domain.model.Region
import kotlinx.coroutines.flow.Flow

/**
 * 语言数据仓库接口
 * 定义语言和地区相关的数据操作契约
 */
interface LanguageRepository {
    
    /**
     * 获取所有支持的语言
     * @return 支持的语言列表流
     */
    fun getSupportedLanguages(): Flow<List<Language>>
    
    /**
     * 根据语言代码获取语言信息
     * @param languageCode 语言代码
     * @return 语言对象
     */
    suspend fun getLanguageByCode(languageCode: String): Result<Language?>
    
    /**
     * 根据语言代码获取所有地区
     * @param languageCode 语言代码
     * @return 地区列表流
     */
    fun getRegionsByLanguage(languageCode: String): Flow<List<Region>>
    
    /**
     * 根据地区代码获取地区信息
     * @param regionCode 地区代码
     * @return 地区对象
     */
    suspend fun getRegionByCode(regionCode: String): Result<Region?>
    
    /**
     * 搜索语言
     * @param query 搜索关键词
     * @return 搜索结果流
     */
    fun searchLanguages(query: String): Flow<List<Language>>
    
    /**
     * 获取常用语言
     * @return 常用语言列表流
     */
    fun getPopularLanguages(): Flow<List<Language>>
    
    /**
     * 获取最近使用的语言
     * @param limit 限制数量
     * @return 最近使用语言列表流
     */
    fun getRecentlyUsedLanguages(limit: Int = 10): Flow<List<Language>>
    
    /**
     * 更新语言使用记录
     * @param language 语言对象
     */
    suspend fun updateLanguageUsage(language: Language): Result<Unit>
    
    /**
     * 添加自定义语言
     * @param language 语言对象
     */
    suspend fun addCustomLanguage(language: Language): Result<Unit>
    
    /**
     * 更新语言信息
     * @param language 语言对象
     */
    suspend fun updateLanguage(language: Language): Result<Unit>
    
    /**
     * 删除自定义语言
     * @param languageCode 语言代码
     */
    suspend fun deleteCustomLanguage(languageCode: String): Result<Unit>
    
    /**
     * 检查语言是否支持
     * @param languageCode 语言代码
     * @return 是否支持
     */
    suspend fun isLanguageSupported(languageCode: String): Boolean
    
    /**
     * 获取语言对支持情况
     * @param sourceLanguage 源语言代码
     * @param targetLanguage 目标语言代码
     * @return 是否支持该语言对翻译
     */
    suspend fun isLanguagePairSupported(sourceLanguage: String, targetLanguage: String): Boolean
    
    /**
     * 初始化默认语言数据
     */
    suspend fun initializeDefaultLanguages(): Result<Unit>
    
    /**
     * 同步语言数据（从远程服务器）
     */
    suspend fun syncLanguageData(): Result<Unit>
}
