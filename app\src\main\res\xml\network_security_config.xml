<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="false">
        <!-- 允许的安全域名 -->
        <domain includeSubdomains="true">generativelanguage.googleapis.com</domain>
        <domain includeSubdomains="true">googleapis.com</domain>
        <domain includeSubdomains="true">google.com</domain>
        
        <!-- 证书固定（可选，用于增强安全性） -->
        <pin-set expiration="2025-12-31">
            <pin digest="SHA-256">AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</pin>
            <pin digest="SHA-256">BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB=</pin>
        </pin-set>
    </domain-config>
    
    <!-- 调试配置（仅在debug版本中启用） -->
    <debug-overrides>
        <trust-anchors>
            <!-- 信任系统证书 -->
            <certificates src="system"/>
            <!-- 信任用户添加的证书（用于调试） -->
            <certificates src="user"/>
        </trust-anchors>
    </debug-overrides>
</network-security-config>