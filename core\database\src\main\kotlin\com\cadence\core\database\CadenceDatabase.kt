package com.cadence.core.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.cadence.core.database.converter.DatabaseConverters
import com.cadence.core.database.dao.*
import com.cadence.core.database.entity.*

/**
 * Cadence应用主数据库
 * 使用Room数据库框架，支持翻译记录、缓存、用户偏好等数据存储
 */
@Database(
    entities = [
        TranslationEntity::class,
        LanguageRegionEntity::class,
        UserPreferenceEntity::class,
        TranslationCacheEntity::class
    ],
    version = 1,
    exportSchema = true
)
@TypeConverters(DatabaseConverters::class)
abstract class CadenceDatabase : RoomDatabase() {
    
    // DAO接口
    abstract fun translationDao(): TranslationDao
    abstract fun languageRegionDao(): LanguageRegionDao
    abstract fun userPreferenceDao(): UserPreferenceDao
    abstract fun translationCacheDao(): TranslationCacheDao
    
    companion object {
        const val DATABASE_NAME = "cadence_database"
        
        @Volatile
        private var INSTANCE: CadenceDatabase? = null
        
        /**
         * 获取数据库实例（单例模式）
         */
        fun getDatabase(context: Context): CadenceDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    CadenceDatabase::class.java,
                    DATABASE_NAME
                )
                    .addCallback(DatabaseCallback())
                    .build()
                INSTANCE = instance
                instance
            }
        }
        
        /**
         * 创建内存数据库（用于测试）
         */
        fun createInMemoryDatabase(context: Context): CadenceDatabase {
            return Room.inMemoryDatabaseBuilder(
                context.applicationContext,
                CadenceDatabase::class.java
            )
                .allowMainThreadQueries()
                .build()
        }
    }
}

/**
 * 数据库回调，用于初始化数据
 */
private class DatabaseCallback : RoomDatabase.Callback() {
    // 可以在这里添加数据库创建后的初始化逻辑
}
