package com.cadence.core.database.dao

import androidx.room.*
import com.cadence.core.database.entity.TranslationCacheEntity
import kotlinx.coroutines.flow.Flow

/**
 * 翻译缓存数据访问对象
 * 提供翻译结果缓存的CRUD操作
 */
@Dao
interface TranslationCacheDao {
    
    /**
     * 插入缓存记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCache(cache: TranslationCacheEntity)
    
    /**
     * 批量插入缓存记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCaches(caches: List<TranslationCacheEntity>)
    
    /**
     * 更新缓存记录
     */
    @Update
    suspend fun updateCache(cache: TranslationCacheEntity)
    
    /**
     * 删除缓存记录
     */
    @Delete
    suspend fun deleteCache(cache: TranslationCacheEntity)
    
    /**
     * 根据缓存键获取缓存
     */
    @Query("SELECT * FROM translation_cache WHERE cache_key = :cacheKey")
    suspend fun getCacheByKey(cacheKey: String): TranslationCacheEntity?
    
    /**
     * 根据源文本和语言对查找缓存
     */
    @Query("""
        SELECT * FROM translation_cache 
        WHERE source_text = :sourceText 
        AND source_language = :sourceLanguage 
        AND target_language = :targetLanguage
        AND (source_region = :sourceRegion OR source_region IS NULL)
        AND (target_region = :targetRegion OR target_region IS NULL)
        AND expires_at > :currentTime
        ORDER BY last_accessed_at DESC
        LIMIT 1
    """)
    suspend fun findCache(
        sourceText: String,
        sourceLanguage: String,
        targetLanguage: String,
        sourceRegion: String?,
        targetRegion: String?,
        currentTime: Long
    ): TranslationCacheEntity?
    
    /**
     * 更新缓存访问信息
     */
    @Query("""
        UPDATE translation_cache 
        SET access_count = access_count + 1, last_accessed_at = :accessTime 
        WHERE cache_key = :cacheKey
    """)
    suspend fun updateCacheAccess(cacheKey: String, accessTime: Long)
    
    /**
     * 删除过期的缓存
     */
    @Query("DELETE FROM translation_cache WHERE expires_at < :currentTime")
    suspend fun deleteExpiredCaches(currentTime: Long)
    
    /**
     * 删除最少使用的缓存（当缓存数量超过限制时）
     */
    @Query("""
        DELETE FROM translation_cache 
        WHERE cache_key IN (
            SELECT cache_key FROM translation_cache 
            ORDER BY access_count ASC, last_accessed_at ASC 
            LIMIT :count
        )
    """)
    suspend fun deleteLeastUsedCaches(count: Int)
    
    /**
     * 获取缓存总数
     */
    @Query("SELECT COUNT(*) FROM translation_cache")
    suspend fun getCacheCount(): Int
    
    /**
     * 获取有效缓存总数（未过期）
     */
    @Query("SELECT COUNT(*) FROM translation_cache WHERE expires_at > :currentTime")
    suspend fun getValidCacheCount(currentTime: Long): Int
    
    /**
     * 清空所有缓存
     */
    @Query("DELETE FROM translation_cache")
    suspend fun clearAllCaches()
    
    /**
     * 获取缓存统计信息
     */
    @Query("""
        SELECT 
            COUNT(*) as total_count,
            SUM(access_count) as total_access_count,
            AVG(access_count) as avg_access_count,
            MAX(last_accessed_at) as last_access_time
        FROM translation_cache 
        WHERE expires_at > :currentTime
    """)
    suspend fun getCacheStatistics(currentTime: Long): CacheStatistics?
    
    /**
     * 根据语言对获取缓存
     */
    @Query("""
        SELECT * FROM translation_cache 
        WHERE source_language = :sourceLanguage 
        AND target_language = :targetLanguage
        AND expires_at > :currentTime
        ORDER BY last_accessed_at DESC
    """)
    fun getCachesByLanguagePair(
        sourceLanguage: String,
        targetLanguage: String,
        currentTime: Long
    ): Flow<List<TranslationCacheEntity>>
}

/**
 * 缓存统计信息数据类
 */
data class CacheStatistics(
    val totalCount: Int,
    val totalAccessCount: Int,
    val avgAccessCount: Double,
    val lastAccessTime: Long
)
