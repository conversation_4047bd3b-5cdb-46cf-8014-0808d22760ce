package com.cadence.core.ocr

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.net.Uri
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.suspendCancellableCoroutine
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * 相机服务
 * 提供拍照和图片处理功能
 */
@Singleton
class CameraService @Inject constructor(
    private val context: Context
) {
    
    /**
     * 相机状态
     */
    sealed class CameraState {
        object Idle : CameraState()
        object Initializing : CameraState()
        object Ready : CameraState()
        object Capturing : CameraState()
        data class Error(val message: String, val errorCode: Int) : CameraState()
    }
    
    /**
     * 拍照结果
     */
    sealed class CaptureResult {
        data class Success(
            val imageUri: Uri,
            val bitmap: Bitmap
        ) : CaptureResult()
        data class Error(
            val message: String,
            val errorCode: Int
        ) : CaptureResult()
    }
    
    /**
     * 相机配置
     */
    data class CameraConfig(
        val lensFacing: Int = CameraSelector.LENS_FACING_BACK,
        val targetResolution: android.util.Size = android.util.Size(1080, 1920),
        val captureMode: Int = ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY,
        val flashMode: Int = ImageCapture.FLASH_MODE_AUTO,
        val enableImageAnalysis: Boolean = false
    )
    
    // 相机状态
    private val _cameraState = MutableStateFlow<CameraState>(CameraState.Idle)
    val cameraState: Flow<CameraState> = _cameraState.asStateFlow()
    
    // 相机组件
    private var cameraProvider: ProcessCameraProvider? = null
    private var imageCapture: ImageCapture? = null
    private var camera: Camera? = null
    
    // 错误代码常量
    companion object {
        const val ERROR_CAMERA_UNAVAILABLE = 2001
        const val ERROR_PERMISSION_DENIED = 2002
        const val ERROR_CAPTURE_FAILED = 2003
        const val ERROR_FILE_CREATION_FAILED = 2004
        const val ERROR_IMAGE_PROCESSING_FAILED = 2005
        const val ERROR_CAMERA_INITIALIZATION_FAILED = 2006
    }
    
    /**
     * 初始化相机
     */
    suspend fun initializeCamera(
        lifecycleOwner: LifecycleOwner,
        config: CameraConfig = CameraConfig()
    ): Boolean {
        return try {
            _cameraState.value = CameraState.Initializing
            
            val cameraProviderFuture = ProcessCameraProvider.getInstance(context)
            
            cameraProvider = suspendCancellableCoroutine { continuation ->
                cameraProviderFuture.addListener({
                    try {
                        continuation.resume(cameraProviderFuture.get())
                    } catch (e: Exception) {
                        continuation.resumeWithException(e)
                    }
                }, ContextCompat.getMainExecutor(context))
            }
            
            setupCamera(lifecycleOwner, config)
            
            _cameraState.value = CameraState.Ready
            true
            
        } catch (e: Exception) {
            _cameraState.value = CameraState.Error(
                "相机初始化失败: ${e.message}",
                ERROR_CAMERA_INITIALIZATION_FAILED
            )
            false
        }
    }
    
    /**
     * 设置相机
     */
    private fun setupCamera(lifecycleOwner: LifecycleOwner, config: CameraConfig) {
        val cameraProvider = this.cameraProvider ?: return
        
        // 创建预览用例
        val preview = Preview.Builder()
            .setTargetResolution(config.targetResolution)
            .build()
        
        // 创建图像捕获用例
        imageCapture = ImageCapture.Builder()
            .setTargetResolution(config.targetResolution)
            .setCaptureMode(config.captureMode)
            .setFlashMode(config.flashMode)
            .build()
        
        // 创建相机选择器
        val cameraSelector = CameraSelector.Builder()
            .requireLensFacing(config.lensFacing)
            .build()
        
        try {
            // 解绑所有用例
            cameraProvider.unbindAll()
            
            // 绑定用例到相机
            val useCases = mutableListOf<UseCase>(preview, imageCapture!!)
            
            // 如果启用图像分析，添加分析用例
            if (config.enableImageAnalysis) {
                val imageAnalyzer = ImageAnalysis.Builder()
                    .setTargetResolution(config.targetResolution)
                    .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                    .build()
                
                useCases.add(imageAnalyzer)
            }
            
            camera = cameraProvider.bindToLifecycle(
                lifecycleOwner,
                cameraSelector,
                *useCases.toTypedArray()
            )
            
        } catch (e: Exception) {
            throw Exception("相机绑定失败: ${e.message}")
        }
    }
    
    /**
     * 拍照
     */
    suspend fun capturePhoto(): CaptureResult {
        return try {
            _cameraState.value = CameraState.Capturing
            
            val imageCapture = this.imageCapture ?: return CaptureResult.Error(
                "相机未初始化",
                ERROR_CAMERA_UNAVAILABLE
            )
            
            // 创建输出文件
            val photoFile = createImageFile()
            val outputFileOptions = ImageCapture.OutputFileOptions.Builder(photoFile).build()
            
            // 执行拍照
            val result = suspendCancellableCoroutine<ImageCapture.OutputFileResults> { continuation ->
                imageCapture.takePicture(
                    outputFileOptions,
                    ContextCompat.getMainExecutor(context),
                    object : ImageCapture.OnImageSavedCallback {
                        override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                            continuation.resume(output)
                        }
                        
                        override fun onError(exception: ImageCaptureException) {
                            continuation.resumeWithException(exception)
                        }
                    }
                )
            }
            
            // 处理拍照结果
            val imageUri = Uri.fromFile(photoFile)
            val bitmap = loadBitmapFromUri(imageUri)
            
            _cameraState.value = CameraState.Ready
            
            CaptureResult.Success(imageUri, bitmap)
            
        } catch (e: Exception) {
            _cameraState.value = CameraState.Ready
            CaptureResult.Error(
                "拍照失败: ${e.message}",
                ERROR_CAPTURE_FAILED
            )
        }
    }
    
    /**
     * 创建图片文件
     */
    private fun createImageFile(): File {
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val imageFileName = "CADENCE_${timeStamp}_"
        val storageDir = File(context.cacheDir, "images")
        
        if (!storageDir.exists()) {
            storageDir.mkdirs()
        }
        
        return File.createTempFile(imageFileName, ".jpg", storageDir)
    }
    
    /**
     * 从URI加载Bitmap
     */
    private fun loadBitmapFromUri(uri: Uri): Bitmap {
        return context.contentResolver.openInputStream(uri)?.use { inputStream ->
            BitmapFactory.decodeStream(inputStream)
        } ?: throw Exception("无法加载图片")
    }
    
    /**
     * 旋转图片
     */
    fun rotateBitmap(bitmap: Bitmap, degrees: Float): Bitmap {
        val matrix = Matrix().apply {
            postRotate(degrees)
        }
        return Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
    }
    
    /**
     * 压缩图片
     */
    fun compressBitmap(bitmap: Bitmap, maxWidth: Int = 1024, maxHeight: Int = 1024): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        
        if (width <= maxWidth && height <= maxHeight) {
            return bitmap
        }
        
        val scaleWidth = maxWidth.toFloat() / width
        val scaleHeight = maxHeight.toFloat() / height
        val scale = minOf(scaleWidth, scaleHeight)
        
        val matrix = Matrix().apply {
            postScale(scale, scale)
        }
        
        return Bitmap.createBitmap(bitmap, 0, 0, width, height, matrix, true)
    }
    
    /**
     * 切换闪光灯模式
     */
    fun toggleFlashMode(): Int {
        val camera = this.camera ?: return ImageCapture.FLASH_MODE_OFF
        
        return if (camera.cameraInfo.hasFlashUnit()) {
            val currentMode = imageCapture?.flashMode ?: ImageCapture.FLASH_MODE_OFF
            val newMode = when (currentMode) {
                ImageCapture.FLASH_MODE_OFF -> ImageCapture.FLASH_MODE_AUTO
                ImageCapture.FLASH_MODE_AUTO -> ImageCapture.FLASH_MODE_ON
                ImageCapture.FLASH_MODE_ON -> ImageCapture.FLASH_MODE_OFF
                else -> ImageCapture.FLASH_MODE_OFF
            }
            imageCapture?.flashMode = newMode
            newMode
        } else {
            ImageCapture.FLASH_MODE_OFF
        }
    }
    
    /**
     * 检查相机权限
     */
    fun hasCameraPermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            android.Manifest.permission.CAMERA
        ) == android.content.pm.PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 检查是否有闪光灯
     */
    fun hasFlashUnit(): Boolean {
        return camera?.cameraInfo?.hasFlashUnit() ?: false
    }
    
    /**
     * 释放相机资源
     */
    fun release() {
        cameraProvider?.unbindAll()
        cameraProvider = null
        imageCapture = null
        camera = null
        _cameraState.value = CameraState.Idle
    }
    
    /**
     * 获取相机状态描述
     */
    fun getStateDescription(state: CameraState): String {
        return when (state) {
            is CameraState.Idle -> "相机未初始化"
            is CameraState.Initializing -> "正在初始化相机..."
            is CameraState.Ready -> "相机就绪"
            is CameraState.Capturing -> "正在拍照..."
            is CameraState.Error -> "相机错误: ${state.message}"
        }
    }
}
