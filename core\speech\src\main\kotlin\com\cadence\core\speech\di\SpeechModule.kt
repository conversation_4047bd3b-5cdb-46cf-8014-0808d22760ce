package com.cadence.core.speech.di

import android.content.Context
import com.cadence.core.speech.SpeechRecognitionService
import com.cadence.core.speech.TextToSpeechService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 语音功能依赖注入模块
 * 提供语音识别和文字转语音服务的依赖注入
 */
@Module
@InstallIn(SingletonComponent::class)
object SpeechModule {
    
    /**
     * 提供语音识别服务
     */
    @Provides
    @Singleton
    fun provideSpeechRecognitionService(
        @ApplicationContext context: Context
    ): SpeechRecognitionService {
        return SpeechRecognitionService(context)
    }
    
    /**
     * 提供文字转语音服务
     */
    @Provides
    @Singleton
    fun provideTextToSpeechService(
        @ApplicationContext context: Context
    ): TextToSpeechService {
        return TextToSpeechService(context)
    }
}
