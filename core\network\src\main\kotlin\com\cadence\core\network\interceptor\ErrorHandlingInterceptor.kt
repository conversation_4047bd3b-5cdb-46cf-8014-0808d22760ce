package com.cadence.core.network.interceptor

import okhttp3.Interceptor
import okhttp3.Response
import timber.log.Timber
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 网络错误处理拦截器
 * 统一处理网络请求错误和重试逻辑
 */
@Singleton
class ErrorHandlingInterceptor @Inject constructor() : Interceptor {
    
    companion object {
        private const val MAX_RETRY_COUNT = 3
        private const val RETRY_DELAY_MS = 1000L
    }
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        var response: Response? = null
        var exception: IOException? = null
        
        // 重试逻辑
        repeat(MAX_RETRY_COUNT) { attempt ->
            try {
                response?.close() // 关闭之前的响应
                response = chain.proceed(request)
                
                // 检查响应状态
                if (response!!.isSuccessful) {
                    return response!!
                } else {
                    handleHttpError(response!!, attempt)
                }
                
            } catch (e: IOException) {
                exception = e
                Timber.w(e, "网络请求失败，尝试次数: ${attempt + 1}")
                
                // 最后一次尝试，抛出异常
                if (attempt == MAX_RETRY_COUNT - 1) {
                    throw e
                }
                
                // 判断是否需要重试
                if (!shouldRetry(e)) {
                    throw e
                }
                
                // 等待后重试
                try {
                    Thread.sleep(RETRY_DELAY_MS * (attempt + 1))
                } catch (ie: InterruptedException) {
                    Thread.currentThread().interrupt()
                    throw IOException("请求被中断", ie)
                }
            }
        }
        
        return response ?: throw (exception ?: IOException("未知网络错误"))
    }
    
    /**
     * 处理HTTP错误响应
     */
    private fun handleHttpError(response: Response, attempt: Int) {
        when (response.code) {
            400 -> Timber.e("请求参数错误: ${response.message}")
            401 -> Timber.e("API密钥无效或已过期")
            403 -> Timber.e("API访问被拒绝，请检查权限")
            404 -> Timber.e("API端点不存在: ${response.request.url}")
            429 -> {
                Timber.w("API请求频率限制，尝试次数: ${attempt + 1}")
                // 对于429错误，增加更长的等待时间
                try {
                    Thread.sleep(RETRY_DELAY_MS * 2 * (attempt + 1))
                } catch (ie: InterruptedException) {
                    Thread.currentThread().interrupt()
                }
            }
            500, 502, 503, 504 -> Timber.w("服务器错误: ${response.code}, 尝试次数: ${attempt + 1}")
            else -> Timber.e("未知HTTP错误: ${response.code} - ${response.message}")
        }
    }
    
    /**
     * 判断是否应该重试
     */
    private fun shouldRetry(exception: IOException): Boolean {
        return when (exception) {
            is SocketTimeoutException -> true // 超时重试
            is UnknownHostException -> false // 网络不可达，不重试
            else -> true // 其他IO异常重试
        }
    }
}
