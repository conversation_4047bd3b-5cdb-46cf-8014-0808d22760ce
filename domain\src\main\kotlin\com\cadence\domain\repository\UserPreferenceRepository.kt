package com.cadence.domain.repository

import com.cadence.domain.model.*
import kotlinx.coroutines.flow.Flow

/**
 * 用户偏好数据仓库接口
 * 定义用户设置相关的数据操作契约
 */
interface UserPreferenceRepository {
    
    /**
     * 获取用户偏好设置
     * @param userId 用户ID
     * @return 用户偏好设置流
     */
    fun getUserPreference(userId: String = "default_user"): Flow<UserPreference?>
    
    /**
     * 保存用户偏好设置
     * @param userPreference 用户偏好设置
     */
    suspend fun saveUserPreference(userPreference: UserPreference): Result<Unit>
    
    /**
     * 更新默认源语言
     * @param language 语言对象
     * @param userId 用户ID
     */
    suspend fun updateDefaultSourceLanguage(
        language: Language,
        userId: String = "default_user"
    ): Result<Unit>
    
    /**
     * 更新默认目标语言
     * @param language 语言对象
     * @param userId 用户ID
     */
    suspend fun updateDefaultTargetLanguage(
        language: Language,
        userId: String = "default_user"
    ): Result<Unit>
    
    /**
     * 更新自动检测语言设置
     * @param enabled 是否启用
     * @param userId 用户ID
     */
    suspend fun updateAutoDetectLanguage(
        enabled: Boolean,
        userId: String = "default_user"
    ): Result<Unit>
    
    /**
     * 更新保存翻译历史设置
     * @param enabled 是否启用
     * @param userId 用户ID
     */
    suspend fun updateSaveTranslationHistory(
        enabled: Boolean,
        userId: String = "default_user"
    ): Result<Unit>
    
    /**
     * 更新文化背景解释设置
     * @param enabled 是否启用
     * @param userId 用户ID
     */
    suspend fun updateEnableCulturalContext(
        enabled: Boolean,
        userId: String = "default_user"
    ): Result<Unit>
    
    /**
     * 更新主题模式
     * @param themeMode 主题模式
     * @param userId 用户ID
     */
    suspend fun updateThemeMode(
        themeMode: ThemeMode,
        userId: String = "default_user"
    ): Result<Unit>
    
    /**
     * 更新字体大小
     * @param fontSize 字体大小
     * @param userId 用户ID
     */
    suspend fun updateFontSize(
        fontSize: FontSize,
        userId: String = "default_user"
    ): Result<Unit>
    
    /**
     * 更新语音输入设置
     * @param enabled 是否启用
     * @param userId 用户ID
     */
    suspend fun updateEnableVoiceInput(
        enabled: Boolean,
        userId: String = "default_user"
    ): Result<Unit>
    
    /**
     * 更新语音输出设置
     * @param enabled 是否启用
     * @param userId 用户ID
     */
    suspend fun updateEnableVoiceOutput(
        enabled: Boolean,
        userId: String = "default_user"
    ): Result<Unit>
    
    /**
     * 更新OCR设置
     * @param enabled 是否启用
     * @param userId 用户ID
     */
    suspend fun updateEnableOcr(
        enabled: Boolean,
        userId: String = "default_user"
    ): Result<Unit>
    
    /**
     * 获取完整的用户设置
     * @param userId 用户ID
     * @return 用户设置流
     */
    fun getUserSettings(userId: String = "default_user"): Flow<UserSettings?>
    
    /**
     * 保存完整的用户设置
     * @param userSettings 用户设置
     */
    suspend fun saveUserSettings(userSettings: UserSettings): Result<Unit>
    
    /**
     * 重置用户偏好设置为默认值
     * @param userId 用户ID
     */
    suspend fun resetToDefault(userId: String = "default_user"): Result<Unit>
    
    /**
     * 导出用户设置
     * @param userId 用户ID
     * @return 导出的JSON字符串
     */
    suspend fun exportUserSettings(userId: String = "default_user"): Result<String>
    
    /**
     * 导入用户设置
     * @param settingsJson 设置JSON字符串
     * @param userId 用户ID
     */
    suspend fun importUserSettings(
        settingsJson: String,
        userId: String = "default_user"
    ): Result<Unit>
    
    /**
     * 删除用户数据
     * @param userId 用户ID
     */
    suspend fun deleteUserData(userId: String = "default_user"): Result<Unit>
}
