# Design Document

## Overview

本设计文档详细描述了地方特色语言翻译安卓应用的系统架构、组件设计和技术实现方案。该应用采用现代化的Android架构模式，集成Gemini API实现地方特色翻译，并提供丰富的用户交互体验。

## Architecture

### 系统架构概述

应用采用MVVM（Model-View-ViewModel）架构模式，结合Clean Architecture原则，确保代码的可维护性和可测试性。

```mermaid
graph TB
    A[Presentation Layer] --> B[Domain Layer]
    B --> C[Data Layer]
    
    A1[Activities/Fragments] --> A2[ViewModels]
    A2 --> A3[UI State]
    
    B1[Use Cases] --> B2[Repository Interfaces]
    B2 --> B3[Domain Models]
    
    C1[Repository Implementations] --> C2[Data Sources]
    C2 --> C3[Local Database]
    C2 --> C4[Remote API]
    C2 --> C5[Preferences]
```

### 核心架构层次

#### 1. Presentation Layer（表现层）
- **Activities/Fragments**: 主界面、翻译界面、历史记录、设置等
- **ViewModels**: 管理UI状态和业务逻辑
- **Compose UI**: 使用Jetpack Compose构建现代化UI
- **Navigation**: 使用Navigation Component管理页面导航

#### 2. Domain Layer（领域层）
- **Use Cases**: 翻译用例、历史管理用例、设置用例等
- **Repository Interfaces**: 数据访问抽象接口
- **Domain Models**: 核心业务模型（Translation, Language, RegionalStyle等）

#### 3. Data Layer（数据层）
- **Repository Implementations**: 具体的数据访问实现
- **Local Data Source**: Room数据库、SharedPreferences
- **Remote Data Source**: Gemini API、语音服务等
- **Cache Management**: 翻译缓存和离线数据管理

## Components and Interfaces

### 1. 核心组件设计

#### TranslationEngine（翻译引擎）
```kotlin
interface TranslationEngine {
    suspend fun translate(
        text: String,
        sourceLanguage: Language,
        targetLanguage: Language,
        regionalStyle: RegionalStyle,
        context: TranslationContext
    ): TranslationResult
    
    suspend fun detectLanguage(text: String): Language
    suspend fun getSupportedStyles(language: Language): List<RegionalStyle>
}
```

#### GeminiTranslationEngine（Gemini翻译引擎实现）
```kotlin
class GeminiTranslationEngine : TranslationEngine {
    private val geminiClient: GeminiClient
    private val promptGenerator: RegionalPromptGenerator
    
    override suspend fun translate(...): TranslationResult {
        val prompt = promptGenerator.generatePrompt(...)
        val response = geminiClient.generateContent(prompt)
        return parseTranslationResponse(response)
    }
}
```

#### RegionalPromptGenerator（地方特色提示词生成器）
```kotlin
class RegionalPromptGenerator {
    fun generatePrompt(
        text: String,
        sourceLanguage: Language,
        targetLanguage: Language,
        regionalStyle: RegionalStyle,
        context: TranslationContext
    ): String {
        return buildString {
            append("请将以下${sourceLanguage.displayName}文本翻译为")
            append("${targetLanguage.displayName}（${regionalStyle.displayName}风格）：\n")
            append("原文：$text\n")
            append("要求：\n")
            append("1. 保持${regionalStyle.displayName}的地方特色和表达习惯\n")
            append("2. 考虑${context.scenario}使用场景\n")
            append("3. 提供文化背景解释（如适用）\n")
            append("4. 确保语言自然流畅\n")
        }
    }
}
```

### 2. 数据模型设计

#### Language（语言模型）
```kotlin
data class Language(
    val code: String,           // "zh", "en", "ja", "ko"
    val displayName: String,    // "中文", "English", "日本語"
    val supportedStyles: List<RegionalStyle>
)
```

#### RegionalStyle（地方风格模型）
```kotlin
data class RegionalStyle(
    val id: String,             // "beijing", "shanghai", "cantonese"
    val displayName: String,    // "北京话", "上海话", "广东话"
    val language: Language,
    val description: String,    // 风格描述
    val culturalNotes: String?  // 文化背景说明
)
```

#### TranslationResult（翻译结果模型）
```kotlin
data class TranslationResult(
    val originalText: String,
    val translatedText: String,
    val sourceLanguage: Language,
    val targetLanguage: Language,
    val regionalStyle: RegionalStyle,
    val confidence: Float,      // 翻译置信度 0.0-1.0
    val culturalExplanation: String?,
    val pronunciationGuide: String?,
    val timestamp: Long
)
```

### 3. UI组件设计

#### MainTranslationScreen（主翻译界面）
```kotlin
@Composable
fun MainTranslationScreen(
    viewModel: TranslationViewModel
) {
    Column {
        // 语言选择区域
        LanguageSelectionRow(
            sourceLanguage = viewModel.sourceLanguage,
            targetLanguage = viewModel.targetLanguage,
            onLanguageSwap = viewModel::swapLanguages
        )
        
        // 地方风格选择
        RegionalStyleSelector(
            language = viewModel.targetLanguage,
            selectedStyle = viewModel.selectedStyle,
            onStyleSelected = viewModel::selectRegionalStyle
        )
        
        // 输入区域
        TranslationInputCard(
            text = viewModel.inputText,
            onTextChanged = viewModel::updateInputText,
            onVoiceInput = viewModel::startVoiceInput,
            onCameraInput = viewModel::startCameraInput
        )
        
        // 翻译按钮
        TranslateButton(
            enabled = viewModel.canTranslate,
            isLoading = viewModel.isTranslating,
            onClick = viewModel::translate
        )
        
        // 结果显示区域
        TranslationResultCard(
            result = viewModel.translationResult,
            onPlayAudio = viewModel::playAudio,
            onSave = viewModel::saveTranslation,
            onShare = viewModel::shareTranslation
        )
    }
}
```

## Data Models

### 数据库设计

#### Translation Entity（翻译记录实体）
```kotlin
@Entity(tableName = "translations")
data class TranslationEntity(
    @PrimaryKey val id: String = UUID.randomUUID().toString(),
    val originalText: String,
    val translatedText: String,
    val sourceLanguageCode: String,
    val targetLanguageCode: String,
    val regionalStyleId: String,
    val confidence: Float,
    val culturalExplanation: String?,
    val pronunciationGuide: String?,
    val timestamp: Long,
    val isFavorite: Boolean = false,
    val tags: String? = null // JSON格式存储标签
)
```

#### RegionalStyle Entity（地方风格实体）
```kotlin
@Entity(tableName = "regional_styles")
data class RegionalStyleEntity(
    @PrimaryKey val id: String,
    val displayName: String,
    val languageCode: String,
    val description: String,
    val culturalNotes: String?,
    val isEnabled: Boolean = true,
    val sortOrder: Int = 0
)
```

#### UserPreferences（用户偏好设置）
```kotlin
data class UserPreferences(
    val defaultSourceLanguage: String = "auto",
    val defaultTargetLanguage: String = "zh",
    val defaultRegionalStyle: String = "standard",
    val autoTranslate: Boolean = false,
    val voiceInputEnabled: Boolean = true,
    val cameraInputEnabled: Boolean = true,
    val offlineMode: Boolean = false,
    val fontSize: FontSize = FontSize.MEDIUM,
    val theme: AppTheme = AppTheme.SYSTEM
)
```

## Error Handling

### 错误处理策略

#### 1. 网络错误处理
```kotlin
sealed class NetworkError : Exception() {
    object NoConnection : NetworkError()
    object Timeout : NetworkError()
    object ServerError : NetworkError()
    data class ApiError(val code: Int, val message: String) : NetworkError()
}

class NetworkErrorHandler {
    fun handleError(error: NetworkError): UserMessage {
        return when (error) {
            is NetworkError.NoConnection -> 
                UserMessage.Error("网络连接不可用，请检查网络设置")
            is NetworkError.Timeout -> 
                UserMessage.Error("请求超时，请稍后重试")
            is NetworkError.ServerError -> 
                UserMessage.Error("服务器暂时不可用，请稍后重试")
            is NetworkError.ApiError -> 
                UserMessage.Error("翻译服务错误：${error.message}")
        }
    }
}
```

#### 2. 翻译错误处理
```kotlin
sealed class TranslationError : Exception() {
    object UnsupportedLanguage : TranslationError()
    object TextTooLong : TranslationError()
    object InvalidInput : TranslationError()
    data class ApiQuotaExceeded(val resetTime: Long) : TranslationError()
}
```

#### 3. 离线模式错误处理
```kotlin
class OfflineModeManager {
    suspend fun handleOfflineTranslation(
        text: String,
        sourceLanguage: Language,
        targetLanguage: Language
    ): Result<TranslationResult> {
        return try {
            val offlineEngine = getOfflineEngine(sourceLanguage, targetLanguage)
            val result = offlineEngine.translate(text)
            Result.success(result.copy(isOffline = true))
        } catch (e: Exception) {
            Result.failure(OfflineError.ModelNotAvailable(targetLanguage))
        }
    }
}
```

## Testing Strategy

### 测试架构

#### 1. 单元测试
- **ViewModel测试**: 使用MockK模拟依赖，测试业务逻辑
- **Use Case测试**: 测试核心业务用例的正确性
- **Repository测试**: 测试数据访问层的实现
- **Utility测试**: 测试工具类和扩展函数

#### 2. 集成测试
- **数据库测试**: 使用Room的测试工具测试数据持久化
- **API测试**: 模拟网络请求测试API集成
- **缓存测试**: 测试离线数据和缓存机制

#### 3. UI测试
- **Compose测试**: 使用Compose Testing框架测试UI组件
- **导航测试**: 测试页面间的导航逻辑
- **用户交互测试**: 测试用户操作流程

#### 4. 端到端测试
```kotlin
@Test
fun completeTranslationFlow() {
    // 启动应用
    composeTestRule.setContent { MainApp() }
    
    // 输入文本
    composeTestRule.onNodeWithTag("input_field")
        .performTextInput("Hello world")
    
    // 选择目标语言和风格
    composeTestRule.onNodeWithTag("language_selector")
        .performClick()
    composeTestRule.onNodeWithText("中文")
        .performClick()
    composeTestRule.onNodeWithText("北京话")
        .performClick()
    
    // 执行翻译
    composeTestRule.onNodeWithTag("translate_button")
        .performClick()
    
    // 验证结果
    composeTestRule.onNodeWithTag("translation_result")
        .assertIsDisplayed()
}
```

### 性能测试

#### 1. 翻译响应时间测试
```kotlin
@Test
fun translationPerformanceTest() {
    val startTime = System.currentTimeMillis()
    
    runBlocking {
        translationEngine.translate(
            text = "测试文本",
            sourceLanguage = Language.CHINESE,
            targetLanguage = Language.ENGLISH,
            regionalStyle = RegionalStyle.AMERICAN_NEW_YORK
        )
    }
    
    val duration = System.currentTimeMillis() - startTime
    assertThat(duration).isLessThan(5000) // 5秒内完成
}
```

#### 2. 内存使用测试
```kotlin
@Test
fun memoryUsageTest() {
    val initialMemory = getUsedMemory()
    
    // 执行大量翻译操作
    repeat(100) {
        performTranslation()
    }
    
    val finalMemory = getUsedMemory()
    val memoryIncrease = finalMemory - initialMemory
    
    assertThat(memoryIncrease).isLessThan(50 * 1024 * 1024) // 50MB以内
}
```

## Security Considerations

### 安全设计原则

#### 1. 数据加密
```kotlin
class EncryptionManager {
    private val keyAlias = "translation_app_key"
    
    fun encryptSensitiveData(data: String): String {
        val keyGenerator = KeyGenerator.getInstance(KeyProperties.KEY_ALGORITHM_AES, "AndroidKeyStore")
        val keyGenParameterSpec = KeyGenParameterSpec.Builder(
            keyAlias,
            KeyProperties.PURPOSE_ENCRYPT or KeyProperties.PURPOSE_DECRYPT
        ).build()
        
        keyGenerator.init(keyGenParameterSpec)
        val secretKey = keyGenerator.generateKey()
        
        val cipher = Cipher.getInstance("AES/GCM/NoPadding")
        cipher.init(Cipher.ENCRYPT_MODE, secretKey)
        
        return Base64.encodeToString(cipher.doFinal(data.toByteArray()), Base64.DEFAULT)
    }
}
```

#### 2. API密钥保护
```kotlin
object ApiKeyManager {
    private const val ENCRYPTED_API_KEY = "encrypted_gemini_key"
    
    fun getApiKey(): String {
        return BuildConfig.GEMINI_API_KEY // 存储在BuildConfig中
    }
    
    fun validateApiKey(key: String): Boolean {
        return key.isNotEmpty() && key.startsWith("AIza")
    }
}
```

#### 3. 网络安全
```kotlin
class SecureNetworkClient {
    private val okHttpClient = OkHttpClient.Builder()
        .certificatePinner(
            CertificatePinner.Builder()
                .add("generativelanguage.googleapis.com", "sha256/...")
                .build()
        )
        .addInterceptor(AuthInterceptor())
        .build()
}
```

#### 4. 用户隐私保护
```kotlin
class PrivacyManager {
    fun anonymizeUserData(text: String): String {
        return text
            .replace(Regex("""\b\d{11}\b"""), "[手机号]")
            .replace(Regex("""\b[\w.-]+@[\w.-]+\.\w+\b"""), "[邮箱]")
            .replace(Regex("""\b\d{15,19}\b"""), "[银行卡号]")
    }
    
    fun shouldStoreTranslation(text: String): Boolean {
        val sensitivePatterns = listOf(
            """\b\d{11}\b""",           // 手机号
            """\b\d{15,19}\b""",        // 银行卡号
            """\b\d{18}\b"""            // 身份证号
        )
        
        return sensitivePatterns.none { pattern ->
            text.contains(Regex(pattern))
        }
    }
}
```

## Performance Optimization

### 性能优化策略

#### 1. 翻译缓存机制
```kotlin
class TranslationCache {
    private val memoryCache = LruCache<String, TranslationResult>(100)
    private val diskCache = DiskLruCache.open(...)
    
    suspend fun get(key: String): TranslationResult? {
        return memoryCache.get(key) ?: diskCache.get(key)?.let { cached ->
            val result = Json.decodeFromString<TranslationResult>(cached)
            memoryCache.put(key, result)
            result
        }
    }
    
    suspend fun put(key: String, result: TranslationResult) {
        memoryCache.put(key, result)
        diskCache.put(key, Json.encodeToString(result))
    }
    
    private fun generateCacheKey(
        text: String,
        sourceLanguage: Language,
        targetLanguage: Language,
        regionalStyle: RegionalStyle
    ): String {
        return "${text.hashCode()}_${sourceLanguage.code}_${targetLanguage.code}_${regionalStyle.id}"
    }
}
```

#### 2. 图片和资源优化
```kotlin
class ResourceOptimizer {
    fun optimizeImages() {
        // 使用WebP格式
        // 提供多种分辨率
        // 延迟加载非关键图片
    }
    
    fun optimizeStrings() {
        // 字符串资源本地化
        // 移除未使用的字符串
        // 压缩字符串资源
    }
}
```

#### 3. 数据库优化
```kotlin
@Dao
interface TranslationDao {
    @Query("SELECT * FROM translations ORDER BY timestamp DESC LIMIT :limit")
    suspend fun getRecentTranslations(limit: Int = 50): List<TranslationEntity>
    
    @Query("SELECT * FROM translations WHERE originalText LIKE :query OR translatedText LIKE :query")
    suspend fun searchTranslations(query: String): List<TranslationEntity>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTranslation(translation: TranslationEntity)
    
    @Query("DELETE FROM translations WHERE timestamp < :cutoffTime")
    suspend fun deleteOldTranslations(cutoffTime: Long)
}
```

#### 4. 内存管理
```kotlin
class MemoryManager {
    fun optimizeMemoryUsage() {
        // 及时释放不需要的对象
        // 使用弱引用避免内存泄漏
        // 监控内存使用情况
    }
    
    fun handleLowMemory() {
        // 清理缓存
        // 释放非关键资源
        // 暂停后台任务
    }
}
```

这个设计文档涵盖了应用的核心架构、组件设计、数据模型、错误处理、测试策略、安全考虑和性能优化等各个方面，为开发团队提供了详细的技术实现指导。