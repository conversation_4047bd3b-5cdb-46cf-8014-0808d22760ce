package com.cadence.feature.translation.presentation.component

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.cadence.core.speech.SpeechRecognitionService

/**
 * 语音输入按钮组件
 * 提供语音识别功能的交互界面
 */
@Composable
fun SpeechInputButton(
    isListening: Boolean,
    isAvailable: <PERSON><PERSON>an,
    hasPermission: <PERSON><PERSON>an,
    onStartListening: () -> Unit,
    onStopListening: () -> Unit,
    onRequestPermission: () -> Unit,
    modifier: Modifier = Modifier,
    speechResult: SpeechRecognitionService.SpeechResult? = null
) {
    // 动画状态
    val scale by animateFloatAsState(
        targetValue = if (isListening) 1.2f else 1.0f,
        animationSpec = tween(300),
        label = "scale"
    )
    
    val infiniteTransition = rememberInfiniteTransition(label = "infinite")
    val pulseAlpha by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 0.8f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulse"
    )
    
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(8.dp),
        modifier = modifier
    ) {
        // 主按钮
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier.size(72.dp)
        ) {
            // 脉冲效果背景
            if (isListening) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .scale(scale)
                        .clip(CircleShape)
                        .background(
                            MaterialTheme.colorScheme.primary.copy(alpha = pulseAlpha)
                        )
                )
            }
            
            // 主按钮
            FloatingActionButton(
                onClick = {
                    when {
                        !hasPermission -> onRequestPermission()
                        !isAvailable -> { /* 显示不可用提示 */ }
                        isListening -> onStopListening()
                        else -> onStartListening()
                    }
                },
                modifier = Modifier
                    .size(56.dp)
                    .scale(scale),
                containerColor = when {
                    !hasPermission -> MaterialTheme.colorScheme.error
                    !isAvailable -> MaterialTheme.colorScheme.outline
                    isListening -> MaterialTheme.colorScheme.error
                    else -> MaterialTheme.colorScheme.primary
                }
            ) {
                Icon(
                    imageVector = when {
                        !hasPermission -> Icons.Default.MicOff
                        !isAvailable -> Icons.Default.MicOff
                        isListening -> Icons.Default.Stop
                        else -> Icons.Default.Mic
                    },
                    contentDescription = when {
                        !hasPermission -> "需要录音权限"
                        !isAvailable -> "语音识别不可用"
                        isListening -> "停止录音"
                        else -> "开始录音"
                    },
                    tint = Color.White
                )
            }
        }
        
        // 状态文本
        AnimatedContent(
            targetState = getStatusText(
                isListening = isListening,
                isAvailable = isAvailable,
                hasPermission = hasPermission,
                speechResult = speechResult
            ),
            transitionSpec = {
                fadeIn(animationSpec = tween(300)) togetherWith 
                fadeOut(animationSpec = tween(300))
            },
            label = "status_text"
        ) { statusText ->
            Text(
                text = statusText,
                style = MaterialTheme.typography.labelMedium,
                color = when {
                    !hasPermission -> MaterialTheme.colorScheme.error
                    !isAvailable -> MaterialTheme.colorScheme.outline
                    isListening -> MaterialTheme.colorScheme.primary
                    else -> MaterialTheme.colorScheme.onSurfaceVariant
                },
                fontWeight = if (isListening) FontWeight.Medium else FontWeight.Normal
            )
        }
        
        // 部分识别结果显示
        speechResult?.let { result ->
            if (result is SpeechRecognitionService.SpeechResult.PartialResult) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .animateContentSize(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.5f)
                    )
                ) {
                    Text(
                        text = result.text,
                        modifier = Modifier.padding(12.dp),
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
            }
        }
    }
}

/**
 * 获取状态文本
 */
private fun getStatusText(
    isListening: Boolean,
    isAvailable: Boolean,
    hasPermission: Boolean,
    speechResult: SpeechRecognitionService.SpeechResult?
): String {
    return when {
        !hasPermission -> "需要录音权限"
        !isAvailable -> "语音识别不可用"
        isListening -> {
            when (speechResult) {
                is SpeechRecognitionService.SpeechResult.Ready -> "准备就绪，请说话"
                is SpeechRecognitionService.SpeechResult.Listening -> "正在聆听..."
                is SpeechRecognitionService.SpeechResult.PartialResult -> "识别中..."
                else -> "正在聆听..."
            }
        }
        else -> "点击开始语音输入"
    }
}

/**
 * 语音识别权限请求对话框
 */
@Composable
fun SpeechPermissionDialog(
    showDialog: Boolean,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit,
    modifier: Modifier = Modifier
) {
    if (showDialog) {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Mic,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Text("语音识别权限")
                }
            },
            text = {
                Text(
                    text = "为了提供语音翻译功能，应用需要录音权限来识别您的语音并转换为文字。\n\n" +
                            "您的语音数据仅用于翻译功能，不会被存储或用于其他目的。",
                    style = MaterialTheme.typography.bodyMedium
                )
            },
            confirmButton = {
                TextButton(onClick = onConfirm) {
                    Text("授予权限")
                }
            },
            dismissButton = {
                TextButton(onClick = onDismiss) {
                    Text("取消")
                }
            },
            modifier = modifier
        )
    }
}

/**
 * 语音识别错误提示
 */
@Composable
fun SpeechErrorSnackbar(
    error: SpeechRecognitionService.SpeechResult.Error?,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    error?.let {
        LaunchedEffect(error) {
            // 可以在这里添加自动消失逻辑
        }
        
        Card(
            modifier = modifier
                .fillMaxWidth()
                .padding(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.errorContainer
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = Icons.Default.Error,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onErrorContainer
                    )
                    Text(
                        text = "语音识别失败: ${error.message}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
                
                IconButton(onClick = onDismiss) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "关闭",
                        tint = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
        }
    }
}
