package com.cadence.cadence

import org.junit.Test
import org.junit.Assert.*

/**
 * Cadence应用程序单元测试示例
 * 
 * 验证基础功能和项目配置
 */
class ExampleUnitTest {
    
    @Test
    fun addition_isCorrect() {
        assertEquals(4, 2 + 2)
    }
    
    @Test
    fun app_name_isCorrect() {
        // 验证应用名称配置
        val expectedAppName = "Cadence"
        // 这里可以添加实际的应用名称验证逻辑
        assertTrue("应用名称应该是Cadence", expectedAppName.isNotEmpty())
    }
    
    @Test
    fun project_config_isValid() {
        // 验证项目配置
        assertTrue("编译SDK版本应该大于0", ProjectConfig.compileSdk > 0)
        assertTrue("最小SDK版本应该大于0", ProjectConfig.minSdk > 0)
        assertTrue("目标SDK版本应该大于等于最小SDK版本", ProjectConfig.targetSdk >= ProjectConfig.minSdk)
        assertTrue("版本名称不应为空", ProjectConfig.versionName.isNotEmpty())
        assertTrue("版本代码应该大于0", ProjectConfig.versionCode > 0)
    }
}