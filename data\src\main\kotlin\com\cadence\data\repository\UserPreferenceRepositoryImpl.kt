package com.cadence.data.repository

import com.cadence.core.database.dao.UserPreferenceDao
import com.cadence.data.mapper.UserPreferenceMapper
import com.cadence.data.mapper.UserPreferenceMapper.toDomain
import com.cadence.data.mapper.UserPreferenceMapper.toEntity
import com.cadence.data.mapper.UserPreferenceMapper.updateTimestamp
import com.cadence.data.mapper.UserPreferenceMapper.createDefaultUserPreference
import com.cadence.domain.model.*
import com.cadence.domain.repository.UserPreferenceRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用户偏好数据仓库实现
 * 管理用户设置和偏好相关的数据操作
 */
@Singleton
class UserPreferenceRepositoryImpl @Inject constructor(
    private val userPreferenceDao: UserPreferenceDao
) : UserPreferenceRepository {
    
    override fun getUserPreference(userId: String): Flow<UserPreference?> {
        return userPreferenceDao.getUserPreference(userId).map { entity ->
            entity?.toDomain()
        }
    }
    
    override suspend fun saveUserPreference(userPreference: UserPreference): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val entity = userPreference.updateTimestamp().toEntity()
                userPreferenceDao.insertOrUpdateUserPreference(entity)
                Timber.d("保存用户偏好设置成功: ${userPreference.id}")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "保存用户偏好设置失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun updateDefaultSourceLanguage(language: Language, userId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                userPreferenceDao.updateDefaultSourceLanguage(
                    userId = userId,
                    languageCode = language.code,
                    languageName = language.name,
                    regionCode = language.region?.code,
                    regionName = language.region?.name,
                    dialectName = language.region?.dialectName,
                    updatedAt = System.currentTimeMillis()
                )
                Timber.d("更新默认源语言成功: ${language.code}")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "更新默认源语言失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun updateDefaultTargetLanguage(language: Language, userId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                userPreferenceDao.updateDefaultTargetLanguage(
                    userId = userId,
                    languageCode = language.code,
                    languageName = language.name,
                    regionCode = language.region?.code,
                    regionName = language.region?.name,
                    dialectName = language.region?.dialectName,
                    updatedAt = System.currentTimeMillis()
                )
                Timber.d("更新默认目标语言成功: ${language.code}")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "更新默认目标语言失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun updateAutoDetectLanguage(enabled: Boolean, userId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                userPreferenceDao.updateAutoDetectLanguage(userId, enabled, System.currentTimeMillis())
                Timber.d("更新自动检测语言设置成功: $enabled")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "更新自动检测语言设置失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun updateSaveTranslationHistory(enabled: Boolean, userId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                userPreferenceDao.updateSaveTranslationHistory(userId, enabled, System.currentTimeMillis())
                Timber.d("更新保存翻译历史设置成功: $enabled")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "更新保存翻译历史设置失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun updateEnableCulturalContext(enabled: Boolean, userId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                userPreferenceDao.updateEnableCulturalContext(userId, enabled, System.currentTimeMillis())
                Timber.d("更新文化背景解释设置成功: $enabled")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "更新文化背景解释设置失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun updateThemeMode(themeMode: ThemeMode, userId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                userPreferenceDao.updateThemeMode(userId, themeMode.name, System.currentTimeMillis())
                Timber.d("更新主题模式成功: $themeMode")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "更新主题模式失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun updateFontSize(fontSize: FontSize, userId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                userPreferenceDao.updateFontSize(userId, fontSize.name, System.currentTimeMillis())
                Timber.d("更新字体大小成功: $fontSize")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "更新字体大小失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun updateEnableVoiceInput(enabled: Boolean, userId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                userPreferenceDao.updateEnableVoiceInput(userId, enabled, System.currentTimeMillis())
                Timber.d("更新语音输入设置成功: $enabled")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "更新语音输入设置失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun updateEnableVoiceOutput(enabled: Boolean, userId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                userPreferenceDao.updateEnableVoiceOutput(userId, enabled, System.currentTimeMillis())
                Timber.d("更新语音输出设置成功: $enabled")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "更新语音输出设置失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun updateEnableOcr(enabled: Boolean, userId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                userPreferenceDao.updateEnableOcr(userId, enabled, System.currentTimeMillis())
                Timber.d("更新OCR设置成功: $enabled")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "更新OCR设置失败")
                Result.failure(e)
            }
        }
    }
    
    override fun getUserSettings(userId: String): Flow<UserSettings?> {
        return getUserPreference(userId).map { userPreference ->
            userPreference?.let {
                UserSettings(
                    userPreference = it,
                    languagePreferences = emptyList(), // TODO: 实现语言偏好
                    translationPreference = TranslationPreference(),
                    privacySettings = PrivacySettings(),
                    notificationSettings = NotificationSettings()
                )
            }
        }
    }
    
    override suspend fun saveUserSettings(userSettings: UserSettings): Result<Unit> {
        return saveUserPreference(userSettings.userPreference)
    }
    
    override suspend fun resetToDefault(userId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val defaultPreference = createDefaultUserPreference(userId)
                val entity = defaultPreference.toEntity()
                userPreferenceDao.insertOrUpdateUserPreference(entity)
                Timber.d("重置用户偏好设置为默认值成功: $userId")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "重置用户偏好设置失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun exportUserSettings(userId: String): Result<String> {
        return withContext(Dispatchers.IO) {
            try {
                // TODO: 实现用户设置导出功能
                Timber.d("用户设置导出功能尚未实现")
                Result.failure(UnsupportedOperationException("用户设置导出功能尚未实现"))
            } catch (e: Exception) {
                Timber.e(e, "导出用户设置失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun importUserSettings(settingsJson: String, userId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                // TODO: 实现用户设置导入功能
                Timber.d("用户设置导入功能尚未实现")
                Result.failure(UnsupportedOperationException("用户设置导入功能尚未实现"))
            } catch (e: Exception) {
                Timber.e(e, "导入用户设置失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun deleteUserData(userId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                userPreferenceDao.deleteUserPreference(userId)
                Timber.d("删除用户数据成功: $userId")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "删除用户数据失败")
                Result.failure(e)
            }
        }
    }
}
