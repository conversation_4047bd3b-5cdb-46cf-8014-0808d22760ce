# 6-Archive 文件夹 - 归档文档管理

## 📦 文件夹说明

本文件夹专门用于归档Cadence项目的历史文档，包括已完成的任务文档、过期的设计方案、历史版本的需求文档、旧版本的报告等，为项目提供完整的历史记录和经验参考。

## 📝 文档命名规则

### 命名格式
```
6-[序号]-归档-[原文档类型]-[归档原因]-[归档日期].md
```

### 命名示例
- `6-01-归档-任务清单-项目完成-20250728.md`
- `6-02-归档-设计方案-方案废弃-20250728.md`
- `6-03-归档-需求规格-需求变更-20250728.md`
- `6-04-归档-测试报告-版本发布-20250728.md`

### 序号规则
- 使用两位数字格式：01, 02, 03, ..., 99
- 按照归档时间顺序排序
- 保持归档文档的时间顺序

### 归档日期格式
- 格式：YYYYMMDD
- 示例：20250728 (2025年7月28日)
- 使用Time Server MCP获取准确日期

## 📊 归档分类体系

### 按原文档类型分类
- **任务归档**: 已完成或取消的任务清单
- **进度归档**: 历史项目进度报告
- **需求归档**: 过期或变更的需求文档
- **设计归档**: 废弃或替换的设计方案
- **报告归档**: 历史测试和分析报告

### 按归档原因分类
- **项目完成**: 项目阶段完成后的归档
- **方案废弃**: 设计方案被废弃或替换
- **需求变更**: 需求发生重大变更
- **版本发布**: 版本发布后的历史文档
- **定期归档**: 定期清理的历史文档

## 📊 归档文档模板

### 基础信息模板
```markdown
# 6-[序号]-归档-[原文档类型]-[归档原因]-[归档日期]

## 📋 归档信息
- **归档时间**: [调用Time Server MCP获取]
- **归档人**: [归档操作人姓名]
- **原文档类型**: [任务清单/进度报告/需求规格/设计方案/测试报告]
- **原文档标题**: [原始文档的完整标题]
- **原文档路径**: [原始文档的原始路径]
- **归档原因**: [项目完成/方案废弃/需求变更/版本发布/定期归档]
- **归档触发事件**: [具体触发归档的事件]

## 📚 原文档概要
### 文档基本信息
- **创建时间**: [原文档创建时间]
- **最后更新**: [原文档最后更新时间]
- **文档版本**: [原文档版本号]
- **负责人**: [原文档负责人]
- **文档状态**: [原文档最终状态]

### 文档内容摘要
[简要描述原文档的主要内容和目的]

### 关键成果
1. **成果1**: [文档产生的关键成果]
2. **成果2**: [文档产生的关键成果]
3. **成果3**: [文档产生的关键成果]

## 🎯 历史价值
### 经验教训
#### 成功经验
1. **经验1**: [成功的做法和经验]
   - **适用场景**: [经验适用的场景]
   - **关键因素**: [成功的关键因素]
   - **可复用性**: [是否可在其他项目复用]

2. **经验2**: [成功的做法和经验]

#### 失败教训
1. **教训1**: [失败的教训和原因]
   - **失败原因**: [具体失败原因分析]
   - **影响程度**: [失败造成的影响]
   - **预防措施**: [如何在未来避免]

2. **教训2**: [失败的教训和原因]

### 技术积累
- **技术方案**: [有价值的技术方案]
- **工具使用**: [有效的工具和方法]
- **最佳实践**: [总结的最佳实践]
- **避坑指南**: [需要避免的问题]

## 📊 数据统计
### 执行数据
- **计划工期**: [原计划工期]
- **实际工期**: [实际执行工期]
- **工期偏差**: [时间偏差分析]
- **资源投入**: [投入的人力资源]
- **成本消耗**: [实际成本消耗]

### 质量数据
- **目标达成率**: [目标完成百分比]
- **质量指标**: [相关质量指标]
- **用户满意度**: [用户反馈评分]
- **缺陷数量**: [发现的问题数量]

### 效率数据
- **生产效率**: [工作效率指标]
- **返工率**: [返工次数和比例]
- **一次通过率**: [一次性通过的比例]

## 🔗 关联文档
### 相关文档列表
- **前置文档**: [依赖的前置文档]
- **后续文档**: [基于此文档的后续文档]
- **并行文档**: [同期进行的相关文档]
- **替代文档**: [替代此文档的新文档]

### 文档演化链
```
[前置文档] → [当前归档文档] → [后续文档]
```

### 影响范围
- **直接影响**: [直接受此文档影响的其他文档]
- **间接影响**: [间接受影响的文档和模块]

## 📈 趋势分析
### 发展趋势
- **技术趋势**: [相关技术的发展趋势]
- **需求趋势**: [需求变化的趋势]
- **团队能力趋势**: [团队能力发展趋势]

### 预测价值
- **未来参考价值**: [对未来项目的参考价值]
- **重用可能性**: [内容重用的可能性]
- **学习价值**: [对团队学习的价值]

## ⚠️ 风险记录
### 已识别风险
1. **风险1**: [当时识别的风险]
   - **风险等级**: [高/中/低]
   - **实际发生**: [是否实际发生]
   - **应对效果**: [应对措施的效果]
   - **经验总结**: [风险管理经验]

### 未预见风险
1. **意外风险1**: [未预见到的风险]
   - **发生原因**: [风险发生的原因]
   - **影响程度**: [实际造成的影响]
   - **应对措施**: [采取的应对措施]
   - **预防建议**: [未来预防建议]

## 📋 决策记录
### 重要决策
1. **决策1**: [重要的技术或业务决策]
   - **决策时间**: [Time Server MCP时间]
   - **决策背景**: [决策的背景和原因]
   - **决策过程**: [决策的过程和参与人]
   - **决策结果**: [决策的最终结果]
   - **执行效果**: [决策执行的效果]

### 决策影响
- **短期影响**: [决策的短期影响]
- **长期影响**: [决策的长期影响]
- **意外后果**: [未预期的后果]

## 🔄 变更历史
### 主要变更记录
| 时间 | 变更类型 | 变更内容 | 变更原因 | 影响评估 |
|------|----------|----------|----------|----------|
| [Time Server MCP时间] | [类型] | [内容] | [原因] | [影响] |
| [Time Server MCP时间] | [类型] | [内容] | [原因] | [影响] |

### 变更模式分析
- **变更频率**: [变更的频率分析]
- **变更类型**: [主要的变更类型]
- **变更原因**: [变更的主要原因]
- **变更影响**: [变更的典型影响]

## 📚 知识提取
### 可复用知识
1. **知识点1**: [可以复用的知识]
   - **适用场景**: [知识适用的场景]
   - **使用方法**: [知识的使用方法]
   - **注意事项**: [使用时的注意事项]

### 方法论总结
- **工作方法**: [有效的工作方法]
- **协作模式**: [成功的协作模式]
- **质量控制**: [质量控制的方法]
- **风险管理**: [风险管理的方法]

### 工具和技术
- **有效工具**: [使用效果好的工具]
- **技术方案**: [成功的技术方案]
- **开发框架**: [适用的开发框架]
- **测试方法**: [有效的测试方法]

## 🔍 检索标签
### 内容标签
- **功能标签**: [功能相关标签]
- **技术标签**: [技术相关标签]
- **业务标签**: [业务相关标签]
- **质量标签**: [质量相关标签]

### 时间标签
- **项目阶段**: [所属项目阶段]
- **开发周期**: [所属开发周期]
- **版本标签**: [相关版本信息]

### 人员标签
- **负责团队**: [负责的团队]
- **关键人员**: [关键参与人员]
- **技能要求**: [涉及的技能要求]

## 📊 归档统计
### 归档数据
- **原文档大小**: [文档大小]
- **包含文件数**: [包含的文件数量]
- **关联文档数**: [关联的文档数量]
- **存储位置**: [归档存储位置]

### 访问统计
- **预期访问频率**: [预期的访问频率]
- **保留期限**: [文档保留期限]
- **销毁计划**: [文档销毁计划]

## 🔒 访问控制
### 访问权限
- **查看权限**: [谁可以查看此归档]
- **编辑权限**: [谁可以编辑此归档]
- **删除权限**: [谁可以删除此归档]

### 安全要求
- **敏感信息**: [是否包含敏感信息]
- **保密等级**: [文档保密等级]
- **访问审计**: [是否需要访问审计]

## 📝 维护计划
### 定期审查
- **审查频率**: [归档文档审查频率]
- **审查内容**: [审查的主要内容]
- **审查责任人**: [负责审查的人员]

### 更新机制
- **更新触发条件**: [何时需要更新归档]
- **更新流程**: [归档更新的流程]
- **版本控制**: [归档文档的版本控制]

## 📎 附件清单
### 原始文件
- **主文档**: [原始主文档文件]
- **附件文档**: [相关附件文档]
- **图片资源**: [相关图片文件]
- **数据文件**: [相关数据文件]

### 备份信息
- **备份位置**: [文档备份位置]
- **备份时间**: [最后备份时间]
- **备份验证**: [备份完整性验证]
```

## 🕐 时间戳管理规范

### Time Server MCP调用要求
所有归档相关的时间信息必须通过Time Server MCP获取，确保归档记录的时间准确性。

#### 关键时间点记录
1. **归档决定时间**: 决定进行归档的时间
2. **归档执行时间**: 实际执行归档操作的时间
3. **原文档创建时间**: 原始文档的创建时间
4. **原文档完成时间**: 原始文档的完成时间
5. **下次审查时间**: 计划的下次审查时间

## 📊 归档策略

### 归档触发条件
1. **项目完成**: 项目阶段性完成
2. **文档过期**: 文档超过有效期
3. **版本更新**: 新版本替代旧版本
4. **需求变更**: 需求发生重大变更
5. **定期清理**: 按计划进行的定期归档

### 归档决策流程
```
文档评估 → 归档决策 → 价值提取 → 执行归档 → 记录维护
```

## 📋 归档质量标准

### 归档完整性
1. **信息完整**: 归档信息必须完整
2. **关联清晰**: 文档关联关系清晰
3. **价值提取**: 充分提取历史价值
4. **经验总结**: 全面总结经验教训

### 归档准确性
1. **时间准确**: 所有时间信息准确
2. **数据准确**: 统计数据准确无误
3. **描述准确**: 文档描述准确客观
4. **分类准确**: 归档分类准确合理

## 🔍 检索和利用

### 检索机制
- **标签检索**: 通过标签快速检索
- **时间检索**: 按时间范围检索
- **内容检索**: 按内容关键词检索
- **关联检索**: 通过关联关系检索

### 利用价值
- **经验参考**: 为新项目提供经验参考
- **知识复用**: 复用有价值的知识和方案
- **风险预防**: 避免重复历史错误
- **培训材料**: 作为团队培训材料

## 🔗 关联管理

### 与活跃文档的关联
- **继承关系**: 新文档对归档文档的继承
- **参考关系**: 新文档对归档文档的参考
- **对比关系**: 新旧文档的对比分析

### 归档文档间的关联
- **时间关联**: 同期归档文档的关联
- **主题关联**: 相同主题文档的关联
- **人员关联**: 相同负责人文档的关联

---

**维护说明**: 本文档规范了Archive文件夹的使用方式，确保历史文档的有效归档和价值保留。所有归档操作都应严格遵循这些规范。

*文档版本: 1.0*  
*创建时间: 2025-07-28 00:49:42*  
*维护人: Cadence开发团队*