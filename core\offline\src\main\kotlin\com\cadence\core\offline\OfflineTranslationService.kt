package com.cadence.core.offline

import android.content.Context
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 离线翻译服务
 * 提供本地机器学习模型的翻译功能
 */
@Singleton
class OfflineTranslationService @Inject constructor(
    private val context: Context,
    private val tensorFlowLiteEngine: TensorFlowLiteEngine,
    private val modelManager: ModelManager
) {
    
    companion object {
        private const val MIN_CONFIDENCE_THRESHOLD = 0.3f
        private const val MAX_TEXT_LENGTH = 500
        
        // 支持的语言对
        private val SUPPORTED_LANGUAGE_PAIRS = setOf(
            "zh-en", "en-zh", // 中英互译
            "zh-ja", "ja-zh", // 中日互译
            "zh-ko", "ko-zh", // 中韩互译
            "en-ja", "ja-en", // 英日互译
            "en-ko", "ko-en"  // 英韩互译
        )
    }
    
    private var isInitialized = false
    
    /**
     * 初始化离线翻译服务
     */
    suspend fun initialize(): Boolean = withContext(Dispatchers.IO) {
        try {
            if (isInitialized) {
                Timber.d("离线翻译服务已初始化")
                return@withContext true
            }
            
            // 检查模型是否可用
            val modelPath = modelManager.getModelPath()
            if (modelPath == null) {
                Timber.w("离线翻译模型不可用")
                return@withContext false
            }
            
            // 初始化TensorFlow Lite引擎
            val success = tensorFlowLiteEngine.initializeModel(modelPath)
            if (success) {
                isInitialized = true
                Timber.d("离线翻译服务初始化成功")
            } else {
                Timber.e("离线翻译服务初始化失败")
            }
            
            success
            
        } catch (e: Exception) {
            Timber.e(e, "离线翻译服务初始化异常")
            false
        }
    }
    
    /**
     * 执行离线翻译
     */
    suspend fun translate(
        text: String,
        sourceLanguage: String,
        targetLanguage: String
    ): OfflineTranslationResult = withContext(Dispatchers.IO) {
        try {
            // 验证输入参数
            val validationResult = validateTranslationRequest(text, sourceLanguage, targetLanguage)
            if (!validationResult.isValid) {
                return@withContext OfflineTranslationResult.Error(validationResult.errorMessage)
            }
            
            // 检查服务是否已初始化
            if (!isInitialized) {
                val initSuccess = initialize()
                if (!initSuccess) {
                    return@withContext OfflineTranslationResult.Error("离线翻译服务初始化失败")
                }
            }
            
            // 执行翻译
            val result = tensorFlowLiteEngine.translate(text, sourceLanguage, targetLanguage)
            
            if (result != null && result.confidence >= MIN_CONFIDENCE_THRESHOLD) {
                OfflineTranslationResult.Success(
                    translatedText = result.translatedText,
                    confidence = result.confidence,
                    sourceLanguage = sourceLanguage,
                    targetLanguage = targetLanguage,
                    processingTimeMs = 0L // TODO: 实际计算处理时间
                )
            } else {
                OfflineTranslationResult.Error("翻译质量不满足要求或翻译失败")
            }
            
        } catch (e: Exception) {
            Timber.e(e, "离线翻译执行异常")
            OfflineTranslationResult.Error("翻译过程中发生异常: ${e.message}")
        }
    }
    
    /**
     * 检查是否支持指定语言对
     */
    fun isSupportedLanguagePair(sourceLanguage: String, targetLanguage: String): Boolean {
        val languagePair = "$sourceLanguage-$targetLanguage"
        return SUPPORTED_LANGUAGE_PAIRS.contains(languagePair)
    }
    
    /**
     * 获取支持的语言列表
     */
    fun getSupportedLanguages(): List<String> {
        return listOf("zh", "en", "ja", "ko")
    }
    
    /**
     * 检查离线翻译是否可用
     */
    fun isAvailable(): Boolean {
        return isInitialized && 
               tensorFlowLiteEngine.isModelReady() && 
               modelManager.isModelAvailable()
    }
    
    /**
     * 获取模型信息
     */
    suspend fun getModelInfo(): ModelInfo? = withContext(Dispatchers.IO) {
        try {
            modelManager.getModelInfo()
        } catch (e: Exception) {
            Timber.e(e, "获取模型信息失败")
            null
        }
    }
    
    /**
     * 释放资源
     */
    fun release() {
        try {
            tensorFlowLiteEngine.release()
            isInitialized = false
            Timber.d("离线翻译服务资源已释放")
        } catch (e: Exception) {
            Timber.e(e, "释放离线翻译服务资源失败")
        }
    }
    
    /**
     * 验证翻译请求
     */
    private fun validateTranslationRequest(
        text: String,
        sourceLanguage: String,
        targetLanguage: String
    ): ValidationResult {
        when {
            text.isBlank() -> {
                return ValidationResult(false, "输入文本不能为空")
            }
            text.length > MAX_TEXT_LENGTH -> {
                return ValidationResult(false, "输入文本长度超过限制($MAX_TEXT_LENGTH字符)")
            }
            sourceLanguage == targetLanguage -> {
                return ValidationResult(false, "源语言和目标语言不能相同")
            }
            !isSupportedLanguagePair(sourceLanguage, targetLanguage) -> {
                return ValidationResult(false, "不支持的语言对: $sourceLanguage -> $targetLanguage")
            }
            else -> {
                return ValidationResult(true, "")
            }
        }
    }
}

/**
 * 离线翻译结果封装类
 */
sealed class OfflineTranslationResult {
    data class Success(
        val translatedText: String,
        val confidence: Float,
        val sourceLanguage: String,
        val targetLanguage: String,
        val processingTimeMs: Long
    ) : OfflineTranslationResult()
    
    data class Error(
        val message: String
    ) : OfflineTranslationResult()
}

/**
 * 验证结果数据类
 */
private data class ValidationResult(
    val isValid: Boolean,
    val errorMessage: String
)

/**
 * 模型信息数据类
 */
data class ModelInfo(
    val version: String,
    val size: Long,
    val supportedLanguages: List<String>,
    val lastUpdated: Long
)
