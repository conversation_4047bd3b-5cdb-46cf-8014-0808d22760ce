package com.cadence.core.database.dao

import androidx.room.*
import com.cadence.core.database.entity.LanguageRegionEntity
import kotlinx.coroutines.flow.Flow

/**
 * 语言区域数据访问对象
 * 提供语言和方言信息的CRUD操作
 */
@Dao
interface LanguageRegionDao {
    
    /**
     * 插入语言区域信息
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertLanguageRegion(languageRegion: LanguageRegionEntity)
    
    /**
     * 批量插入语言区域信息
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertLanguageRegions(languageRegions: List<LanguageRegionEntity>)
    
    /**
     * 更新语言区域信息
     */
    @Update
    suspend fun updateLanguageRegion(languageRegion: LanguageRegionEntity)
    
    /**
     * 删除语言区域信息
     */
    @Delete
    suspend fun deleteLanguageRegion(languageRegion: LanguageRegionEntity)
    
    /**
     * 根据ID获取语言区域信息
     */
    @Query("SELECT * FROM language_regions WHERE id = :id")
    suspend fun getLanguageRegionById(id: String): LanguageRegionEntity?
    
    /**
     * 获取所有支持的语言区域（按显示顺序）
     */
    @Query("SELECT * FROM language_regions WHERE is_supported = 1 ORDER BY display_order ASC")
    fun getSupportedLanguageRegions(): Flow<List<LanguageRegionEntity>>
    
    /**
     * 根据语言代码获取所有区域
     */
    @Query("""
        SELECT * FROM language_regions 
        WHERE language_code = :languageCode 
        AND is_supported = 1 
        ORDER BY display_order ASC
    """)
    fun getRegionsByLanguage(languageCode: String): Flow<List<LanguageRegionEntity>>
    
    /**
     * 获取所有支持的语言（去重）
     */
    @Query("""
        SELECT DISTINCT language_code, language_name 
        FROM language_regions 
        WHERE is_supported = 1 
        ORDER BY display_order ASC
    """)
    fun getSupportedLanguages(): Flow<List<LanguageRegionEntity>>
    
    /**
     * 根据语言和区域代码获取信息
     */
    @Query("""
        SELECT * FROM language_regions 
        WHERE language_code = :languageCode 
        AND region_code = :regionCode
    """)
    suspend fun getLanguageRegion(
        languageCode: String,
        regionCode: String
    ): LanguageRegionEntity?
    
    /**
     * 搜索语言区域
     */
    @Query("""
        SELECT * FROM language_regions 
        WHERE (language_name LIKE '%' || :query || '%' 
        OR region_name LIKE '%' || :query || '%'
        OR dialect_name LIKE '%' || :query || '%')
        AND is_supported = 1
        ORDER BY display_order ASC
    """)
    fun searchLanguageRegions(query: String): Flow<List<LanguageRegionEntity>>
    
    /**
     * 更新支持状态
     */
    @Query("UPDATE language_regions SET is_supported = :isSupported WHERE id = :id")
    suspend fun updateSupportStatus(id: String, isSupported: Boolean)
    
    /**
     * 获取语言区域总数
     */
    @Query("SELECT COUNT(*) FROM language_regions WHERE is_supported = 1")
    suspend fun getSupportedLanguageRegionCount(): Int
    
    /**
     * 清空所有语言区域数据
     */
    @Query("DELETE FROM language_regions")
    suspend fun clearAllLanguageRegions()
}
