package com.cadence.cadence.ui.theme

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Shapes
import androidx.compose.ui.unit.dp

/**
 * Cadence应用程序形状定义
 * 
 * 基于Material Design 3形状系统，
 * 为翻译应用定制的圆角和形状规范
 */

/**
 * Cadence应用程序形状系统
 */
val CadenceShapes = Shapes(
    // 小型组件形状 - 用于按钮、芯片、小卡片
    small = RoundedCornerShape(8.dp),
    
    // 中型组件形状 - 用于卡片、对话框
    medium = RoundedCornerShape(12.dp),
    
    // 大型组件形状 - 用于底部表单、大型容器
    large = RoundedCornerShape(16.dp)
)

/**
 * 扩展形状定义 - 针对特定组件的自定义形状
 */
object CadenceExtendedShapes {
    
    // 翻译卡片形状
    val translationCard = RoundedCornerShape(
        topStart = 16.dp,
        topEnd = 16.dp,
        bottomStart = 16.dp,
        bottomEnd = 16.dp
    )
    
    // 语言选择器形状
    val languageSelector = RoundedCornerShape(20.dp)
    
    // 输入框形状
    val inputField = RoundedCornerShape(12.dp)
    
    // 结果显示区域形状
    val resultContainer = RoundedCornerShape(
        topStart = 0.dp,
        topEnd = 0.dp,
        bottomStart = 16.dp,
        bottomEnd = 16.dp
    )
    
    // 浮动操作按钮形状
    val fab = RoundedCornerShape(16.dp)
    
    // 底部导航栏形状
    val bottomNavigation = RoundedCornerShape(
        topStart = 20.dp,
        topEnd = 20.dp,
        bottomStart = 0.dp,
        bottomEnd = 0.dp
    )
    
    // 文化解释弹窗形状
    val culturalDialog = RoundedCornerShape(20.dp)
    
    // 设置项形状
    val settingsItem = RoundedCornerShape(8.dp)
    
    // 历史记录项形状
    val historyItem = RoundedCornerShape(12.dp)
    
    // 收藏项形状
    val favoriteItem = RoundedCornerShape(12.dp)
    
    // 学习卡片形状
    val learningCard = RoundedCornerShape(16.dp)
    
    // 进度指示器形状
    val progressIndicator = RoundedCornerShape(4.dp)
    
    // 标签形状
    val tag = RoundedCornerShape(16.dp)
    
    // 搜索框形状
    val searchField = RoundedCornerShape(24.dp)
}