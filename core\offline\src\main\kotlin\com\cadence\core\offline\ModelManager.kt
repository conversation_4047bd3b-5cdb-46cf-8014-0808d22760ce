package com.cadence.core.offline

import android.content.Context
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.security.MessageDigest
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 模型管理器
 * 负责离线翻译模型的存储、验证和管理
 */
@Singleton
class ModelManager @Inject constructor(
    private val context: Context
) {
    
    companion object {
        private const val MODEL_DIR_NAME = "offline_models"
        private const val MODEL_FILE_NAME = "translation_model.tflite"
        private const val MODEL_INFO_FILE = "model_info.json"
        private const val VOCAB_FILE_NAME = "vocab.txt"
        
        // 模型版本和校验信息
        private const val CURRENT_MODEL_VERSION = "1.0.0"
        private const val MODEL_CHECKSUM = "placeholder_checksum" // 实际部署时需要真实校验值
    }
    
    private val modelDir: File by lazy {
        File(context.filesDir, MODEL_DIR_NAME).apply {
            if (!exists()) {
                mkdirs()
            }
        }
    }
    
    private val modelFile: File by lazy {
        File(modelDir, MODEL_FILE_NAME)
    }
    
    private val vocabFile: File by lazy {
        File(modelDir, VOCAB_FILE_NAME)
    }
    
    private val modelInfoFile: File by lazy {
        File(modelDir, MODEL_INFO_FILE)
    }
    
    /**
     * 检查模型是否可用
     */
    fun isModelAvailable(): Boolean {
        return modelFile.exists() && 
               vocabFile.exists() && 
               validateModelIntegrity()
    }
    
    /**
     * 获取模型文件路径
     */
    fun getModelPath(): String? {
        return if (isModelAvailable()) {
            modelFile.absolutePath
        } else {
            null
        }
    }
    
    /**
     * 获取词汇表文件路径
     */
    fun getVocabPath(): String? {
        return if (vocabFile.exists()) {
            vocabFile.absolutePath
        } else {
            null
        }
    }
    
    /**
     * 获取模型信息
     */
    suspend fun getModelInfo(): ModelInfo? = withContext(Dispatchers.IO) {
        try {
            if (!isModelAvailable()) {
                return@withContext null
            }
            
            ModelInfo(
                version = CURRENT_MODEL_VERSION,
                size = modelFile.length(),
                supportedLanguages = listOf("zh", "en", "ja", "ko"),
                lastUpdated = modelFile.lastModified()
            )
        } catch (e: Exception) {
            Timber.e(e, "获取模型信息失败")
            null
        }
    }
    
    /**
     * 保存模型文件
     */
    suspend fun saveModel(
        modelData: ByteArray,
        vocabData: ByteArray
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            // 确保目录存在
            if (!modelDir.exists()) {
                modelDir.mkdirs()
            }
            
            // 保存模型文件
            modelFile.writeBytes(modelData)
            Timber.d("模型文件保存成功: ${modelFile.absolutePath}")
            
            // 保存词汇表文件
            vocabFile.writeBytes(vocabData)
            Timber.d("词汇表文件保存成功: ${vocabFile.absolutePath}")
            
            // 验证文件完整性
            if (validateModelIntegrity()) {
                // 保存模型信息
                saveModelInfo()
                Timber.d("模型保存和验证完成")
                true
            } else {
                // 验证失败，删除文件
                deleteModel()
                Timber.e("模型验证失败，已删除文件")
                false
            }
            
        } catch (e: Exception) {
            Timber.e(e, "保存模型失败")
            // 清理可能的部分文件
            deleteModel()
            false
        }
    }
    
    /**
     * 删除模型文件
     */
    suspend fun deleteModel(): Boolean = withContext(Dispatchers.IO) {
        try {
            var success = true
            
            if (modelFile.exists()) {
                success = success && modelFile.delete()
            }
            
            if (vocabFile.exists()) {
                success = success && vocabFile.delete()
            }
            
            if (modelInfoFile.exists()) {
                success = success && modelInfoFile.delete()
            }
            
            if (success) {
                Timber.d("模型文件删除成功")
            } else {
                Timber.w("部分模型文件删除失败")
            }
            
            success
        } catch (e: Exception) {
            Timber.e(e, "删除模型文件失败")
            false
        }
    }
    
    /**
     * 获取模型目录大小
     */
    suspend fun getModelDirectorySize(): Long = withContext(Dispatchers.IO) {
        try {
            calculateDirectorySize(modelDir)
        } catch (e: Exception) {
            Timber.e(e, "计算模型目录大小失败")
            0L
        }
    }
    
    /**
     * 验证模型完整性
     */
    private fun validateModelIntegrity(): Boolean {
        return try {
            // 检查文件是否存在
            if (!modelFile.exists() || !vocabFile.exists()) {
                Timber.w("模型文件不完整")
                return false
            }
            
            // 检查文件大小
            if (modelFile.length() == 0L || vocabFile.length() == 0L) {
                Timber.w("模型文件大小为0")
                return false
            }
            
            // 简化的完整性检查（实际部署时应使用真实的校验和）
            val modelChecksum = calculateFileChecksum(modelFile)
            if (modelChecksum.isEmpty()) {
                Timber.w("无法计算模型文件校验和")
                return false
            }
            
            Timber.d("模型完整性验证通过")
            true
            
        } catch (e: Exception) {
            Timber.e(e, "模型完整性验证失败")
            false
        }
    }
    
    /**
     * 计算文件校验和
     */
    private fun calculateFileChecksum(file: File): String {
        return try {
            val digest = MessageDigest.getInstance("SHA-256")
            val bytes = file.readBytes()
            val hashBytes = digest.digest(bytes)
            hashBytes.joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            Timber.e(e, "计算文件校验和失败")
            ""
        }
    }
    
    /**
     * 计算目录大小
     */
    private fun calculateDirectorySize(directory: File): Long {
        var size = 0L
        if (directory.exists()) {
            directory.listFiles()?.forEach { file ->
                size += if (file.isDirectory) {
                    calculateDirectorySize(file)
                } else {
                    file.length()
                }
            }
        }
        return size
    }
    
    /**
     * 保存模型信息
     */
    private fun saveModelInfo() {
        try {
            val modelInfo = """
                {
                    "version": "$CURRENT_MODEL_VERSION",
                    "checksum": "${calculateFileChecksum(modelFile)}",
                    "size": ${modelFile.length()},
                    "created_at": ${System.currentTimeMillis()},
                    "supported_languages": ["zh", "en", "ja", "ko"]
                }
            """.trimIndent()
            
            modelInfoFile.writeText(modelInfo)
            Timber.d("模型信息保存成功")
        } catch (e: Exception) {
            Timber.e(e, "保存模型信息失败")
        }
    }
}
