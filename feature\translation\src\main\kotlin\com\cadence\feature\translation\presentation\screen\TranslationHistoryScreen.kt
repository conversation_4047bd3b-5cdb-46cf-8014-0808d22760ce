package com.cadence.feature.translation.presentation.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cadence.domain.model.Translation
import com.cadence.feature.translation.presentation.viewmodel.TranslationHistoryViewModel
import java.text.SimpleDateFormat
import java.util.*

/**
 * 翻译历史界面
 * 显示翻译历史记录、搜索和管理功能
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TranslationHistoryScreen(
    onNavigateBack: () -> Unit,
    onTranslationClick: (Translation) -> Unit,
    modifier: Modifier = Modifier,
    viewModel: TranslationHistoryViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val searchQuery by viewModel.searchQuery.collectAsStateWithLifecycle()
    val translationHistory by viewModel.translationHistory.collectAsStateWithLifecycle()
    val favoriteTranslations by viewModel.favoriteTranslations.collectAsStateWithLifecycle()
    val supportedLanguages by viewModel.supportedLanguages.collectAsStateWithLifecycle()
    
    var showSearchBar by remember { mutableStateOf(false) }
    var showFilterDialog by remember { mutableStateOf(false) }
    var showDeleteDialog by remember { mutableStateOf(false) }
    
    Scaffold(
        topBar = {
            if (showSearchBar) {
                SearchTopBar(
                    searchQuery = searchQuery,
                    onSearchQueryChange = viewModel::updateSearchQuery,
                    onCloseSearch = {
                        showSearchBar = false
                        viewModel.clearSearchQuery()
                    }
                )
            } else {
                TranslationHistoryTopBar(
                    onNavigateBack = onNavigateBack,
                    onShowSearch = { showSearchBar = true },
                    onShowFilter = { showFilterDialog = true },
                    onClearHistory = { showDeleteDialog = true },
                    favoritesOnly = uiState.favoritesOnly,
                    onToggleFavoritesOnly = viewModel::toggleFavoritesOnly
                )
            }
        }
    ) { paddingValues ->
        Column(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 统计信息卡片
            uiState.statistics?.let { statistics ->
                StatisticsCard(
                    totalTranslations = statistics.totalTranslations,
                    favoriteCount = statistics.favoriteTranslations,
                    todayCount = statistics.translationsToday,
                    modifier = Modifier.padding(16.dp)
                )
            }
            
            // 翻译历史列表
            if (translationHistory.isEmpty()) {
                EmptyHistoryContent(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp)
                )
            } else {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(
                        items = translationHistory,
                        key = { it.id }
                    ) { translation ->
                        TranslationHistoryItem(
                            translation = translation,
                            onClick = { onTranslationClick(translation) },
                            onToggleFavorite = { isFavorite ->
                                viewModel.updateFavoriteStatus(translation.id, isFavorite)
                            },
                            onDelete = {
                                viewModel.deleteTranslation(translation.id)
                            }
                        )
                    }
                }
            }
        }
    }
    
    // 筛选对话框
    if (showFilterDialog) {
        FilterDialog(
            supportedLanguages = supportedLanguages,
            selectedLanguagePair = uiState.selectedLanguagePair,
            onLanguagePairSelected = { source, target ->
                viewModel.setLanguagePairFilter(source, target)
            },
            onClearFilter = viewModel::clearLanguagePairFilter,
            onDismiss = { showFilterDialog = false }
        )
    }
    
    // 删除确认对话框
    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteDialog = false },
            title = { Text("清空翻译历史") },
            text = { Text("确定要清空所有翻译历史记录吗？此操作无法撤销。") },
            confirmButton = {
                TextButton(
                    onClick = {
                        viewModel.clearTranslationHistory()
                        showDeleteDialog = false
                    }
                ) {
                    Text("确定")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteDialog = false }) {
                    Text("取消")
                }
            }
        )
    }
}

/**
 * 翻译历史顶部应用栏
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun TranslationHistoryTopBar(
    onNavigateBack: () -> Unit,
    onShowSearch: () -> Unit,
    onShowFilter: () -> Unit,
    onClearHistory: () -> Unit,
    favoritesOnly: Boolean,
    onToggleFavoritesOnly: () -> Unit,
    modifier: Modifier = Modifier
) {
    var showMenu by remember { mutableStateOf(false) }
    
    TopAppBar(
        title = { Text("翻译历史") },
        navigationIcon = {
            IconButton(onClick = onNavigateBack) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "返回"
                )
            }
        },
        actions = {
            IconButton(onClick = onShowSearch) {
                Icon(
                    imageVector = Icons.Default.Search,
                    contentDescription = "搜索"
                )
            }
            
            IconButton(onClick = onShowFilter) {
                Icon(
                    imageVector = Icons.Default.FilterList,
                    contentDescription = "筛选"
                )
            }
            
            IconButton(onClick = { showMenu = true }) {
                Icon(
                    imageVector = Icons.Default.MoreVert,
                    contentDescription = "更多选项"
                )
            }
            
            DropdownMenu(
                expanded = showMenu,
                onDismissRequest = { showMenu = false }
            ) {
                DropdownMenuItem(
                    text = { Text("仅显示收藏") },
                    onClick = {
                        onToggleFavoritesOnly()
                        showMenu = false
                    },
                    leadingIcon = {
                        Icon(
                            imageVector = if (favoritesOnly) Icons.Default.CheckBox else Icons.Default.CheckBoxOutlineBlank,
                            contentDescription = null
                        )
                    }
                )
                
                HorizontalDivider()
                
                DropdownMenuItem(
                    text = { Text("清空历史") },
                    onClick = {
                        onClearHistory()
                        showMenu = false
                    },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.DeleteSweep,
                            contentDescription = null
                        )
                    }
                )
            }
        },
        modifier = modifier
    )
}

/**
 * 搜索顶部应用栏
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SearchTopBar(
    searchQuery: String,
    onSearchQueryChange: (String) -> Unit,
    onCloseSearch: () -> Unit,
    modifier: Modifier = Modifier
) {
    TopAppBar(
        title = {
            OutlinedTextField(
                value = searchQuery,
                onValueChange = onSearchQueryChange,
                placeholder = { Text("搜索翻译历史...") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                trailingIcon = {
                    if (searchQuery.isNotEmpty()) {
                        IconButton(onClick = { onSearchQueryChange("") }) {
                            Icon(
                                imageVector = Icons.Default.Clear,
                                contentDescription = "清空搜索"
                            )
                        }
                    }
                }
            )
        },
        navigationIcon = {
            IconButton(onClick = onCloseSearch) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "关闭搜索"
                )
            }
        },
        modifier = modifier
    )
}

/**
 * 统计信息卡片
 */
@Composable
private fun StatisticsCard(
    totalTranslations: Int,
    favoriteCount: Int,
    todayCount: Int,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            StatisticItem(
                label = "总计",
                value = totalTranslations.toString()
            )
            
            StatisticItem(
                label = "收藏",
                value = favoriteCount.toString()
            )
            
            StatisticItem(
                label = "今日",
                value = todayCount.toString()
            )
        }
    }
}

/**
 * 统计项目
 */
@Composable
private fun StatisticItem(
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )
        Text(
            text = label,
            style = MaterialTheme.typography.labelMedium,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )
    }
}

/**
 * 空历史内容
 */
@Composable
private fun EmptyHistoryContent(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.History,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.outline
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "暂无翻译历史",
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Text(
            text = "开始翻译后，历史记录将显示在这里",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.outline
        )
    }
}

/**
 * 翻译历史项目
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun TranslationHistoryItem(
    translation: Translation,
    onClick: () -> Unit,
    onToggleFavorite: (Boolean) -> Unit,
    onDelete: () -> Unit,
    modifier: Modifier = Modifier
) {
    val dateFormatter = remember { SimpleDateFormat("MM/dd HH:mm", Locale.getDefault()) }

    Card(
        onClick = onClick,
        modifier = modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 语言对和时间
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "${translation.sourceLanguage.name} → ${translation.targetLanguage.name}",
                        style = MaterialTheme.typography.labelMedium,
                        color = MaterialTheme.colorScheme.primary
                    )

                    if (translation.isFavorite) {
                        Icon(
                            imageVector = Icons.Default.Favorite,
                            contentDescription = "收藏",
                            modifier = Modifier.size(16.dp),
                            tint = MaterialTheme.colorScheme.error
                        )
                    }
                }

                Text(
                    text = dateFormatter.format(Date(translation.createdAt)),
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.outline
                )
            }

            // 原文
            Text(
                text = translation.sourceText,
                style = MaterialTheme.typography.bodyMedium,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            // 译文
            Text(
                text = translation.translatedText,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )

            // 操作按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.End,
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(
                    onClick = { onToggleFavorite(!translation.isFavorite) }
                ) {
                    Icon(
                        imageVector = if (translation.isFavorite) Icons.Default.Favorite else Icons.Default.FavoriteBorder,
                        contentDescription = if (translation.isFavorite) "取消收藏" else "收藏",
                        tint = if (translation.isFavorite) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.outline
                    )
                }

                IconButton(onClick = onDelete) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "删除",
                        tint = MaterialTheme.colorScheme.outline
                    )
                }
            }
        }
    }
}

/**
 * 筛选对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun FilterDialog(
    supportedLanguages: List<com.cadence.domain.model.Language>,
    selectedLanguagePair: Pair<com.cadence.domain.model.Language, com.cadence.domain.model.Language>?,
    onLanguagePairSelected: (com.cadence.domain.model.Language?, com.cadence.domain.model.Language?) -> Unit,
    onClearFilter: () -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    var sourceLanguage by remember { mutableStateOf(selectedLanguagePair?.first) }
    var targetLanguage by remember { mutableStateOf(selectedLanguagePair?.second) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("筛选翻译历史") },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 源语言选择
                Text(
                    text = "源语言",
                    style = MaterialTheme.typography.labelMedium
                )

                var sourceExpanded by remember { mutableStateOf(false) }
                ExposedDropdownMenuBox(
                    expanded = sourceExpanded,
                    onExpandedChange = { sourceExpanded = !sourceExpanded }
                ) {
                    OutlinedTextField(
                        value = sourceLanguage?.name ?: "全部",
                        onValueChange = { },
                        readOnly = true,
                        modifier = Modifier
                            .fillMaxWidth()
                            .menuAnchor(),
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = sourceExpanded) }
                    )

                    ExposedDropdownMenu(
                        expanded = sourceExpanded,
                        onDismissRequest = { sourceExpanded = false }
                    ) {
                        DropdownMenuItem(
                            text = { Text("全部") },
                            onClick = {
                                sourceLanguage = null
                                sourceExpanded = false
                            }
                        )

                        supportedLanguages.forEach { language ->
                            DropdownMenuItem(
                                text = { Text(language.name) },
                                onClick = {
                                    sourceLanguage = language
                                    sourceExpanded = false
                                }
                            )
                        }
                    }
                }

                // 目标语言选择
                Text(
                    text = "目标语言",
                    style = MaterialTheme.typography.labelMedium
                )

                var targetExpanded by remember { mutableStateOf(false) }
                ExposedDropdownMenuBox(
                    expanded = targetExpanded,
                    onExpandedChange = { targetExpanded = !targetExpanded }
                ) {
                    OutlinedTextField(
                        value = targetLanguage?.name ?: "全部",
                        onValueChange = { },
                        readOnly = true,
                        modifier = Modifier
                            .fillMaxWidth()
                            .menuAnchor(),
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = targetExpanded) }
                    )

                    ExposedDropdownMenu(
                        expanded = targetExpanded,
                        onDismissRequest = { targetExpanded = false }
                    ) {
                        DropdownMenuItem(
                            text = { Text("全部") },
                            onClick = {
                                targetLanguage = null
                                targetExpanded = false
                            }
                        )

                        supportedLanguages.forEach { language ->
                            DropdownMenuItem(
                                text = { Text(language.name) },
                                onClick = {
                                    targetLanguage = language
                                    targetExpanded = false
                                }
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    onLanguagePairSelected(sourceLanguage, targetLanguage)
                    onDismiss()
                }
            ) {
                Text("应用")
            }
        },
        dismissButton = {
            TextButton(onClick = onClearFilter) {
                Text("清除筛选")
            }
        },
        modifier = modifier
    )
}
