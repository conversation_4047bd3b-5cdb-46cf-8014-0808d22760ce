package com.cadence.core.ocr

import android.graphics.Bitmap
import android.graphics.Rect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * OCR优化服务
 * 提供识别准确率优化和结果后处理功能
 */
@Singleton
class OcrOptimizationService @Inject constructor() {
    
    /**
     * 优化结果
     */
    sealed class OptimizationResult {
        data class Success(
            val optimizedText: String,
            val confidence: Float,
            val improvements: List<String>
        ) : OptimizationResult()
        data class Error(
            val message: String,
            val errorCode: Int
        ) : OptimizationResult()
    }
    
    /**
     * 优化配置
     */
    data class OptimizationConfig(
        val enableSpellCheck: Boolean = true,
        val enableContextCorrection: Boolean = true,
        val enableFormatCorrection: Boolean = true,
        val enableLanguageDetection: Boolean = true,
        val enableConfidenceFiltering: Boolean = true,
        val minConfidenceThreshold: Float = 0.7f,
        val targetLanguage: String = "zh-CN"
    )
    
    /**
     * 文字质量评估
     */
    data class TextQuality(
        val overallScore: Float,
        val readabilityScore: Float,
        val consistencyScore: Float,
        val completenessScore: Float,
        val issues: List<QualityIssue>
    )
    
    /**
     * 质量问题
     */
    data class QualityIssue(
        val type: IssueType,
        val description: String,
        val severity: Severity,
        val suggestion: String
    )
    
    /**
     * 问题类型
     */
    enum class IssueType {
        SPELLING_ERROR,
        FORMATTING_ERROR,
        INCOMPLETE_TEXT,
        LOW_CONFIDENCE,
        INCONSISTENT_LANGUAGE,
        SPECIAL_CHARACTERS,
        PUNCTUATION_ERROR
    }
    
    /**
     * 严重程度
     */
    enum class Severity {
        LOW, MEDIUM, HIGH, CRITICAL
    }
    
    // 错误代码常量
    companion object {
        const val ERROR_OPTIMIZATION_FAILED = 5001
        const val ERROR_INVALID_INPUT = 5002
        const val ERROR_LANGUAGE_NOT_SUPPORTED = 5003
        
        // 常见错误字符映射
        private val COMMON_OCR_ERRORS = mapOf(
            "0" to "O", "1" to "I", "5" to "S", "8" to "B",
            "rn" to "m", "cl" to "d", "vv" to "w", "ii" to "n"
        )
        
        // 中文常见错误
        private val CHINESE_OCR_ERRORS = mapOf(
            "人" to "入", "土" to "士", "未" to "末", "己" to "已"
        )
    }
    
    /**
     * 优化识别结果
     */
    suspend fun optimizeRecognitionResult(
        recognizedText: TextRecognitionService.RecognizedText,
        config: OptimizationConfig = OptimizationConfig()
    ): OptimizationResult = withContext(Dispatchers.Default) {
        try {
            val improvements = mutableListOf<String>()
            var optimizedText = recognizedText.fullText
            
            // 1. 置信度过滤
            if (config.enableConfidenceFiltering) {
                val filteredResult = filterLowConfidenceText(recognizedText, config.minConfidenceThreshold)
                optimizedText = filteredResult.first
                if (filteredResult.second) {
                    improvements.add("过滤了低置信度文字")
                }
            }
            
            // 2. 语言检测和校正
            if (config.enableLanguageDetection) {
                val detectedLanguage = detectLanguage(optimizedText)
                if (detectedLanguage != config.targetLanguage) {
                    improvements.add("检测到语言: $detectedLanguage")
                }
            }
            
            // 3. 拼写检查和校正
            if (config.enableSpellCheck) {
                val spellCorrected = correctSpelling(optimizedText, config.targetLanguage)
                if (spellCorrected != optimizedText) {
                    optimizedText = spellCorrected
                    improvements.add("修正了拼写错误")
                }
            }
            
            // 4. 上下文校正
            if (config.enableContextCorrection) {
                val contextCorrected = correctContext(optimizedText, config.targetLanguage)
                if (contextCorrected != optimizedText) {
                    optimizedText = contextCorrected
                    improvements.add("进行了上下文校正")
                }
            }
            
            // 5. 格式校正
            if (config.enableFormatCorrection) {
                val formatCorrected = correctFormat(optimizedText)
                if (formatCorrected != optimizedText) {
                    optimizedText = formatCorrected
                    improvements.add("修正了格式问题")
                }
            }
            
            // 6. 计算最终置信度
            val finalConfidence = calculateOptimizedConfidence(recognizedText.confidence, improvements.size)
            
            OptimizationResult.Success(optimizedText, finalConfidence, improvements)
            
        } catch (e: Exception) {
            OptimizationResult.Error("优化失败: ${e.message}", ERROR_OPTIMIZATION_FAILED)
        }
    }
    
    /**
     * 过滤低置信度文字
     */
    private fun filterLowConfidenceText(
        recognizedText: TextRecognitionService.RecognizedText,
        threshold: Float
    ): Pair<String, Boolean> {
        val filteredBlocks = recognizedText.textBlocks.filter { block ->
            block.confidence >= threshold
        }
        
        val filteredText = filteredBlocks.joinToString("\n") { it.text }
        val hasFiltered = filteredBlocks.size < recognizedText.textBlocks.size
        
        return Pair(filteredText, hasFiltered)
    }
    
    /**
     * 检测语言
     */
    private fun detectLanguage(text: String): String {
        return when {
            text.any { it in '\u4e00'..'\u9fff' } -> "zh-CN"
            text.any { it in '\u3040'..'\u309f' || it in '\u30a0'..'\u30ff' } -> "ja-JP"
            text.any { it in '\uac00'..'\ud7af' } -> "ko-KR"
            text.matches(Regex(".*[a-zA-Z].*")) -> "en-US"
            else -> "unknown"
        }
    }
    
    /**
     * 拼写校正
     */
    private fun correctSpelling(text: String, language: String): String {
        var correctedText = text
        
        when (language) {
            "zh-CN" -> {
                // 中文OCR常见错误校正
                CHINESE_OCR_ERRORS.forEach { (wrong, correct) ->
                    correctedText = correctedText.replace(wrong, correct)
                }
            }
            "en-US" -> {
                // 英文OCR常见错误校正
                COMMON_OCR_ERRORS.forEach { (wrong, correct) ->
                    correctedText = correctedText.replace(wrong, correct)
                }
            }
        }
        
        return correctedText
    }
    
    /**
     * 上下文校正
     */
    private fun correctContext(text: String, language: String): String {
        var correctedText = text
        
        when (language) {
            "zh-CN" -> {
                // 中文上下文校正
                correctedText = correctChineseContext(correctedText)
            }
            "en-US" -> {
                // 英文上下文校正
                correctedText = correctEnglishContext(correctedText)
            }
        }
        
        return correctedText
    }
    
    /**
     * 中文上下文校正
     */
    private fun correctChineseContext(text: String): String {
        var corrected = text
        
        // 常见词组校正
        val commonPhrases = mapOf(
            "人工智能" to "人工智能",
            "机器学习" to "机器学习",
            "深度学习" to "深度学习",
            "自然语言" to "自然语言"
        )
        
        commonPhrases.forEach { (pattern, replacement) ->
            // 模糊匹配和替换
            corrected = corrected.replace(Regex(pattern), replacement)
        }
        
        return corrected
    }
    
    /**
     * 英文上下文校正
     */
    private fun correctEnglishContext(text: String): String {
        var corrected = text
        
        // 常见单词校正
        val commonWords = mapOf(
            "teh" to "the",
            "adn" to "and",
            "taht" to "that",
            "wiht" to "with"
        )
        
        commonWords.forEach { (wrong, correct) ->
            corrected = corrected.replace(Regex("\\b$wrong\\b", RegexOption.IGNORE_CASE), correct)
        }
        
        return corrected
    }
    
    /**
     * 格式校正
     */
    private fun correctFormat(text: String): String {
        var corrected = text
        
        // 1. 修正空格问题
        corrected = corrected.replace(Regex("\\s+"), " ") // 多个空格合并为一个
        corrected = corrected.replace(Regex("\\s*\n\\s*"), "\n") // 清理换行符周围的空格
        
        // 2. 修正标点符号
        corrected = corrected.replace(Regex("\\s+([,.!?;:])"), "$1") // 标点符号前的空格
        corrected = corrected.replace(Regex("([,.!?;:])([a-zA-Z\u4e00-\u9fff])"), "$1 $2") // 标点符号后添加空格
        
        // 3. 修正引号
        corrected = corrected.replace(Regex("\\s*"\\s*"), """) // 左引号
        corrected = corrected.replace(Regex("\\s*"\\s*"), """) // 右引号
        
        // 4. 修正数字格式
        corrected = corrected.replace(Regex("(\\d)\\s+(\\d)"), "$1$2") // 数字中间的空格
        
        // 5. 首尾空格清理
        corrected = corrected.trim()
        
        return corrected
    }
    
    /**
     * 计算优化后的置信度
     */
    private fun calculateOptimizedConfidence(originalConfidence: Float, improvementCount: Int): Float {
        val improvementBonus = improvementCount * 0.05f
        return (originalConfidence + improvementBonus).coerceIn(0f, 1f)
    }
    
    /**
     * 评估文字质量
     */
    suspend fun assessTextQuality(
        text: String,
        recognizedText: TextRecognitionService.RecognizedText
    ): TextQuality = withContext(Dispatchers.Default) {
        val issues = mutableListOf<QualityIssue>()
        
        // 1. 可读性评估
        val readabilityScore = assessReadability(text, issues)
        
        // 2. 一致性评估
        val consistencyScore = assessConsistency(text, issues)
        
        // 3. 完整性评估
        val completenessScore = assessCompleteness(text, recognizedText, issues)
        
        // 4. 计算总体评分
        val overallScore = (readabilityScore + consistencyScore + completenessScore) / 3
        
        TextQuality(overallScore, readabilityScore, consistencyScore, completenessScore, issues)
    }
    
    /**
     * 评估可读性
     */
    private fun assessReadability(text: String, issues: MutableList<QualityIssue>): Float {
        var score = 1.0f
        
        // 检查特殊字符比例
        val specialCharCount = text.count { !it.isLetterOrDigit() && !it.isWhitespace() }
        val specialCharRatio = specialCharCount.toFloat() / text.length
        
        if (specialCharRatio > 0.3f) {
            score -= 0.3f
            issues.add(QualityIssue(
                IssueType.SPECIAL_CHARACTERS,
                "特殊字符过多",
                Severity.MEDIUM,
                "检查是否存在识别错误"
            ))
        }
        
        // 检查连续重复字符
        val repeatedChars = Regex("(.)\\1{3,}").findAll(text).count()
        if (repeatedChars > 0) {
            score -= 0.2f
            issues.add(QualityIssue(
                IssueType.FORMATTING_ERROR,
                "存在连续重复字符",
                Severity.LOW,
                "可能是识别错误导致"
            ))
        }
        
        return score.coerceIn(0f, 1f)
    }
    
    /**
     * 评估一致性
     */
    private fun assessConsistency(text: String, issues: MutableList<QualityIssue>): Float {
        var score = 1.0f
        
        // 检查语言一致性
        val chineseCharCount = text.count { it in '\u4e00'..'\u9fff' }
        val englishCharCount = text.count { it in 'a'..'z' || it in 'A'..'Z' }
        val totalCharCount = chineseCharCount + englishCharCount
        
        if (totalCharCount > 0) {
            val chineseRatio = chineseCharCount.toFloat() / totalCharCount
            val englishRatio = englishCharCount.toFloat() / totalCharCount
            
            if (chineseRatio > 0.1f && englishRatio > 0.1f && chineseRatio < 0.9f && englishRatio < 0.9f) {
                issues.add(QualityIssue(
                    IssueType.INCONSISTENT_LANGUAGE,
                    "混合语言文本",
                    Severity.LOW,
                    "确认是否为多语言文档"
                ))
            }
        }
        
        return score.coerceIn(0f, 1f)
    }
    
    /**
     * 评估完整性
     */
    private fun assessCompleteness(
        text: String,
        recognizedText: TextRecognitionService.RecognizedText,
        issues: MutableList<QualityIssue>
    ): Float {
        var score = 1.0f
        
        // 检查置信度
        if (recognizedText.confidence < 0.8f) {
            score -= 0.3f
            issues.add(QualityIssue(
                IssueType.LOW_CONFIDENCE,
                "识别置信度较低: ${String.format("%.2f", recognizedText.confidence)}",
                Severity.HIGH,
                "建议重新拍照或调整图片质量"
            ))
        }
        
        // 检查文本长度
        if (text.length < 10) {
            score -= 0.2f
            issues.add(QualityIssue(
                IssueType.INCOMPLETE_TEXT,
                "识别文本过短",
                Severity.MEDIUM,
                "可能存在识别不完整"
            ))
        }
        
        return score.coerceIn(0f, 1f)
    }
    
    /**
     * 获取优化建议
     */
    fun getOptimizationSuggestions(quality: TextQuality): List<String> {
        val suggestions = mutableListOf<String>()
        
        if (quality.overallScore < 0.7f) {
            suggestions.add("整体识别质量较低，建议重新拍照")
        }
        
        if (quality.readabilityScore < 0.8f) {
            suggestions.add("提高图片清晰度，确保文字清楚可见")
        }
        
        if (quality.completenessScore < 0.8f) {
            suggestions.add("确保图片包含完整的文字内容")
        }
        
        quality.issues.forEach { issue ->
            when (issue.severity) {
                Severity.CRITICAL, Severity.HIGH -> suggestions.add(issue.suggestion)
                else -> { /* 低优先级建议可以忽略 */ }
            }
        }
        
        return suggestions
    }
}
