package com.cadence.data.mapper

import com.cadence.core.database.entity.UserPreferenceEntity
import com.cadence.domain.model.*

/**
 * 用户偏好数据映射器
 * 负责用户偏好数据库实体与领域模型之间的转换
 */
object UserPreferenceMapper {
    
    /**
     * 将数据库实体转换为用户偏好领域模型
     */
    fun UserPreferenceEntity.toDomain(): UserPreference {
        return UserPreference(
            id = userId,
            defaultSourceLanguage = Language(
                code = defaultSourceLanguageCode,
                name = defaultSourceLanguageName,
                region = defaultSourceRegionCode?.let { regionCode ->
                    Region(
                        code = regionCode,
                        name = defaultSourceRegionName ?: "",
                        dialectName = defaultSourceDialectName,
                        culturalInfo = null
                    )
                }
            ),
            defaultTargetLanguage = Language(
                code = defaultTargetLanguageCode,
                name = defaultTargetLanguageName,
                region = defaultTargetRegionCode?.let { regionCode ->
                    Region(
                        code = regionCode,
                        name = defaultTargetRegionName ?: "",
                        dialectName = defaultTargetDialectName,
                        culturalInfo = null
                    )
                }
            ),
            autoDetectLanguage = autoDetectLanguage,
            saveTranslationHistory = saveTranslationHistory,
            enableCulturalContext = enableCulturalContext,
            themeMode = ThemeMode.valueOf(themeMode),
            fontSize = FontSize.valueOf(fontSize),
            enableVoiceInput = enableVoiceInput,
            enableVoiceOutput = enableVoiceOutput,
            enableOcr = enableOcr,
            createdAt = createdAt,
            updatedAt = updatedAt
        )
    }
    
    /**
     * 将用户偏好领域模型转换为数据库实体
     */
    fun UserPreference.toEntity(): UserPreferenceEntity {
        return UserPreferenceEntity(
            userId = id,
            defaultSourceLanguageCode = defaultSourceLanguage.code,
            defaultSourceLanguageName = defaultSourceLanguage.name,
            defaultSourceRegionCode = defaultSourceLanguage.region?.code,
            defaultSourceRegionName = defaultSourceLanguage.region?.name,
            defaultSourceDialectName = defaultSourceLanguage.region?.dialectName,
            defaultTargetLanguageCode = defaultTargetLanguage.code,
            defaultTargetLanguageName = defaultTargetLanguage.name,
            defaultTargetRegionCode = defaultTargetLanguage.region?.code,
            defaultTargetRegionName = defaultTargetLanguage.region?.name,
            defaultTargetDialectName = defaultTargetLanguage.region?.dialectName,
            autoDetectLanguage = autoDetectLanguage,
            saveTranslationHistory = saveTranslationHistory,
            enableCulturalContext = enableCulturalContext,
            themeMode = themeMode.name,
            fontSize = fontSize.name,
            enableVoiceInput = enableVoiceInput,
            enableVoiceOutput = enableVoiceOutput,
            enableOcr = enableOcr,
            createdAt = createdAt,
            updatedAt = updatedAt
        )
    }
    
    /**
     * 创建默认用户偏好
     */
    fun createDefaultUserPreference(userId: String = "default_user"): UserPreference {
        val currentTime = System.currentTimeMillis()
        
        return UserPreference(
            id = userId,
            defaultSourceLanguage = Language(
                code = "zh",
                name = "中文",
                region = Region(
                    code = "CN",
                    name = "大陆",
                    dialectName = "普通话"
                )
            ),
            defaultTargetLanguage = Language(
                code = "en",
                name = "English",
                region = Region(
                    code = "US",
                    name = "美国",
                    dialectName = "美式英语"
                )
            ),
            autoDetectLanguage = true,
            saveTranslationHistory = true,
            enableCulturalContext = true,
            themeMode = ThemeMode.SYSTEM,
            fontSize = FontSize.MEDIUM,
            enableVoiceInput = true,
            enableVoiceOutput = true,
            enableOcr = true,
            createdAt = currentTime,
            updatedAt = currentTime
        )
    }
    
    /**
     * 创建默认用户偏好实体
     */
    fun createDefaultUserPreferenceEntity(userId: String = "default_user"): UserPreferenceEntity {
        return createDefaultUserPreference(userId).toEntity()
    }
    
    /**
     * 更新用户偏好的时间戳
     */
    fun UserPreference.updateTimestamp(): UserPreference {
        return copy(updatedAt = System.currentTimeMillis())
    }
    
    /**
     * 更新用户偏好实体的时间戳
     */
    fun UserPreferenceEntity.updateTimestamp(): UserPreferenceEntity {
        return copy(updatedAt = System.currentTimeMillis())
    }
}
