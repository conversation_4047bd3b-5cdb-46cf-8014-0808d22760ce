package com.cadence.core.ocr

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import androidx.core.content.ContextCompat
import javax.inject.Inject
import javax.inject.Singleton

/**
 * OCR权限管理器
 * 管理OCR功能所需的各种权限
 */
@Singleton
class OcrPermissionManager @Inject constructor(
    private val context: Context
) {
    
    /**
     * OCR功能所需的权限
     */
    companion object {
        val REQUIRED_PERMISSIONS = arrayOf(
            Manifest.permission.CAMERA,
            Manifest.permission.READ_EXTERNAL_STORAGE
        )
        
        // 权限说明文本
        const val CAMERA_PERMISSION_RATIONALE = "需要相机权限来拍照识别文字"
        const val STORAGE_PERMISSION_RATIONALE = "需要存储权限来选择和保存图片"
    }
    
    /**
     * 权限状态
     */
    enum class PermissionStatus {
        GRANTED,        // 已授权
        DENIED,         // 被拒绝
        NEVER_ASK_AGAIN // 不再询问
    }
    
    /**
     * 权限检查结果
     */
    data class PermissionCheckResult(
        val allGranted: Boolean,
        val grantedPermissions: List<String>,
        val deniedPermissions: List<String>,
        val missingPermissions: List<String>
    )
    
    /**
     * 检查相机权限
     */
    fun hasCameraPermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.CAMERA
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 检查存储权限
     */
    fun hasStoragePermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.READ_EXTERNAL_STORAGE
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 检查所有必需权限
     */
    fun checkAllPermissions(): PermissionCheckResult {
        val grantedPermissions = mutableListOf<String>()
        val deniedPermissions = mutableListOf<String>()
        
        REQUIRED_PERMISSIONS.forEach { permission ->
            if (ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED) {
                grantedPermissions.add(permission)
            } else {
                deniedPermissions.add(permission)
            }
        }
        
        return PermissionCheckResult(
            allGranted = deniedPermissions.isEmpty(),
            grantedPermissions = grantedPermissions,
            deniedPermissions = deniedPermissions,
            missingPermissions = deniedPermissions
        )
    }
    
    /**
     * 获取权限状态
     */
    fun getPermissionStatus(permission: String): PermissionStatus {
        return when (ContextCompat.checkSelfPermission(context, permission)) {
            PackageManager.PERMISSION_GRANTED -> PermissionStatus.GRANTED
            else -> PermissionStatus.DENIED
        }
    }
    
    /**
     * 获取权限说明文本
     */
    fun getPermissionRationale(permission: String): String {
        return when (permission) {
            Manifest.permission.CAMERA -> CAMERA_PERMISSION_RATIONALE
            Manifest.permission.READ_EXTERNAL_STORAGE -> STORAGE_PERMISSION_RATIONALE
            else -> "此功能需要相应权限才能正常工作"
        }
    }
    
    /**
     * 获取所有缺失权限的说明
     */
    fun getMissingPermissionsRationale(): List<Pair<String, String>> {
        val result = mutableListOf<Pair<String, String>>()
        
        REQUIRED_PERMISSIONS.forEach { permission ->
            if (getPermissionStatus(permission) != PermissionStatus.GRANTED) {
                result.add(Pair(permission, getPermissionRationale(permission)))
            }
        }
        
        return result
    }
    
    /**
     * 检查是否可以使用OCR功能
     */
    fun canUseOcrFeatures(): Boolean {
        return checkAllPermissions().allGranted
    }
    
    /**
     * 检查是否可以使用相机功能
     */
    fun canUseCamera(): Boolean {
        return hasCameraPermission()
    }
    
    /**
     * 检查是否可以访问图片
     */
    fun canAccessImages(): Boolean {
        return hasStoragePermission()
    }
    
    /**
     * 获取权限友好名称
     */
    fun getPermissionFriendlyName(permission: String): String {
        return when (permission) {
            Manifest.permission.CAMERA -> "相机"
            Manifest.permission.READ_EXTERNAL_STORAGE -> "存储"
            else -> "未知权限"
        }
    }
}
