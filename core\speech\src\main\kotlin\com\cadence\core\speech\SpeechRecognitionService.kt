package com.cadence.core.speech

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.speech.RecognitionListener
import android.speech.RecognizerIntent
import android.speech.SpeechRecognizer
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 语音识别服务
 * 提供语音转文字功能，支持多语言识别
 */
@Singleton
class SpeechRecognitionService @Inject constructor(
    private val context: Context
) {
    
    /**
     * 语音识别结果
     */
    sealed class SpeechResult {
        data class Success(val text: String, val confidence: Float) : SpeechResult()
        data class Error(val errorCode: Int, val message: String) : SpeechResult()
        object Listening : SpeechResult()
        object Ready : SpeechResult()
        data class PartialResult(val text: String) : SpeechResult()
    }
    
    /**
     * 语音识别配置
     */
    data class SpeechConfig(
        val language: String = "zh-CN",
        val maxResults: Int = 5,
        val partialResults: Boolean = true,
        val offlineOnly: Boolean = false,
        val preferOffline: Boolean = false
    )
    
    /**
     * 检查语音识别是否可用
     */
    fun isAvailable(): Boolean {
        return SpeechRecognizer.isRecognitionAvailable(context)
    }
    
    /**
     * 获取支持的语言列表
     */
    fun getSupportedLanguages(): List<String> {
        return listOf(
            "zh-CN", // 中文（简体）
            "zh-TW", // 中文（繁体）
            "en-US", // 英语（美国）
            "en-GB", // 英语（英国）
            "ja-JP", // 日语
            "ko-KR", // 韩语
            "fr-FR", // 法语
            "de-DE", // 德语
            "es-ES", // 西班牙语
            "it-IT", // 意大利语
            "pt-BR", // 葡萄牙语（巴西）
            "ru-RU", // 俄语
            "ar-SA", // 阿拉伯语
            "hi-IN", // 印地语
            "th-TH", // 泰语
            "vi-VN", // 越南语
            "id-ID", // 印尼语
            "ms-MY", // 马来语
            "tl-PH", // 菲律宾语
            "tr-TR", // 土耳其语
            "pl-PL", // 波兰语
            "nl-NL", // 荷兰语
            "sv-SE", // 瑞典语
            "da-DK", // 丹麦语
            "no-NO"  // 挪威语
        )
    }
    
    /**
     * 开始语音识别
     * @param config 识别配置
     * @return 识别结果流
     */
    fun startRecognition(config: SpeechConfig = SpeechConfig()): Flow<SpeechResult> = callbackFlow {
        if (!isAvailable()) {
            trySend(SpeechResult.Error(-1, "语音识别不可用"))
            close()
            return@callbackFlow
        }
        
        val speechRecognizer = SpeechRecognizer.createSpeechRecognizer(context)
        val intent = createRecognitionIntent(config)
        
        val listener = object : RecognitionListener {
            override fun onReadyForSpeech(params: Bundle?) {
                trySend(SpeechResult.Ready)
            }
            
            override fun onBeginningOfSpeech() {
                trySend(SpeechResult.Listening)
            }
            
            override fun onRmsChanged(rmsdB: Float) {
                // 音量变化，可用于UI反馈
            }
            
            override fun onBufferReceived(buffer: ByteArray?) {
                // 音频缓冲区数据
            }
            
            override fun onEndOfSpeech() {
                // 语音结束
            }
            
            override fun onError(error: Int) {
                val errorMessage = getErrorMessage(error)
                trySend(SpeechResult.Error(error, errorMessage))
                close()
            }
            
            override fun onResults(results: Bundle?) {
                results?.let { bundle ->
                    val matches = bundle.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                    val confidences = bundle.getFloatArray(SpeechRecognizer.CONFIDENCE_SCORES)
                    
                    if (!matches.isNullOrEmpty()) {
                        val bestMatch = matches[0]
                        val confidence = confidences?.get(0) ?: 0.0f
                        trySend(SpeechResult.Success(bestMatch, confidence))
                    } else {
                        trySend(SpeechResult.Error(-1, "未识别到语音内容"))
                    }
                }
                close()
            }
            
            override fun onPartialResults(partialResults: Bundle?) {
                if (config.partialResults) {
                    partialResults?.let { bundle ->
                        val matches = bundle.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                        if (!matches.isNullOrEmpty()) {
                            trySend(SpeechResult.PartialResult(matches[0]))
                        }
                    }
                }
            }
            
            override fun onEvent(eventType: Int, params: Bundle?) {
                // 其他事件
            }
        }
        
        speechRecognizer.setRecognitionListener(listener)
        speechRecognizer.startListening(intent)
        
        awaitClose {
            speechRecognizer.stopListening()
            speechRecognizer.destroy()
        }
    }
    
    /**
     * 创建语音识别Intent
     */
    private fun createRecognitionIntent(config: SpeechConfig): Intent {
        return Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
            putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
            putExtra(RecognizerIntent.EXTRA_LANGUAGE, config.language)
            putExtra(RecognizerIntent.EXTRA_LANGUAGE_PREFERENCE, config.language)
            putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, config.maxResults)
            putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, config.partialResults)
            putExtra(RecognizerIntent.EXTRA_CALLING_PACKAGE, context.packageName)
            
            if (config.offlineOnly) {
                putExtra(RecognizerIntent.EXTRA_PREFER_OFFLINE, true)
            } else if (config.preferOffline) {
                putExtra(RecognizerIntent.EXTRA_PREFER_OFFLINE, true)
            }
        }
    }
    
    /**
     * 获取错误信息
     */
    private fun getErrorMessage(errorCode: Int): String {
        return when (errorCode) {
            SpeechRecognizer.ERROR_AUDIO -> "音频录制错误"
            SpeechRecognizer.ERROR_CLIENT -> "客户端错误"
            SpeechRecognizer.ERROR_INSUFFICIENT_PERMISSIONS -> "权限不足"
            SpeechRecognizer.ERROR_NETWORK -> "网络错误"
            SpeechRecognizer.ERROR_NETWORK_TIMEOUT -> "网络超时"
            SpeechRecognizer.ERROR_NO_MATCH -> "未找到匹配结果"
            SpeechRecognizer.ERROR_RECOGNIZER_BUSY -> "识别器忙碌"
            SpeechRecognizer.ERROR_SERVER -> "服务器错误"
            SpeechRecognizer.ERROR_SPEECH_TIMEOUT -> "语音超时"
            else -> "未知错误 ($errorCode)"
        }
    }
    
    /**
     * 获取语言显示名称
     */
    fun getLanguageDisplayName(languageCode: String): String {
        return when (languageCode) {
            "zh-CN" -> "中文（简体）"
            "zh-TW" -> "中文（繁体）"
            "en-US" -> "English (US)"
            "en-GB" -> "English (UK)"
            "ja-JP" -> "日本語"
            "ko-KR" -> "한국어"
            "fr-FR" -> "Français"
            "de-DE" -> "Deutsch"
            "es-ES" -> "Español"
            "it-IT" -> "Italiano"
            "pt-BR" -> "Português (Brasil)"
            "ru-RU" -> "Русский"
            "ar-SA" -> "العربية"
            "hi-IN" -> "हिन्दी"
            "th-TH" -> "ไทย"
            "vi-VN" -> "Tiếng Việt"
            "id-ID" -> "Bahasa Indonesia"
            "ms-MY" -> "Bahasa Melayu"
            "tl-PH" -> "Filipino"
            "tr-TR" -> "Türkçe"
            "pl-PL" -> "Polski"
            "nl-NL" -> "Nederlands"
            "sv-SE" -> "Svenska"
            "da-DK" -> "Dansk"
            "no-NO" -> "Norsk"
            else -> languageCode
        }
    }
    
    /**
     * 根据翻译语言代码获取语音识别语言代码
     */
    fun mapTranslationLanguageToSpeech(translationLanguage: String): String {
        return when (translationLanguage.lowercase()) {
            "chinese", "zh", "中文" -> "zh-CN"
            "english", "en", "英文" -> "en-US"
            "japanese", "ja", "日文" -> "ja-JP"
            "korean", "ko", "韩文" -> "ko-KR"
            "french", "fr", "法文" -> "fr-FR"
            "german", "de", "德文" -> "de-DE"
            "spanish", "es", "西班牙文" -> "es-ES"
            "italian", "it", "意大利文" -> "it-IT"
            "portuguese", "pt", "葡萄牙文" -> "pt-BR"
            "russian", "ru", "俄文" -> "ru-RU"
            "arabic", "ar", "阿拉伯文" -> "ar-SA"
            "hindi", "hi", "印地文" -> "hi-IN"
            "thai", "th", "泰文" -> "th-TH"
            "vietnamese", "vi", "越南文" -> "vi-VN"
            "indonesian", "id", "印尼文" -> "id-ID"
            "malay", "ms", "马来文" -> "ms-MY"
            "filipino", "tl", "菲律宾文" -> "tl-PH"
            "turkish", "tr", "土耳其文" -> "tr-TR"
            "polish", "pl", "波兰文" -> "pl-PL"
            "dutch", "nl", "荷兰文" -> "nl-NL"
            "swedish", "sv", "瑞典文" -> "sv-SE"
            "danish", "da", "丹麦文" -> "da-DK"
            "norwegian", "no", "挪威文" -> "no-NO"
            else -> "zh-CN" // 默认中文
        }
    }
}
