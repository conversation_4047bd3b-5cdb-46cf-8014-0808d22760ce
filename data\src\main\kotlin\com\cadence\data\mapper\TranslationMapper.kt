package com.cadence.data.mapper

import com.cadence.core.database.entity.TranslationEntity
import com.cadence.domain.model.*

/**
 * 翻译数据映射器
 * 负责数据库实体与领域模型之间的转换
 */
object TranslationMapper {
    
    /**
     * 将数据库实体转换为领域模型
     */
    fun TranslationEntity.toDomain(): Translation {
        return Translation(
            id = id,
            sourceText = sourceText,
            translatedText = translatedText,
            sourceLanguage = Language(
                code = sourceLanguageCode,
                name = sourceLanguageName,
                region = sourceRegionCode?.let { regionCode ->
                    Region(
                        code = regionCode,
                        name = sourceRegionName ?: "",
                        dialectName = sourceDialectName,
                        culturalInfo = null
                    )
                }
            ),
            targetLanguage = Language(
                code = targetLanguageCode,
                name = targetLanguageName,
                region = targetRegionCode?.let { regionCode ->
                    Region(
                        code = regionCode,
                        name = targetRegionName ?: "",
                        dialectName = targetDialectName,
                        culturalInfo = null
                    )
                }
            ),
            confidenceScore = confidenceScore,
            isFavorite = isFavorite,
            createdAt = createdAt,
            updatedAt = updatedAt,
            translationType = TranslationType.valueOf(translationType),
            culturalContext = culturalContext
        )
    }
    
    /**
     * 将领域模型转换为数据库实体
     */
    fun Translation.toEntity(): TranslationEntity {
        return TranslationEntity(
            id = id,
            sourceText = sourceText,
            translatedText = translatedText,
            sourceLanguageCode = sourceLanguage.code,
            sourceLanguageName = sourceLanguage.name,
            sourceRegionCode = sourceLanguage.region?.code,
            sourceRegionName = sourceLanguage.region?.name,
            sourceDialectName = sourceLanguage.region?.dialectName,
            targetLanguageCode = targetLanguage.code,
            targetLanguageName = targetLanguage.name,
            targetRegionCode = targetLanguage.region?.code,
            targetRegionName = targetLanguage.region?.name,
            targetDialectName = targetLanguage.region?.dialectName,
            confidenceScore = confidenceScore,
            isFavorite = isFavorite,
            createdAt = createdAt,
            updatedAt = updatedAt,
            translationType = translationType.name,
            culturalContext = culturalContext
        )
    }
    
    /**
     * 批量转换数据库实体列表为领域模型列表
     */
    fun List<TranslationEntity>.toDomainList(): List<Translation> {
        return map { it.toDomain() }
    }
    
    /**
     * 批量转换领域模型列表为数据库实体列表
     */
    fun List<Translation>.toEntityList(): List<TranslationEntity> {
        return map { it.toEntity() }
    }
    
    /**
     * 创建翻译统计信息
     */
    fun createTranslationStatistics(
        totalTranslations: Int,
        favoriteCount: Int,
        languagePairUsages: List<LanguagePairUsage>,
        translationsByType: Map<String, Int>,
        averageConfidenceScore: Float
    ): TranslationStatistics {
        return TranslationStatistics(
            totalTranslations = totalTranslations,
            favoriteCount = favoriteCount,
            mostUsedLanguagePairs = languagePairUsages,
            translationsByType = translationsByType.mapKeys { TranslationType.valueOf(it.key) },
            averageConfidenceScore = averageConfidenceScore
        )
    }
}
