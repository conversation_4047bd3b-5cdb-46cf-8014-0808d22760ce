package com.cadence.domain.usecase

import com.cadence.domain.model.*
import com.cadence.domain.repository.TranslationRepository
import com.cadence.domain.repository.UserPreferenceRepository
import kotlinx.coroutines.flow.first
import timber.log.Timber
import java.util.*
import javax.inject.Inject

/**
 * 翻译文本用例
 * 处理文本翻译的核心业务逻辑
 */
class TranslateTextUseCase @Inject constructor(
    private val translationRepository: TranslationRepository,
    private val userPreferenceRepository: UserPreferenceRepository
) {
    
    /**
     * 执行翻译
     * @param sourceText 源文本
     * @param sourceLanguage 源语言（可选，为null时自动检测）
     * @param targetLanguage 目标语言（可选，使用用户默认设置）
     * @param includeCulturalContext 是否包含文化背景
     * @param saveToHistory 是否保存到历史记录
     * @return 翻译结果
     */
    suspend operator fun invoke(
        sourceText: String,
        sourceLanguage: Language? = null,
        targetLanguage: Language? = null,
        includeCulturalContext: Boolean? = null,
        saveToHistory: Boolean? = null
    ): Result<TranslationResult> {
        return try {
            // 验证输入
            if (sourceText.isBlank()) {
                return Result.failure(IllegalArgumentException("源文本不能为空"))
            }
            
            // 获取用户偏好设置
            val userPreference = userPreferenceRepository.getUserPreference().first()
            
            // 确定源语言
            val finalSourceLanguage = sourceLanguage ?: run {
                if (userPreference?.autoDetectLanguage == true) {
                    // 自动检测语言
                    val detectionResult = translationRepository.detectLanguage(sourceText)
                    if (detectionResult.isSuccess) {
                        detectionResult.getOrNull()?.detectedLanguage
                    } else {
                        userPreference.defaultSourceLanguage
                    }
                } else {
                    userPreference?.defaultSourceLanguage
                }
            } ?: return Result.failure(IllegalStateException("无法确定源语言"))
            
            // 确定目标语言
            val finalTargetLanguage = targetLanguage 
                ?: userPreference?.defaultTargetLanguage
                ?: return Result.failure(IllegalStateException("无法确定目标语言"))
            
            // 检查是否为相同语言
            if (finalSourceLanguage.code == finalTargetLanguage.code && 
                finalSourceLanguage.region?.code == finalTargetLanguage.region?.code) {
                return Result.failure(IllegalArgumentException("源语言和目标语言不能相同"))
            }
            
            // 确定其他参数
            val finalIncludeCulturalContext = includeCulturalContext 
                ?: userPreference?.enableCulturalContext 
                ?: false
            val finalSaveToHistory = saveToHistory 
                ?: userPreference?.saveTranslationHistory 
                ?: true
            
            // 创建翻译请求
            val request = TranslationRequest(
                sourceText = sourceText.trim(),
                sourceLanguage = finalSourceLanguage,
                targetLanguage = finalTargetLanguage,
                translationType = TranslationType.TEXT,
                includeCulturalContext = finalIncludeCulturalContext,
                saveToHistory = finalSaveToHistory
            )
            
            // 执行翻译
            val translationResult = translationRepository.translate(request)
            
            if (translationResult.isSuccess) {
                val result = translationResult.getOrThrow()
                
                // 如果需要文化背景解释且翻译成功
                if (finalIncludeCulturalContext && result.translation.culturalContext.isNullOrBlank()) {
                    val contextResult = translationRepository.getCulturalContext(result.translation)
                    if (contextResult.isSuccess) {
                        val updatedTranslation = result.translation.copy(
                            culturalContext = contextResult.getOrNull()
                        )
                        val updatedResult = result.copy(translation = updatedTranslation)
                        
                        // 更新保存的翻译记录
                        if (finalSaveToHistory) {
                            translationRepository.updateTranslation(updatedTranslation)
                        }
                        
                        return Result.success(updatedResult)
                    }
                }
                
                Timber.d("翻译完成: ${sourceText} -> ${result.translation.translatedText}")
                Result.success(result)
            } else {
                Timber.e("翻译失败: ${translationResult.exceptionOrNull()?.message}")
                translationResult
            }
            
        } catch (e: Exception) {
            Timber.e(e, "翻译过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 批量翻译
     * @param texts 文本列表
     * @param sourceLanguage 源语言
     * @param targetLanguage 目标语言
     * @param includeCulturalContext 是否包含文化背景
     * @param saveToHistory 是否保存到历史记录
     * @return 翻译结果列表
     */
    suspend fun translateBatch(
        texts: List<String>,
        sourceLanguage: Language? = null,
        targetLanguage: Language? = null,
        includeCulturalContext: Boolean? = null,
        saveToHistory: Boolean? = null
    ): Result<List<TranslationResult>> {
        return try {
            if (texts.isEmpty()) {
                return Result.success(emptyList())
            }
            
            val results = mutableListOf<TranslationResult>()
            
            for (text in texts) {
                val result = invoke(
                    sourceText = text,
                    sourceLanguage = sourceLanguage,
                    targetLanguage = targetLanguage,
                    includeCulturalContext = includeCulturalContext,
                    saveToHistory = saveToHistory
                )
                
                if (result.isSuccess) {
                    results.add(result.getOrThrow())
                } else {
                    // 批量翻译中的单个失败不应该中断整个过程
                    Timber.w("批量翻译中的单个文本翻译失败: $text")
                }
            }
            
            Result.success(results)
        } catch (e: Exception) {
            Timber.e(e, "批量翻译过程中发生异常")
            Result.failure(e)
        }
    }
}
